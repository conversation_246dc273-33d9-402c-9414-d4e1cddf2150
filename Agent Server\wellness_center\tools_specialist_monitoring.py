# Clinical Dashboard Insights Tool 

def generate_clinical_dashboard_insight(user_health_data: dict, include_metrics: bool = True) -> dict:
    import datetime
    from collections import defaultdict
    from statistics import mean

    now = datetime.datetime.now()
    metric_values = defaultdict(list)
    patients_flagged = {}
    summary_alerts = []

    for patient_id, records in user_health_data.items():
        latest = sorted(records, key=lambda x: x.get("timestamp", ""))[-1]
        patient_flags = []

        # Extract and evaluate vitals
        temp = latest.get("Temperature")
        if temp is not None:
            metric_values["Temperature"].append(temp)
            if temp > 38:
                patient_flags.append("🔥 Fever detected (> 38°C)")

        spo2 = latest.get("SpO2")
        if spo2 is not None:
            metric_values["SpO2"].append(spo2)
            if spo2 < 92:
                patient_flags.append("🫁 Oxygen level low (SpO₂ < 92%). Check for respiratory issues.")

        glucose = latest.get("Glucose")
        if glucose is not None:
            metric_values["Glucose"].append(glucose)
            if glucose > 110:
                patient_flags.append("🍬 High glucose (hyperglycemia). Monitor for diabetes risk.")

        heart = latest.get("ECG (Heart Rate)")
        if heart is not None:
            metric_values["ECG (Heart Rate)"].append(heart)
            if heart < 60 or heart > 100:
                patient_flags.append("❤️ Abnormal heart rate detected (outside 60–100 bpm).")

        sys = latest.get("Blood Pressure (Systolic)")
        if sys is not None:
            metric_values["Blood Pressure (Systolic)"].append(sys)
            if sys > 140:
                patient_flags.append("🩸 Systolic hypertension (SBP > 140 mmHg)")

        dia = latest.get("Blood Pressure (Diastolic)")
        if dia is not None:
            metric_values["Blood Pressure (Diastolic)"].append(dia)
            if dia > 90:
                patient_flags.append("🩸 Diastolic hypertension (DBP > 90 mmHg)")

        bmi = latest.get("Weight (BMI)")
        if bmi is not None:
            metric_values["Weight (BMI)"].append(bmi)
            if bmi >= 30:
                patient_flags.append("⚖️ Obesity risk (BMI ≥ 30). Recommend weight management intervention.")

        waist = latest.get("Waist Circumference")
        if waist is not None:
            metric_values["Waist Circumference"].append(waist)
            if waist >= 102:
                patient_flags.append("📏 High waist circumference (> 102 cm). Elevated cardiovascular risk.")

        malaria = latest.get("Malaria")
        if malaria == "Positive":
            patient_flags.append("🦟 Malaria test positive. Urgent treatment advised.")

        hep_b = latest.get("Hepatitis B")
        if hep_b == "Positive":
            patient_flags.append("🧬 Hepatitis B detected. Monitor liver function and counsel patient.")

        hiv = latest.get("HIV")
        if hiv == "Positive":
            patient_flags.append("🧪 HIV positive. Ensure patient is linked to care and treatment.")

        lung = latest.get("Lung Capacity")
        if lung is not None:
            metric_values["Lung Capacity"].append(lung)
            if lung < 2.5:
                patient_flags.append("🌬️ Low lung capacity (<2.5L). Check for COPD or asthma risk.")

        widal = latest.get("Widal Test")
        if isinstance(widal, dict):
            reactive_count = sum(1 for v in widal.values() if v.lower() == "reactive")
            if reactive_count > 0:
                patient_flags.append("🧫 Widal test reactive. Possible typhoid/paratyphoid infection.")

        if patient_flags:
            patients_flagged[patient_id] = patient_flags

    # Summary alerts
    def count_flags(keyword):
        return sum(1 for flags in patients_flagged.values() if any(keyword in f for f in flags))

    if count_flags("Fever"):
        summary_alerts.append(f"🔥 {count_flags('Fever')} patients have high fever (Temp > 38°C). Fever may indicate infection.")
    if count_flags("Oxygen"):
        summary_alerts.append(f"🫁 {count_flags('Oxygen')} patients have dangerously low oxygen levels (SpO₂ < 92%).")
    if count_flags("glucose"):
        summary_alerts.append(f"🍬 {count_flags('glucose')} patients show high glucose levels. Watch for hyperglycemia.")
    if count_flags("Systolic hypertension"):
        summary_alerts.append(f"🩸 {count_flags('Systolic hypertension')} patients have systolic hypertension (> 140 mmHg).")
    if count_flags("Diastolic hypertension"):
        summary_alerts.append(f"🩸 {count_flags('Diastolic hypertension')} patients have diastolic hypertension (> 90 mmHg).")
    if count_flags("heart rate"):
        summary_alerts.append(f"❤️ {count_flags('heart rate')} patients have abnormal heart rate (<60 or >100 bpm).")
    if count_flags("Obesity"):
        summary_alerts.append(f"⚖️ {count_flags('Obesity')} patients classified as obese (BMI ≥ 30).")
    if count_flags("waist circumference"):
        summary_alerts.append(f"📏 {count_flags('waist circumference')} patients have high waist circumference (> 102 cm).")
    if count_flags("Malaria"):
        summary_alerts.append(f"🦟 {count_flags('Malaria')} patients tested positive for Malaria.")
    if count_flags("Hepatitis B"):
        summary_alerts.append(f"🧬 {count_flags('Hepatitis B')} patients are Hepatitis B positive.")
    if count_flags("HIV"):
        summary_alerts.append(f"🧪 {count_flags('HIV')} patients tested positive for HIV.")
    if count_flags("lung capacity"):
        summary_alerts.append(f"🌬️ {count_flags('lung capacity')} patients show signs of reduced lung capacity (<2.5L).")
    if count_flags("Widal"):
        summary_alerts.append(f"🧫 {count_flags('Widal')} patients have reactive Widal tests. Possible typhoid symptoms.")

    metric_averages = {}
    if include_metrics:
        for key, values in metric_values.items():
            if values:
                metric_averages[key] = {
                    "average": round(mean(values), 2),
                    "min": min(values),
                    "max": max(values)
                }

    return {
        "timestamp": now.isoformat(),
        "message": "Dashboard generated successfully",
        "total_patients": len(user_health_data),
        "flagged_cases": len(patients_flagged),
        "flagged_patients": patients_flagged,
        "alerts": summary_alerts,
        #"metric_averages": metric_averages if include_metrics else "-- Skipped --"
    }
