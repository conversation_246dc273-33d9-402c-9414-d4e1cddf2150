"""
Lab Test Explainer Tool

This tool provides detailed explanations of various laboratory tests, including:
- What the test measures
- Normal ranges
- When the test is ordered
- Patient education information
- Preparation requirements
- Interpretation guidance
"""

import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from langchain.tools import Tool

class LabTestExplainer:
    """
    A tool that provides detailed explanations of various laboratory tests.
    """

    def __init__(self):
        # Database of lab tests with detailed information
        self.lab_tests = {
            "Complete Blood Count (CBC)": {
                "description": "A complete blood count (CBC) is a blood test that measures various components and features of your blood, including red blood cells, white blood cells, and platelets.",
                "what_it_measures": [
                    "Red blood cell count (RBC)",
                    "White blood cell count (WBC)",
                    "Platelet count",
                    "Hemoglobin (Hgb)",
                    "Hematocrit (Hct)",
                    "Mean corpuscular volume (MCV)",
                    "Mean corpuscular hemoglobin (MCH)",
                    "Mean corpuscular hemoglobin concentration (MCHC)",
                    "Red cell distribution width (RDW)"
                ],
                "normal_ranges": {
                    "RBC": "4.5-5.9 million cells/mcL for men; 4.1-5.1 million cells/mcL for women",
                    "WBC": "4,500-11,000 cells/mcL",
                    "Platelets": "150,000-450,000 cells/mcL",
                    "Hemoglobin": "13.5-17.5 g/dL for men; 12.0-15.5 g/dL for women",
                    "Hematocrit": "38.8-50.0% for men; 34.9-44.5% for women",
                    "MCV": "80-96 femtoliters (fL)",
                    "MCH": "27-33 picograms (pg)",
                    "MCHC": "33-36 g/dL",
                    "RDW": "11.5-14.5%"
                },
                "when_ordered": [
                    "As part of a routine medical exam",
                    "To diagnose or monitor various conditions, including anemia, infection, and blood disorders",
                    "To monitor treatment effects for various conditions",
                    "Before surgery to check for bleeding problems"
                ],
                "patient_education": "A CBC is a common blood test that requires no special preparation. It helps your doctor evaluate your overall health and detect a wide range of disorders, including anemia, infection, and leukemia.",
                "preparation": "No special preparation is needed. You can eat and drink normally before the test.",
                "interpretation": "Abnormal CBC results may indicate various conditions, including anemia, infection, inflammation, bleeding disorders, or certain types of cancer. Your doctor will interpret the results in the context of your overall health and other test results."
            },
            "Basic Metabolic Panel (BMP)": {
                "description": "A basic metabolic panel (BMP) is a group of blood tests that measure different chemicals in the blood. It provides information about your body's metabolism and chemical balance.",
                "what_it_measures": [
                    "Glucose (blood sugar)",
                    "Calcium",
                    "Sodium",
                    "Potassium",
                    "Bicarbonate (carbon dioxide)",
                    "Chloride",
                    "Blood urea nitrogen (BUN)",
                    "Creatinine"
                ],
                "normal_ranges": {
                    "Glucose": "70-99 mg/dL",
                    "Calcium": "8.5-10.2 mg/dL",
                    "Sodium": "135-145 mEq/L",
                    "Potassium": "3.5-5.0 mEq/L",
                    "Bicarbonate": "23-29 mEq/L",
                    "Chloride": "96-106 mEq/L",
                    "BUN": "7-20 mg/dL",
                    "Creatinine": "0.6-1.2 mg/dL for men; 0.5-1.1 mg/dL for women"
                },
                "when_ordered": [
                    "As part of a routine medical exam",
                    "To monitor existing conditions like kidney disease or diabetes",
                    "To check the body's electrolyte balance",
                    "To monitor medications that might affect kidney function or electrolyte levels"
                ],
                "patient_education": "The BMP provides important information about your metabolism, including kidney function, blood sugar levels, and electrolyte balance. It's often used to monitor overall health or specific conditions like diabetes or kidney disease.",
                "preparation": "You may need to fast for 8-12 hours before the test. Follow your doctor's instructions.",
                "interpretation": "Abnormal results may indicate kidney problems, diabetes, dehydration, or electrolyte imbalances. Your doctor will interpret the results based on your medical history and other factors."
            },
            "Lipid Profile": {
                "description": "A lipid profile is a blood test that measures the levels of lipids (fats) in your blood, including cholesterol and triglycerides.",
                "what_it_measures": [
                    "Total cholesterol",
                    "High-density lipoprotein (HDL) cholesterol ('good' cholesterol)",
                    "Low-density lipoprotein (LDL) cholesterol ('bad' cholesterol)",
                    "Triglycerides",
                    "Non-HDL cholesterol",
                    "Cholesterol/HDL ratio"
                ],
                "normal_ranges": {
                    "Total cholesterol": "Less than 200 mg/dL",
                    "HDL cholesterol": "40 mg/dL or higher for men; 50 mg/dL or higher for women",
                    "LDL cholesterol": "Less than 100 mg/dL",
                    "Triglycerides": "Less than 150 mg/dL",
                    "Non-HDL cholesterol": "Less than 130 mg/dL",
                    "Cholesterol/HDL ratio": "Less than 5:1, with optimal being 3.5:1"
                },
                "when_ordered": [
                    "As part of a routine health exam",
                    "To assess risk of heart disease",
                    "To monitor effectiveness of cholesterol-lowering medications",
                    "For people with risk factors for heart disease, such as high blood pressure, diabetes, or family history"
                ],
                "patient_education": "A lipid profile helps assess your risk of heart disease and stroke. High levels of LDL cholesterol and triglycerides, along with low levels of HDL cholesterol, increase your risk of cardiovascular disease.",
                "preparation": "You may need to fast for 9-12 hours before the test. Water is allowed. Follow your doctor's instructions.",
                "interpretation": "Abnormal results may indicate an increased risk of heart disease. Your doctor will consider other risk factors, such as age, family history, and lifestyle, when interpreting your results."
            },
            "Liver Function Tests (LFTs)": {
                "description": "Liver function tests are blood tests that measure various enzymes, proteins, and substances that are produced or processed by the liver.",
                "what_it_measures": [
                    "Alanine transaminase (ALT)",
                    "Aspartate transaminase (AST)",
                    "Alkaline phosphatase (ALP)",
                    "Gamma-glutamyl transferase (GGT)",
                    "Bilirubin (total and direct)",
                    "Albumin",
                    "Total protein"
                ],
                "normal_ranges": {
                    "ALT": "7-56 units/L for men; 7-45 units/L for women",
                    "AST": "10-40 units/L",
                    "ALP": "44-147 units/L",
                    "GGT": "8-61 units/L for men; 5-36 units/L for women",
                    "Total bilirubin": "0.1-1.2 mg/dL",
                    "Direct bilirubin": "0-0.3 mg/dL",
                    "Albumin": "3.4-5.4 g/dL",
                    "Total protein": "6.0-8.3 g/dL"
                },
                "when_ordered": [
                    "To screen for liver disorders",
                    "To monitor liver function during treatment with certain medications",
                    "To help diagnose conditions such as hepatitis, cirrhosis, or liver damage",
                    "To monitor the progression of liver disease"
                ],
                "patient_education": "Liver function tests help assess the health of your liver. The liver performs many vital functions, including filtering toxins from the blood, producing proteins necessary for blood clotting, and metabolizing drugs and nutrients.",
                "preparation": "You may need to fast for 8-12 hours before the test. Some medications can affect results, so inform your doctor about all medications you're taking.",
                "interpretation": "Elevated liver enzymes may indicate liver damage or disease. The pattern of abnormalities can help identify the specific liver problem. Your doctor will interpret the results in the context of your symptoms and medical history."
            },
            "Thyroid Function Tests": {
                "description": "Thyroid function tests are blood tests that check how well your thyroid gland is working. They measure levels of thyroid hormones and thyroid-stimulating hormone (TSH).",
                "what_it_measures": [
                    "Thyroid-stimulating hormone (TSH)",
                    "Free thyroxine (Free T4)",
                    "Free triiodothyronine (Free T3)",
                    "Total T4",
                    "Total T3",
                    "Thyroid antibodies (in some cases)"
                ],
                "normal_ranges": {
                    "TSH": "0.4-4.0 mIU/L",
                    "Free T4": "0.8-1.8 ng/dL",
                    "Free T3": "2.3-4.2 pg/mL",
                    "Total T4": "5.0-12.0 μg/dL",
                    "Total T3": "80-200 ng/dL"
                },
                "when_ordered": [
                    "To diagnose hyperthyroidism (overactive thyroid) or hypothyroidism (underactive thyroid)",
                    "To monitor treatment for thyroid disorders",
                    "To screen for thyroid problems during pregnancy",
                    "To evaluate symptoms such as fatigue, weight changes, or mood disorders"
                ],
                "patient_education": "Thyroid function tests help diagnose and monitor thyroid disorders. The thyroid gland produces hormones that regulate metabolism, energy production, and many other body functions.",
                "preparation": "No special preparation is usually needed. However, certain medications can affect results, so inform your doctor about all medications you're taking.",
                "interpretation": "Abnormal results may indicate hyperthyroidism, hypothyroidism, or other thyroid disorders. Your doctor will interpret the results in the context of your symptoms and medical history."
            },
            "Widal Test": {
                "description": "The Widal test is a blood test used to diagnose typhoid fever by detecting antibodies against Salmonella typhi and Salmonella paratyphi in the blood.",
                "what_it_measures": [
                    "Antibodies against Salmonella typhi O antigen",
                    "Antibodies against Salmonella typhi H antigen",
                    "Antibodies against Salmonella paratyphi A,H antigen",
                    "Antibodies against Salmonella paratyphi B,H antigen"
                ],
                "normal_ranges": {
                    "Typhi O": "Titer < 1:80 (Non-reactive)",
                    "Typhi H": "Titer < 1:80 (Non-reactive)",
                    "Paratyphi A,H": "Titer < 1:80 (Non-reactive)",
                    "Paratyphi B,H": "Titer < 1:80 (Non-reactive)"
                },
                "when_ordered": [
                    "When typhoid fever is suspected",
                    "In patients with prolonged fever, especially in endemic areas",
                    "In patients with symptoms like fever, headache, abdominal pain, and diarrhea or constipation",
                    "For differential diagnosis of fever of unknown origin"
                ],
                "patient_education": "The Widal test helps diagnose typhoid fever, a bacterial infection caused by Salmonella typhi. It's most useful in areas where typhoid is common. The test measures antibodies your body produces in response to the infection.",
                "preparation": "No special preparation is needed. You can eat and drink normally before the test.",
                "interpretation": "A positive result (reactive) with high titers, especially for both O and H antigens, suggests typhoid fever. However, the test has limitations, including false positives and false negatives. Results should be interpreted along with clinical symptoms and other tests."
            },
            "Malaria Test": {
                "description": "Malaria tests detect the presence of malaria parasites in the blood. The most common tests include blood smears (thick and thin) and rapid diagnostic tests (RDTs).",
                "what_it_measures": [
                    "Presence of malaria parasites in red blood cells",
                    "Type of malaria parasite (P. falciparum, P. vivax, P. malariae, P. ovale)",
                    "Parasite density (in blood smear tests)"
                ],
                "normal_ranges": {
                    "Result": "Negative (no malaria parasites detected)"
                },
                "when_ordered": [
                    "When malaria is suspected based on symptoms like fever, chills, and sweating",
                    "After travel to areas where malaria is common",
                    "For monitoring treatment effectiveness in patients with confirmed malaria",
                    "For screening in endemic areas"
                ],
                "patient_education": "Malaria tests detect parasites that cause malaria, a serious disease transmitted by mosquitoes. Early diagnosis and treatment are crucial to prevent complications. If you've traveled to an area where malaria is common and develop fever, seek medical attention promptly.",
                "preparation": "No special preparation is needed. The test requires a blood sample.",
                "interpretation": "A positive result indicates malaria infection. The type of parasite identified helps determine the appropriate treatment. P. falciparum malaria is the most severe form and requires prompt treatment. A negative result may need to be confirmed with additional tests if symptoms persist."
            },
            "Hemoglobin A1c (HbA1c)": {
                "description": "The hemoglobin A1c test measures the average blood sugar level over the past 2-3 months. It shows the percentage of hemoglobin proteins in your blood that are coated with sugar (glycated).",
                "what_it_measures": [
                    "Average blood glucose levels over the past 2-3 months"
                ],
                "normal_ranges": {
                    "Normal": "Below 5.7%",
                    "Prediabetes": "5.7% to 6.4%",
                    "Diabetes": "6.5% or higher"
                },
                "when_ordered": [
                    "To diagnose diabetes or prediabetes",
                    "To monitor blood sugar control in people with diabetes",
                    "To adjust diabetes treatment plans",
                    "As a screening test for people at risk of diabetes"
                ],
                "patient_education": "The HbA1c test provides a longer-term view of blood sugar control compared to daily glucose monitoring. It helps assess how well diabetes is being managed over time and can guide treatment decisions.",
                "preparation": "No fasting or special preparation is required. You can eat and drink normally before the test.",
                "interpretation": "Higher A1c levels indicate poorer blood sugar control and a higher risk of diabetes complications. For most adults with diabetes, the target A1c is below 7%, but individual targets may vary based on age, other health conditions, and risk of hypoglycemia."
            },
            "Urinalysis": {
                "description": "Urinalysis is a test that examines the physical, chemical, and microscopic properties of urine. It can provide information about many aspects of health and disease.",
                "what_it_measures": [
                    "Physical properties: color, clarity, specific gravity",
                    "Chemical properties: pH, protein, glucose, ketones, bilirubin, blood, nitrites, leukocyte esterase",
                    "Microscopic examination: red blood cells, white blood cells, epithelial cells, bacteria, crystals, casts"
                ],
                "normal_ranges": {
                    "Color": "Pale yellow to amber",
                    "Clarity": "Clear",
                    "Specific gravity": "1.005-1.030",
                    "pH": "4.5-8.0",
                    "Protein": "Negative",
                    "Glucose": "Negative",
                    "Ketones": "Negative",
                    "Blood": "Negative",
                    "Nitrites": "Negative",
                    "Leukocyte esterase": "Negative",
                    "RBCs": "0-2 per high power field",
                    "WBCs": "0-5 per high power field",
                    "Bacteria": "None or few",
                    "Crystals": "None or few",
                    "Casts": "None or few hyaline casts"
                },
                "when_ordered": [
                    "As part of a routine medical exam",
                    "To diagnose urinary tract infections",
                    "To evaluate kidney function",
                    "To monitor certain medical conditions, such as diabetes or kidney disease",
                    "During pregnancy to screen for preeclampsia and gestational diabetes"
                ],
                "patient_education": "Urinalysis can detect a wide range of conditions, from urinary tract infections to kidney disease and diabetes. It's a simple, non-invasive test that provides valuable information about your health.",
                "preparation": "You may be asked to provide a clean-catch midstream urine sample. This involves cleaning the genital area, starting urination, and then collecting urine midstream in a sterile container.",
                "interpretation": "Abnormal results may indicate various conditions, including urinary tract infection, kidney disease, diabetes, liver disease, or other metabolic disorders. Your doctor will interpret the results in the context of your symptoms and medical history."
            }
        }

    def explain_lab_test(self, test_name: str) -> Dict[str, Any]:
        """
        Provide detailed explanation of a specific lab test.

        Args:
            test_name: Name of the lab test to explain

        Returns:
            Dictionary containing detailed information about the lab test
        """
        # Check if the test exists in our database (exact match)
        if test_name in self.lab_tests:
            test_info = self.lab_tests[test_name]

            # Add timestamp to the response
            result = {
                "timestamp": datetime.now().isoformat(),
                "test_name": test_name,
                "found": True,
                **test_info
            }

            return result
        else:
            # Try case-insensitive matching
            test_name_lower = test_name.lower()
            for key in self.lab_tests.keys():
                if key.lower() == test_name_lower:
                    test_info = self.lab_tests[key]

                    # Add timestamp to the response
                    result = {
                        "timestamp": datetime.now().isoformat(),
                        "test_name": key,  # Use the correct case from the database
                        "found": True,
                        **test_info
                    }

                    return result

            # Try fuzzy matching for common typos
            # Check if any test name contains the query as a substring (case-insensitive)
            potential_matches = []
            for key in self.lab_tests.keys():
                # Check for substring match
                if test_name_lower in key.lower():
                    potential_matches.append(key)
                # Check for acronyms (e.g., "CBC" for "Complete Blood Count")
                elif test_name_lower in ''.join([word[0].lower() for word in key.split() if word[0].isalpha()]):
                    potential_matches.append(key)
                # Check for word-by-word partial matches
                elif any(word.lower() in key.lower() for word in test_name.split()):
                    potential_matches.append(key)

            if potential_matches:
                # If we have potential matches, return the first one
                test_info = self.lab_tests[potential_matches[0]]

                # Add timestamp to the response
                result = {
                    "timestamp": datetime.now().isoformat(),
                    "test_name": potential_matches[0],
                    "found": True,
                    "note": f"Showing results for '{potential_matches[0]}' instead of '{test_name}'",
                    "all_matches": potential_matches,
                    **test_info
                }

                return result

            # If no match found, generate information for a custom test
            return self.generate_custom_test_info(test_name)

    def generate_custom_test_info(self, test_name: str) -> Dict[str, Any]:
        """
        Generate information for a custom lab test not in our database.

        Args:
            test_name: Name of the custom lab test

        Returns:
            Dictionary containing generated information about the lab test
        """
        # Common test categories and their typical measurements
        test_categories = {
            "blood": ["blood", "serum", "plasma", "hematology", "cbc", "complete blood", "hemoglobin", "hgb", "hct", "rbc", "wbc", "platelets"],
            "chemistry": ["chemistry", "metabolic", "electrolyte", "glucose", "bmp", "cmp", "panel", "sodium", "potassium", "chloride", "bicarbonate", "bun", "creatinine", "calcium"],
            "lipid": ["lipid", "cholesterol", "triglyceride", "hdl", "ldl", "lipoprotein"],
            "liver": ["liver", "hepatic", "alt", "ast", "alp", "ggt", "bilirubin", "albumin", "protein"],
            "thyroid": ["thyroid", "tsh", "t3", "t4", "thyroxine"],
            "hormone": ["hormone", "estrogen", "testosterone", "progesterone", "cortisol", "insulin", "prolactin", "fsh", "lh"],
            "infectious": ["infectious", "antibody", "antigen", "culture", "pcr", "viral", "bacterial", "fungal", "parasite", "widal", "malaria"],
            "urine": ["urine", "urinalysis", "ua"],
            "cardiac": ["cardiac", "heart", "troponin", "ck", "ck-mb", "bnp", "nt-probnp"],
            "tumor": ["tumor", "cancer", "marker", "cea", "psa", "ca-125", "afp", "hcg"],
            "vitamin": ["vitamin", "nutrient", "mineral", "b12", "folate", "d", "iron", "ferritin", "zinc"],
            "coagulation": ["coagulation", "clotting", "pt", "inr", "ptt", "fibrinogen", "d-dimer"],
            "allergy": ["allergy", "ige", "rast"],
            "autoimmune": ["autoimmune", "ana", "rf", "esr", "crp", "c-reactive", "sedimentation"]
        }

        # Determine the likely category of the test
        test_name_lower = test_name.lower()
        likely_categories = []

        for category, keywords in test_categories.items():
            if any(keyword in test_name_lower for keyword in keywords):
                likely_categories.append(category)

        # Default to blood test if no category is identified
        if not likely_categories:
            likely_categories = ["blood"]

        # Generate appropriate description based on the test name and likely categories
        description = f"The {test_name} is "

        if "blood" in likely_categories:
            description += "a blood test that "
        elif "urine" in likely_categories:
            description += "a urine test that "
        else:
            description += "a laboratory test that "

        if "chemistry" in likely_categories:
            description += "measures various chemicals and metabolites in the blood. "
        elif "lipid" in likely_categories:
            description += "measures levels of fats (lipids) in the blood. "
        elif "liver" in likely_categories:
            description += "assesses liver function and detects liver damage. "
        elif "thyroid" in likely_categories:
            description += "evaluates thyroid gland function. "
        elif "hormone" in likely_categories:
            description += "measures hormone levels in the blood. "
        elif "infectious" in likely_categories:
            description += "detects infectious agents or the body's response to infection. "
        elif "cardiac" in likely_categories:
            description += "evaluates heart function and detects heart damage. "
        elif "tumor" in likely_categories:
            description += "measures substances that may indicate the presence of certain cancers. "
        elif "vitamin" in likely_categories:
            description += "measures levels of vitamins, minerals, or nutrients in the blood. "
        elif "coagulation" in likely_categories:
            description += "assesses blood clotting function. "
        elif "allergy" in likely_categories:
            description += "identifies allergic responses to specific substances. "
        elif "autoimmune" in likely_categories:
            description += "detects antibodies or markers associated with autoimmune conditions. "
        else:
            description += "provides important information about your health status. "

        description += "It is commonly used in clinical settings to help diagnose, monitor, or screen for various health conditions."

        # Generate what it measures based on the test name and categories
        what_it_measures = []

        # Add specific measurements based on test name keywords
        if "complete" in test_name_lower and "blood" in test_name_lower:
            what_it_measures = ["Red blood cells", "White blood cells", "Platelets", "Hemoglobin", "Hematocrit"]
        elif "lipid" in test_name_lower or "cholesterol" in test_name_lower:
            what_it_measures = ["Total cholesterol", "HDL cholesterol", "LDL cholesterol", "Triglycerides"]
        elif "liver" in test_name_lower:
            what_it_measures = ["Liver enzymes (ALT, AST, ALP)", "Bilirubin", "Albumin", "Total protein"]
        elif "thyroid" in test_name_lower:
            what_it_measures = ["Thyroid-stimulating hormone (TSH)", "Free thyroxine (T4)", "Free triiodothyronine (T3)"]
        elif "metabolic" in test_name_lower:
            what_it_measures = ["Glucose", "Electrolytes", "Kidney function markers", "Liver function markers"]
        elif "glucose" in test_name_lower:
            what_it_measures = ["Blood glucose level"]
        elif "hemoglobin" in test_name_lower and "a1c" in test_name_lower:
            what_it_measures = ["Average blood glucose levels over the past 2-3 months"]
        elif "urine" in test_name_lower or "urinalysis" in test_name_lower:
            what_it_measures = ["Physical properties of urine", "Chemical composition of urine", "Microscopic elements in urine"]
        else:
            # Generate generic measurements based on the first identified category
            if likely_categories:
                if likely_categories[0] == "blood":
                    what_it_measures = ["Blood components related to " + test_name]
                elif likely_categories[0] == "chemistry":
                    what_it_measures = ["Chemical components in blood related to metabolism"]
                elif likely_categories[0] == "infectious":
                    what_it_measures = ["Antibodies or antigens related to infectious agents"]
                elif likely_categories[0] == "hormone":
                    what_it_measures = ["Hormone levels in blood"]
                else:
                    what_it_measures = ["Specific markers related to " + likely_categories[0] + " function"]
            else:
                what_it_measures = ["Specific markers related to " + test_name]

        # Generate normal ranges (generic)
        normal_ranges = {}

        # Add patient education
        patient_education = f"The {test_name} provides valuable information about your health. "

        if "blood" in likely_categories:
            patient_education += "It's a common blood test that helps your healthcare provider assess your overall health. "

        patient_education += "Regular testing may be recommended to monitor your health status or to track the effectiveness of treatments. Always discuss your test results with your healthcare provider for proper interpretation."

        # Generate when ordered
        when_ordered = [
            "As part of a routine health assessment",
            "When specific symptoms suggest a need for this test",
            "To monitor existing health conditions",
            "To evaluate the effectiveness of treatments"
        ]

        # Generate preparation instructions
        preparation = "Ask your healthcare provider about any specific preparation needed for this test. "

        if "blood" in likely_categories:
            preparation += "Some blood tests require fasting for 8-12 hours before the test, while others don't need any special preparation."
        elif "urine" in likely_categories:
            preparation += "You may be asked to provide a clean-catch midstream urine sample."
        else:
            preparation += "Follow your healthcare provider's instructions regarding eating, drinking, and medication use before the test."

        # Return the generated information
        return {
            "timestamp": datetime.now().isoformat(),
            "test_name": test_name,
            "found": True,
            "is_generated": True,
            "description": description,
            "what_it_measures": what_it_measures,
            "normal_ranges": normal_ranges,
            "when_ordered": when_ordered,
            "patient_education": patient_education,
            "preparation": preparation,
            "note": "This information is generated based on the test name and may not be specific to your situation. Always consult with your healthcare provider for accurate information about this test."
        }

    def list_available_tests(self) -> List[str]:
        """
        List all available lab tests in the database.

        Returns:
            List of available lab test names
        """
        return list(self.lab_tests.keys())

def explain_lab_test(test_name: str) -> str:
    """
    Explain a specific lab test, providing details about what it measures,
    normal ranges, when it's ordered, and patient education information.

    Args:
        test_name: Name of the lab test to explain

    Returns:
        JSON string with detailed information about the lab test
    """
    explainer = LabTestExplainer()
    result = explainer.explain_lab_test(test_name)
    return json.dumps(result, indent=2)

def list_available_tests() -> str:
    """
    List all available lab tests in the database.

    Returns:
        JSON string with a list of available lab test names
    """
    explainer = LabTestExplainer()
    tests = explainer.list_available_tests()

    # Add a note about custom tests
    tests.append("Any other lab test - we can provide information on virtually any test")

    result = {
        "timestamp": datetime.now().isoformat(),
        "available_tests": tests,
        "count": len(tests),
        "note": "You can enter any lab test name, even if it's not in this list. Our system will generate information for tests not in our database."
    }
    return json.dumps(result, indent=2)

# Create a Tool instance for use with LangChain
lab_test_explainer_tool = Tool(
    name="LabTestExplainer",
    func=lambda x: explain_lab_test(x),
    description="Explains lab tests, including what they measure, normal ranges, when they're ordered, and patient education information."
)
