import streamlit as st
import requests
import json
import uuid
from datetime import datetime

# === CONSTANTS ===
API_URL = "http://127.0.0.1:8000"
MODELS = ["qwen2.5:1.5b", "deepseek-r1:1.5b"]
DEFAULT_MODEL = "qwen2.5:1.5b"

# === STYLING ===
st.markdown("""
<style>
    .main {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    .sidebar .sidebar-content {
        background-color: #2d2d2d;
    }
    .stTextInput textarea {
        color: #ffffff !important;
    }
    .stSelectbox div[data-baseweb="select"] {
        color: white !important;
        background-color: #3d3d3d !important;
    }
    .stSelectbox svg {
        fill: white !important;
    }
    .stSelectbox option {
        background-color: #2d2d2d !important;
        color: white !important;
    }
    div[role="listbox"] div {
        background-color: #2d2d2d !important;
        color: white !important;
    }
    .chat-title {
        margin-bottom: 0;
    }
    .stButton button {
        background-color: #4d4d4d;
        color: white;
        border: none;
    }
    .stButton button:hover {
        background-color: #5d5d5d;
    }
    .health-metrics {
        background-color: #2d2d2d;
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 10px;
    }
    .quick-actions {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;
    }
</style>
""", unsafe_allow_html=True)

# === SESSION STATE INITIALIZATION ===
if "user_id" not in st.session_state:
    st.session_state.user_id = str(uuid.uuid4())

if "session_id" not in st.session_state:
    st.session_state.session_id = str(uuid.uuid4())

if "message_log" not in st.session_state:
    st.session_state.message_log = [{"role": "ai", "content": "Hi! I'm Dr. Deuce. How can I help you today?"}]

if "chat_title" not in st.session_state:
    st.session_state.chat_title = "New Chat"

if "server_status" not in st.session_state:
    st.session_state.server_status = "Checking..."

if "waiting_for_vitals" not in st.session_state:
    st.session_state.waiting_for_vitals = False

if "waiting_for_health_score" not in st.session_state:
    st.session_state.waiting_for_health_score = False

if "waiting_for_kidney_function" not in st.session_state:
    st.session_state.waiting_for_kidney_function = False

if "kidney_data" not in st.session_state:
    st.session_state.kidney_data = {}

if "kidney_analysis_result" not in st.session_state:
    st.session_state.kidney_analysis_result = {}

if "waiting_for_confirmation" not in st.session_state:
    st.session_state.waiting_for_confirmation = False

if "confirmation_type" not in st.session_state:
    st.session_state.confirmation_type = None

# === HELPER FUNCTIONS ===
def check_server_status():
    """Check if the server is running"""
    try:
        response = requests.get(f"{API_URL}/status", timeout=5)
        if response.status_code == 200:
            return "Online ✅", response.json()
        return "Error ❌", None
    except requests.RequestException:
        return "Offline ❌", None

def get_default_health_data():
    """Get default health data from the server"""
    try:
        response = requests.get(f"{API_URL}/default-health-data", timeout=5)
        if response.status_code == 200:
            return response.json()
        print(f"Error getting default health data: {response.status_code}")
        # Return the exact DEFAULT_HEALTH_DATA structure
        return {
            "Glucose": None,
            "SpO2": None,
            "ECG (Heart Rate)": None,
            "Blood Pressure (Systolic)": None,
            "Blood Pressure (Diastolic)": None,
            "Weight (BMI)": None,
            "Temperature": None,
            "Malaria": "Unknown",
            "Widal Test": "Unknown",
            "Hepatitis B": "Unknown",
            "Voluntary Serology": "Unknown",
            "Perfusion_index": None,
            "Waist Circumference": None,
            "Fev": None
        }
    except requests.RequestException as e:
        print(f"Error connecting to server: {str(e)}")
        # Return the exact DEFAULT_HEALTH_DATA structure
        return {
            "Glucose": None,
            "SpO2": None,
            "ECG (Heart Rate)": None,
            "Blood Pressure (Systolic)": None,
            "Blood Pressure (Diastolic)": None,
            "Weight (BMI)": None,
            "Temperature": None,
            "Malaria": "Unknown",
            "Widal Test": "Unknown",
            "Hepatitis B": "Unknown",
            "Voluntary Serology": "Unknown",
            "Perfusion_index": None,
            "Waist Circumference": None,
            "Fev": None
        }

def query_agent(user_query, model):
    """Send a query to the agent"""
    try:
        payload = {
            "session_id": st.session_state.session_id,
            "user_id": st.session_state.user_id,
            "query": user_query,
            "model": model
        }
        response = requests.post(f"{API_URL}/query", json=payload)
        if response.status_code == 200:
            data = response.json()
            if "error" in data:
                return f"Error: {data['error']}"

            # Update chat title if available
            if "chat_title" in data:
                st.session_state.chat_title = data["chat_title"]

            # Check for tool intents
            if "tools_used" in data:
                tools = data.get("tools_used", [])
                if "health_score_intent" in tools:
                    st.session_state.waiting_for_confirmation = True
                    st.session_state.confirmation_type = "health_score"
                elif "vital_signs_intent" in tools:
                    st.session_state.waiting_for_confirmation = True
                    st.session_state.confirmation_type = "vital_signs"
                elif "kidney_function_intent" in tools:
                    st.session_state.waiting_for_confirmation = True
                    st.session_state.confirmation_type = "kidney_function"

            return data["response"]
        return f"Error: Server returned status code {response.status_code}"
    except requests.RequestException as e:
        return f"Error connecting to server: {str(e)}"

def submit_vital_signs(vital_signs):
    """Submit vital signs to the server"""
    try:
        payload = {
            "user_id": st.session_state.user_id,
            "vital_signs": vital_signs
        }
        response = requests.post(f"{API_URL}/vital-signs", json=payload)
        if response.status_code == 200:
            return response.json()
        return {"error": f"Server returned status code {response.status_code}"}
    except requests.RequestException as e:
        return {"error": f"Error connecting to server: {str(e)}"}

def submit_health_score(health_data):
    """Submit health data for score analysis"""
    try:
        # Log the data being sent
        print(f"Submitting health data: {json.dumps(health_data, indent=2)}")

        payload = {
            "user_id": st.session_state.user_id,
            "health_data": health_data
        }

        # Make the request
        response = requests.post(f"{API_URL}/health-score", json=payload)

        # Handle different status codes
        if response.status_code == 200:
            result = response.json()
            print(f"Received response: {json.dumps(result, indent=2)}")
            return result
        elif response.status_code == 500:
            # Try to parse the error message from the response
            try:
                error_data = response.json()
                error_msg = error_data.get('error', f"Server error (500): {response.text}")
            except:
                error_msg = f"Server error (500): {response.text}"
            print(f"Server error: {error_msg}")
            return {"error": error_msg}
        else:
            error_msg = f"Server returned status code {response.status_code}: {response.text}"
            print(error_msg)
            return {"error": error_msg}
    except requests.RequestException as e:
        error_msg = f"Error connecting to server: {str(e)}"
        print(error_msg)
        return {"error": error_msg}
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)
        return {"error": error_msg}

def submit_kidney_function(kidney_data):
    """Submit kidney function data for analysis"""
    try:
        # Log the data being sent
        print(f"Submitting kidney function data: {json.dumps(kidney_data, indent=2)}")

        payload = {
            "user_id": st.session_state.user_id,
            "kidney_data": kidney_data
        }

        # Make the request
        response = requests.post(f"{API_URL}/kidney-function", json=payload)

        # Handle different status codes
        if response.status_code == 200:
            result = response.json()
            print(f"Received response: {json.dumps(result, indent=2)}")

            # Store the kidney data and analysis result in session state for future reference
            st.session_state.kidney_data = kidney_data
            st.session_state.kidney_analysis_result = result

            return result
        elif response.status_code == 500:
            # Try to parse the error message from the response
            try:
                error_data = response.json()
                error_msg = error_data.get('error', f"Server error (500): {response.text}")
            except:
                error_msg = f"Server error (500): {response.text}"
            print(f"Server error: {error_msg}")
            return {"error": error_msg}
        else:
            error_msg = f"Server returned status code {response.status_code}: {response.text}"
            print(error_msg)
            return {"error": error_msg}
    except requests.RequestException as e:
        error_msg = f"Error connecting to server: {str(e)}"
        print(error_msg)
        return {"error": error_msg}
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)
        return {"error": error_msg}

def handle_confirmation(confirmation_type):
    """Handle user confirmation for different actions"""
    if confirmation_type == "vital_signs":
        st.session_state.waiting_for_vitals = True
        st.session_state.waiting_for_confirmation = False
        st.session_state.confirmation_type = None
        return "Please enter your vital signs below:"

    elif confirmation_type == "health_score":
        st.session_state.waiting_for_health_score = True
        st.session_state.waiting_for_confirmation = False
        st.session_state.confirmation_type = None
        return "Please enter your health data below for analysis:"

    elif confirmation_type == "kidney_function":
        st.session_state.waiting_for_kidney_function = True
        st.session_state.waiting_for_confirmation = False
        st.session_state.confirmation_type = None
        return "Please enter your kidney function test results below:"

    return "I'm not sure what you're confirming. How can I help you?"

# === SIDEBAR ===
with st.sidebar:
    # Server status at the top of the sidebar
    status_text, status_data = check_server_status()
    st.session_state.server_status = status_text

    st.markdown(f"### Agent Server: {st.session_state.server_status}")

    # User ID
    st.markdown(f"**User ID**: {st.session_state.user_id[:8]}...")

    # Model selection
    selected_model = st.selectbox(
        "Choose Model",
        MODELS,
        index=MODELS.index(DEFAULT_MODEL) if DEFAULT_MODEL in MODELS else 0
    )

    # Chat title
    st.markdown("### Chat")
    st.markdown(f"**Title**: {st.session_state.chat_title}")

    # New chat button
    if st.button("New Chat", key="new_chat"):
        st.session_state.session_id = str(uuid.uuid4())
        st.session_state.message_log = [{"role": "ai", "content": "Hi! I'm Dr. Deuce. How can I help you today?"}]
        st.session_state.chat_title = "New Chat"
        st.session_state.waiting_for_vitals = False
        st.session_state.waiting_for_health_score = False
        st.session_state.waiting_for_kidney_function = False
        st.session_state.waiting_for_confirmation = False
        st.session_state.confirmation_type = None
        st.rerun()

# === MAIN CONTENT ===
st.title("🩺 Dr. Deuce Health Assistant")
st.caption("Your AI Assistant for Healthcare Issues")

# Quick action buttons above the chat
st.markdown("### Quick Actions")
col1, col2, col3, col4 = st.columns(4)

with col1:
    if st.button("Monitor Vital Signs", key="btn_vitals"):
        st.session_state.waiting_for_confirmation = True
        st.session_state.confirmation_type = "vital_signs"
        st.session_state.message_log.append({"role": "ai", "content": "Would you like to enter your vital signs for monitoring? Type 'yes' to begin."})
        st.rerun()

with col2:
    if st.button("Health Score Analysis", key="btn_health_score"):
        st.session_state.waiting_for_confirmation = True
        st.session_state.confirmation_type = "health_score"
        st.session_state.message_log.append({"role": "ai", "content": "Would you like to analyze your health score? Type 'yes' to begin."})
        st.rerun()

with col3:
    if st.button("Kidney Function Test", key="btn_kidney_function"):
        st.session_state.waiting_for_confirmation = True
        st.session_state.confirmation_type = "kidney_function"
        st.session_state.message_log.append({"role": "ai", "content": "Would you like to analyze your kidney function? Type 'yes' to begin."})
        st.rerun()

with col4:
    if st.button("Health Consultation", key="btn_consultation"):
        response = query_agent("I'd like a health consultation", selected_model)
        st.session_state.message_log.append({"role": "user", "content": "I'd like a health consultation"})
        st.session_state.message_log.append({"role": "ai", "content": response})
        st.rerun()

# Chat history
st.markdown("### Chat History")
chat_container = st.container()

with chat_container:
    for message in st.session_state.message_log:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])

# Vital signs form (only shown when waiting for vitals)
if st.session_state.waiting_for_vitals:
    # Get default health data from the server
    default_data = get_default_health_data()

    with st.form(key="vital_signs_form"):
        st.markdown("### Enter Your Vital Signs")
        col1, col2 = st.columns(2)

        with col1:
            blood_pressure_systolic = st.number_input("Blood Pressure (Systolic)", min_value=70, max_value=200,
                                                    value=120 if default_data.get("Blood Pressure (Systolic)") is None else default_data.get("Blood Pressure (Systolic)"))
            blood_pressure_diastolic = st.number_input("Blood Pressure (Diastolic)", min_value=40, max_value=120,
                                                     value=80 if default_data.get("Blood Pressure (Diastolic)") is None else default_data.get("Blood Pressure (Diastolic)"))
            heart_rate = st.number_input("Heart Rate (bpm)", min_value=40, max_value=200,
                                        value=75 if default_data.get("ECG (Heart Rate)") is None else default_data.get("ECG (Heart Rate)"))
            temperature = st.number_input("Temperature (°C)", min_value=35.0, max_value=42.0,
                                         value=36.8 if default_data.get("Temperature") is None else default_data.get("Temperature"), step=0.1)

        with col2:
            glucose = st.number_input("Glucose (mg/dL)", min_value=50, max_value=300,
                                     value=100 if default_data.get("Glucose") is None else default_data.get("Glucose"))
            spo2 = st.number_input("SpO2 (%)", min_value=80, max_value=100,
                                  value=98 if default_data.get("SpO2") is None else default_data.get("SpO2"))

        submit_button = st.form_submit_button(label="Submit Vital Signs")

        if submit_button:
            vital_signs = {
                "Blood_Pressure_Systolic": blood_pressure_systolic,
                "Blood_Pressure_Diastolic": blood_pressure_diastolic,
                "Heart_Rate": heart_rate,
                "Temperature": temperature,
                "Glucose": glucose,
                "SpO2": spo2
            }

            result = submit_vital_signs(vital_signs)

            if "error" in result:
                response = f"Error processing vital signs: {result['error']}"
            else:
                response = f"**Vital Signs Analysis**\n\n{result.get('analysis', '')}"
                if result.get('alerts'):
                    response += f"\n\n**Alerts**:\n{result['alerts']}"

            st.session_state.message_log.append({"role": "user", "content": f"I've submitted my vital signs: {json.dumps(vital_signs, indent=2)}"})
            st.session_state.message_log.append({"role": "ai", "content": response})
            st.session_state.waiting_for_vitals = False
            st.rerun()

# Health score form (only shown when waiting for health score)
if st.session_state.waiting_for_health_score:
    # Get default health data from the server
    default_data = get_default_health_data()

    with st.form(key="health_score_form"):
        st.markdown("### Enter Your Health Data")
        col1, col2 = st.columns(2)

        with col1:
            weight = st.number_input("Weight (kg)", min_value=30.0, max_value=200.0, value=70.0, step=0.1)
            height = st.number_input("Height (cm)", min_value=100, max_value=250, value=170)
            bmi = round(weight / ((height/100) ** 2), 1)
            st.info(f"Calculated BMI: {bmi}")

            # Use None for default values to match the DEFAULT_HEALTH_DATA structure
            # But display reasonable defaults in the UI
            blood_pressure_systolic = st.number_input("Blood Pressure (Systolic)", min_value=70, max_value=200,
                                                    value=120 if default_data.get("Blood Pressure (Systolic)") is None else default_data.get("Blood Pressure (Systolic)"),
                                                    key="bp_sys_health")
            blood_pressure_diastolic = st.number_input("Blood Pressure (Diastolic)", min_value=40, max_value=120,
                                                     value=80 if default_data.get("Blood Pressure (Diastolic)") is None else default_data.get("Blood Pressure (Diastolic)"),
                                                     key="bp_dia_health")
            heart_rate = st.number_input("Heart Rate (bpm)", min_value=40, max_value=200,
                                        value=75 if default_data.get("ECG (Heart Rate)") is None else default_data.get("ECG (Heart Rate)"),
                                        key="hr_health")
            malaria = st.selectbox("Malaria", ["Positive", "Negative", "Unknown"], index=2)
            widal_test = st.selectbox("Widal Test", ["Positive", "Negative", "Unknown"], index=2)
            hepatitis_b = st.selectbox("Hepatitis B", ["Positive", "Negative", "Unknown"], index=2)
            voluntary_serology = st.selectbox("Voluntary Serology", ["Positive", "Negative", "Unknown"], index=2)

        with col2:
            glucose = st.number_input("Glucose (mg/dL)", min_value=50, max_value=300,
                                     value=100 if default_data.get("Glucose") is None else default_data.get("Glucose"),
                                     key="glucose_health")
            spo2 = st.number_input("SpO2 (%)", min_value=80, max_value=100,
                                  value=98 if default_data.get("SpO2") is None else default_data.get("SpO2"),
                                  key="spo2_health")
            temperature = st.number_input("Temperature (°C)", min_value=35.0, max_value=42.0,
                                         value=36.8 if default_data.get("Temperature") is None else default_data.get("Temperature"),
                                         step=0.1, key="temp_health")

        submit_button = st.form_submit_button(label="Analyze Health Score")

        if submit_button:
            # Use the exact structure from DEFAULT_HEALTH_DATA
            health_data = {
                "Glucose": glucose,
                "SpO2": spo2,
                "ECG (Heart Rate)": heart_rate,
                "Blood Pressure (Systolic)": blood_pressure_systolic,
                "Blood Pressure (Diastolic)": blood_pressure_diastolic,
                "Weight (BMI)": bmi,
                "Temperature": temperature,
                "Malaria": "Unknown",
                "Widal Test": "Unknown",
                "Hepatitis B": "Unknown",
                "Voluntary Serology": "Unknown",
                "Perfusion_index": None,
                "Waist Circumference": None,
                "Fev": None
            }

            result = submit_health_score(health_data)

            if "error" in result:
                response = f"Error analyzing health score: {result['error']}"
            else:
                response = f"**Health Score Analysis**\n\n{result.get('analysis', '')}"

            st.session_state.message_log.append({"role": "user", "content": f"I've submitted my health data for analysis: {json.dumps(health_data, indent=2)}"})
            st.session_state.message_log.append({"role": "ai", "content": response})
            st.session_state.waiting_for_health_score = False
            st.rerun()

# Kidney function test form (only shown when waiting for kidney function test)
if st.session_state.waiting_for_kidney_function:
    with st.form(key="kidney_function_form"):
        st.markdown("### Enter Your Kidney Function Test Results")
        col1, col2 = st.columns(2)

        with col1:
            serum_urea = st.number_input("Serum Urea", min_value=0.0, max_value=200.0, value=5.0, step=0.1)
            serum_creatinine = st.number_input("Serum Creatinine", min_value=0.0, max_value=20.0, value=1.0, step=0.1)
            serum_sodium = st.number_input("Serum Sodium", min_value=100.0, max_value=200.0, value=140.0, step=0.1)
            serum_potassium = st.number_input("Serum Potassium", min_value=1.0, max_value=10.0, value=4.0, step=0.1)
            serum_calcium = st.number_input("Serum Calcium", min_value=5.0, max_value=15.0, value=9.5, step=0.1)
            serum_uric_acid = st.number_input("Serum Uric Acid", min_value=1.0, max_value=20.0, value=5.0, step=0.1)

        with col2:
            urine_albumin = st.number_input("Urine Albumin", min_value=0.0, max_value=1000.0, value=10.0, step=1.0)
            urine_creatinine = st.number_input("Urine Creatinine", min_value=0.0, max_value=500.0, value=100.0, step=1.0)
            chloride = st.number_input("Chloride", min_value=50.0, max_value=150.0, value=100.0, step=0.1)
            bicarbonate = st.number_input("Bicarbonate", min_value=10.0, max_value=50.0, value=25.0, step=0.1)
            age = st.number_input("Age", min_value=1, max_value=120, value=40)
            sex = st.selectbox("Sex", ["Male", "Female"])

        submit_button = st.form_submit_button(label="Analyze Kidney Function")

        if submit_button:
            kidney_data = {
                "Serum Urea": serum_urea,
                "Serum Creatinine": serum_creatinine,
                "Serum Sodium": serum_sodium,
                "Serum Potassium": serum_potassium,
                "Serum Calcium": serum_calcium,
                "Serum Uric Acid": serum_uric_acid,
                "Urine Albumin": urine_albumin,
                "Urine Creatinine": urine_creatinine,
                "Chloride": chloride,
                "Bicarbonate": bicarbonate,
                "Age": age,
                "Sex": sex
            }

            result = submit_kidney_function(kidney_data)

            if "error" in result:
                response = f"Error analyzing kidney function: {result['error']}"
            else:
                # Format the response
                formatted_analysis = result.get("analysis", "")
                overall_health = result.get("overall_health", "Unknown")
                confidence_level = result.get("confidence_level", "Unknown")
                missing_parameters = result.get("missing_parameters", [])
                recommendations = result.get("recommendations", "")
                personalized_recommendations = result.get("personalized_recommendations", "")

                response = f"**Kidney Function Analysis**\n\n{formatted_analysis}\n\n"
                response += f"**Findings**:\n{overall_health}\n\n"
                response += f"**Confidence Level**: {confidence_level} "

                if missing_parameters:
                    response += f"(Due to missing parameters: {', '.join(missing_parameters)})\n\n"
                    response += f"Some parameters necessary for a more complete analysis were not provided, which may affect the accuracy of this assessment.\n\n"
                    if recommendations:
                        response += f"{recommendations}\n\n"
                else:
                    response += "(Due to complete data)\n\n"

                # Add personalized recommendations
                if personalized_recommendations:
                    response += f"**Personalized Recommendations**:\n{personalized_recommendations}\n\n"
                    response += "You can ask me for more specific recommendations based on your test results at any time."

            st.session_state.message_log.append({"role": "user", "content": f"I've submitted my kidney function test results for analysis: {json.dumps(kidney_data, indent=2)}"})
            st.session_state.message_log.append({"role": "ai", "content": response})
            st.session_state.waiting_for_kidney_function = False
            st.rerun()

# Chat input
user_query = st.chat_input("Type your message here...")

# Process user input
if user_query:
    # Add user message to chat history
    st.session_state.message_log.append({"role": "user", "content": user_query})

    # Check if waiting for confirmation
    if st.session_state.waiting_for_confirmation and user_query.lower() == "yes":
        response = handle_confirmation(st.session_state.confirmation_type)
        st.session_state.message_log.append({"role": "ai", "content": response})
    else:
        # Check if the user is asking for kidney function recommendations and we have data
        if any(keyword in user_query.lower() for keyword in ["kidney recommendation", "kidney advice", "renal advice", "kidney diet", "what should i do about my kidney"]) and st.session_state.kidney_analysis_result:
            # Generate personalized response based on stored kidney function data
            if "personalized_recommendations" in st.session_state.kidney_analysis_result:
                personalized_recommendations = st.session_state.kidney_analysis_result.get("personalized_recommendations", "")
                # Extract only the personalized part (remove general recommendations)
                if "**General recommendations**:" in personalized_recommendations:
                    personalized_recommendations = personalized_recommendations.split("**General recommendations**:")[0]

                response = f"Based on your kidney function test results, here are personalized recommendations:\n\n{personalized_recommendations}"
                st.session_state.message_log.append({"role": "ai", "content": response})
            else:
                # Get response from agent
                response = query_agent(user_query, selected_model)
                st.session_state.message_log.append({"role": "ai", "content": response})
        else:
            # Get response from agent
            response = query_agent(user_query, selected_model)
            st.session_state.message_log.append({"role": "ai", "content": response})

    # Rerun to update UI
    st.rerun()

# Footer
st.markdown("---")
st.caption(f"© {datetime.now().year} Dr. Deuce Health Assistant | Last updated: {datetime.now().strftime('%Y-%m-%d')}")
