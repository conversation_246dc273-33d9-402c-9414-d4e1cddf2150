from langchain_core.runnables import <PERSON>na<PERSON>Lambda
from langgraph.graph import StateGraph, END
import json
import os
import sys
from dotenv import load_dotenv
from langchain_core.prompts import ChatPromptTemplate
from langchain_ollama import ChatOllama
from pydantic import BaseModel

# === Add custom tools path ===
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), 'tools')))
from tools.tools_health_score import HealthScoreAnalysisTool
from tools.tools_vector import vector_search_tool
from tools.tools_monitor_vital_signs import vital_sign_monitoring_tool
from tools.tools_health_consult import automated_health_consultation_tool
from tools.tools_health_data_json import get_default_health_data
from tools.tools_kidney_function import kidney_function_analysis_tool

# === Load environment variables ===
load_dotenv()

# === MODEL SELECTION ===
llm = ChatOllama(model="qwen2.5:1.5b")

# === PROMPT TEMPLATE ===
prompt = ChatPromptTemplate.from_messages([
    ("system",
     "You are <PERSON><PERSON>, a certified and authorized medical assistant. "
     "You assist users by analyzing their health data, monitoring vitals, kidney function, and providing health consultations. "
     "Engage users in a friendly, conversational tone. Ask relevant follow-up questions when needed. "
     "Be concise, empathetic, and helpful. Always respond in English."),
    ("human", "{question}")
])

# === STATE MODEL ===
class GraphState(BaseModel):
    question: str
    llm_output: str = ""
    final_response: str = ""
    tools_used: list[str] = []
    health_data_collected: bool = False
    health_data: dict = {}
    kidney_data_collected: bool = False
    kidney_data: dict = {}
    kidney_analysis_result: dict = {}

# === DEFAULT HEALTH DATA ===
DEFAULT_HEALTH_DATA = {
    "Glucose": None,
    "SpO2": None,
    "ECG (Heart Rate)": None,
    "Blood Pressure (Systolic)": None,
    "Blood Pressure (Diastolic)": None,
    "Weight (BMI)": None,
    "Temperature": None,
    "Malaria": "Unknown",
    "Widal Test": "Unknown",
    "Hepatitis B": "Unknown",
    "Voluntary Serology": "Unknown",
    "Perfusion_index": None,
    "Waist Circumference": None,
    "Fev": None
}

# === Initialize HealthScoreAnalysisTool ===
health_score_tool = HealthScoreAnalysisTool()

# === NODE 1: LLM PROCESSING ===
def run_llm(state: GraphState) -> GraphState:
    # Check if the question is about kidney function recommendations and we have data
    if state.kidney_data_collected and any(keyword in state.question.lower() for keyword in ["kidney", "renal", "recommendation", "advice", "what should i do"]):
        # Generate personalized recommendations based on kidney function test results
        abnormal_params = []
        for analysis_item in state.kidney_analysis_result.get("analysis", []):
            if "High" in analysis_item or "Low" in analysis_item:
                param = analysis_item.split(":")[0].strip()
                abnormal_params.append(param)

        response = "Based on your kidney function test results, here are personalized recommendations:\n\n"

        if abnormal_params:
            if "eGFR" in abnormal_params:
                response += "- **For low eGFR**: Limit protein intake to reduce kidney workload. Aim for 0.8g of protein per kg of body weight daily. Reduce salt consumption to help control blood pressure. Stay well-hydrated but avoid excessive fluid intake.\n\n"
            if "Serum Creatinine" in abnormal_params:
                response += "- **For high Serum Creatinine**: Avoid creatine supplements which can increase creatinine levels. Limit red meat consumption. Stay hydrated to help your kidneys filter waste efficiently.\n\n"
            if "BUN" in abnormal_params or "Serum Urea" in abnormal_params:
                response += "- **For elevated BUN/Urea**: Reduce protein intake, especially animal proteins. Ensure adequate hydration to help flush out urea.\n\n"
            if "Serum Sodium" in abnormal_params:
                response += "- **For abnormal Sodium levels**: Consult with a doctor about appropriate fluid and salt intake. Sodium imbalances can indicate kidney dysfunction.\n\n"
            if "Serum Potassium" in abnormal_params:
                response += "- **For abnormal Potassium levels**: This can be serious and requires medical attention. Discuss with your doctor about dietary adjustments and possible medication review.\n\n"
            if "ACR" in abnormal_params:
                response += "- **For elevated ACR (albumin-to-creatinine ratio)**: This indicates protein in urine. Control blood pressure and blood sugar levels, and reduce salt intake.\n\n"

            response += "**General recommendations**:\n"
            response += "- Maintain a healthy blood pressure (below 130/80 mmHg for kidney patients)\n"
            response += "- Control blood sugar if you have diabetes\n"
            response += "- Follow a kidney-friendly diet low in sodium, phosphorus, and protein\n"
            response += "- Stay physically active with doctor-approved exercises\n"
            response += "- Avoid nephrotoxic medications like NSAIDs (ibuprofen, naproxen)\n"
            response += "- Schedule regular follow-up appointments with a nephrologist\n\n"
        else:
            response += "Your kidney function parameters appear to be within normal ranges. To maintain kidney health:\n\n"
            response += "- Stay well-hydrated\n"
            response += "- Maintain a balanced diet low in sodium\n"
            response += "- Exercise regularly\n"
            response += "- Avoid smoking and limit alcohol consumption\n"
            response += "- Control blood pressure and blood sugar levels\n\n"

        response += "**Important**: Please consult with a healthcare professional before making any significant changes to your diet or lifestyle. These recommendations are based on your test results but are not a substitute for medical advice."

        return GraphState(
            question=state.question,
            llm_output=response,
            tools_used=state.tools_used,
            health_data_collected=state.health_data_collected,
            kidney_data_collected=state.kidney_data_collected,
            kidney_data=state.kidney_data,
            kidney_analysis_result=state.kidney_analysis_result
        )

    # Normal LLM processing for other queries
    chain = prompt | llm
    result = chain.invoke({"question": state.question})
    return GraphState(
        question=state.question,
        llm_output=result.content,
        tools_used=state.tools_used,
        health_data_collected=state.health_data_collected,
        kidney_data_collected=state.kidney_data_collected,
        kidney_data=state.kidney_data,
        kidney_analysis_result=state.kidney_analysis_result
    )

# Helper functions
def detect_abnormal_patterns(vitals_data):
    alerts = []
    if vitals_data.get("Glucose") and vitals_data["Glucose"] > 140:
        alerts.append("High glucose levels detected. Consider consulting a doctor.")
    if vitals_data.get("SpO2") and vitals_data["SpO2"] < 95:
        alerts.append("Low SpO2 levels detected. Ensure proper ventilation.")
    if vitals_data.get("ECG (Heart Rate)") and vitals_data["ECG (Heart Rate)"] > 100:
        alerts.append("High heart rate detected. Practice stress management.")
    if vitals_data.get("Temperature") and vitals_data["Temperature"] > 37.5:
        alerts.append("Fever detected. Stay hydrated and consult a doctor if it persists.")
    return "\n".join(alerts) if alerts else "No abnormal patterns detected."

def recommend_lifestyle_changes(vitals_data):
    recommendations = []
    if vitals_data.get("Glucose") and vitals_data["Glucose"] > 140:
        recommendations.append("Consider reducing sugar intake and exercising regularly.")
    if vitals_data.get("SpO2") and vitals_data["SpO2"] < 95:
        recommendations.append("Ensure proper ventilation and consider breathing exercises.")
    if vitals_data.get("ECG (Heart Rate)") and vitals_data["ECG (Heart Rate)"] > 100:
        recommendations.append("Practice stress management techniques like meditation.")
    if vitals_data.get("Temperature") and vitals_data["Temperature"] > 37.5:
        recommendations.append("Stay hydrated and consult a doctor if fever persists.")
    return "\n".join(recommendations) if recommendations else "No specific recommendations at this time."


def run_tool_logic(state: GraphState) -> GraphState:
    llm_output = state.llm_output or ""
    llm_output_lower = llm_output.lower()

    # Clear LLM output after capturing
    state.llm_output = ""

    # Health Score Analysis
    if "health score" in llm_output_lower and not state.health_data_collected:
        print("\n🩺 Dr. Deuce: Would you like to get an analysis of your health status? (yes/no)")
        user_response = input("👤 You: ").strip().lower()

        if user_response == "yes":
            user_health_data = DEFAULT_HEALTH_DATA.copy()

            print("\n💡 Please enter your health data. (Press Enter to skip any field and use 'null')\n")
            for key in user_health_data:
                user_input = input(f"{key}: ").strip()
                if user_input:
                    try:
                        # Accept numeric or textual input
                        if user_input.replace('.', '', 1).isdigit():
                            user_health_data[key] = float(user_input)
                        else:
                            user_health_data[key] = user_input
                    except ValueError:
                        print(f"⚠️ Invalid input for {key}, setting as 'null'.")
                        user_health_data[key] = "null"
                else:
                    print(f"ℹ️ No input for {key}, setting as 'null'.")
                    user_health_data[key] = "null"

            try:
                report = health_score_tool.generate_report(user_health_data)

                total_score = report.get('Total Score', 0)
                status = report.get('Health Status', 'Unknown')

                if str(status).lower() == "excellent":
                    summary = f" Amazing! Your current health score is {total_score}%. You're in excellent health — keep it up!"
                elif str(status).lower() == "good":
                    summary = f" Your current health score is {total_score}%. You're doing well — just maintain your routine!"
                elif str(status).lower() == "fair":
                    summary = f" Your health score is {total_score}%. You're on the right track, but there's room for improvement."
                elif str(status).lower() == "poor":
                    summary = f" Your health score is {total_score}%. It indicates poor health. Let's work together to improve it!"
                else:
                    summary = f" Your health score is {total_score}%. Please review the analysis below for details."

                state.llm_output += (
                    f"\n\n{summary}"
                    f"\n\n📊 Health Score Analysis:\n"
                    f"Total Score: {total_score}\n"
                    f"Health Status: {status}\n"
                    f"Vitals Needing Improvement: {report.get('Vitals Needing Improvement', 'N/A')}\n"
                    f"Improvement Tips: {report.get('Improvement Tips', 'N/A')}\n"
                )

                state.tools_used.append("health_score_tool")
                state.health_data_collected = True

            except Exception as e:
                state.llm_output += f"\n\n⚠️ Error processing health data: {e}"

    # Kidney Function Test
    elif any(keyword in llm_output_lower for keyword in ["kidney", "kidney function", "kidney test", "renal"]):
        print("\n🩺 Dr. Deuce: Would you like to analyze your kidney function? (yes/no)")
        user_response = input("👤 You: ").strip().lower()

        if user_response == "yes":
            kidney_data = {}

            # Define required kidney function parameters
            kidney_params = [
                "Serum Urea", "Serum Creatinine", "Serum Sodium", "Serum Potassium",
                "Serum Calcium", "Serum Uric Acid", "Urine Albumin", "Urine Creatinine",
                "Chloride", "Bicarbonate", "Age", "Sex"
            ]

            print("\n💡 Please enter your kidney function test results. (Press Enter to skip any field)\n")
            for param in kidney_params:
                user_input = input(f"{param}: ").strip()
                if user_input:
                    try:
                        if param == "Sex":
                            kidney_data[param] = user_input
                        else:
                            kidney_data[param] = float(user_input) if user_input.replace('.', '', 1).isdigit() else None
                    except ValueError:
                        print(f"⚠️ Invalid input for {param}, skipping.")
                else:
                    print(f"ℹ️ No input for {param}, skipping.")

            if kidney_data:
                try:
                    # Run kidney function analysis
                    result = kidney_function_analysis_tool(kidney_data)

                    # Store kidney data and analysis results in state for personalized recommendations
                    state.kidney_data = kidney_data
                    state.kidney_data_collected = True
                    state.kidney_analysis_result = result

                    # Format the analysis for display
                    formatted_analysis = "\n".join(result.get("analysis", []))

                    # Build the response
                    state.llm_output += f"\n\n🔹 Kidney Function Analysis:\n\n{formatted_analysis}\n\n"
                    state.llm_output += f"Based on the available test results, here is my preliminary assessment:\n\n"
                    state.llm_output += f"🔹 Findings:\n{result.get('overall_health', 'Unknown')}\n\n"

                    # Add confidence level
                    confidence_level = result.get('confidence_level', 'Unknown')
                    missing_params = result.get('missing_parameters', [])
                    if missing_params:
                        state.llm_output += f"🔹 Confidence Level: {confidence_level} (Due to missing parameters: {', '.join(missing_params)})\n\n"
                        state.llm_output += f"Some parameters necessary for a more complete analysis were not provided, which may affect the accuracy of this assessment.\n\n"
                        if result.get("recommendations"):
                            state.llm_output += f"{result.get('recommendations')}"
                    else:
                        state.llm_output += f"🔹 Confidence Level: {confidence_level} (Due to complete data)"

                    # Add personalized recommendations based on abnormal values
                    abnormal_params = []
                    for analysis_item in result.get("analysis", []):
                        if "High" in analysis_item or "Low" in analysis_item:
                            param = analysis_item.split(":")[0].strip()
                            abnormal_params.append(param)

                    if abnormal_params:
                        state.llm_output += f"\n\n🔹 Personalized Recommendations:\n"
                        if "eGFR" in abnormal_params:
                            state.llm_output += "- For low eGFR: Limit protein intake, reduce salt consumption, and stay well-hydrated.\n"
                        if "Serum Creatinine" in abnormal_params:
                            state.llm_output += "- For high Serum Creatinine: Avoid creatine supplements, limit red meat consumption, and stay hydrated.\n"
                        if "BUN" in abnormal_params or "Serum Urea" in abnormal_params:
                            state.llm_output += "- For elevated BUN/Urea: Reduce protein intake and ensure adequate hydration.\n"
                        if "Serum Sodium" in abnormal_params:
                            state.llm_output += "- For abnormal Sodium levels: Consult with a doctor about appropriate fluid and salt intake.\n"
                        if "Serum Potassium" in abnormal_params:
                            state.llm_output += "- For abnormal Potassium levels: Discuss with your doctor about dietary adjustments and possible medication review.\n"
                        if "ACR" in abnormal_params:
                            state.llm_output += "- For elevated ACR: Control blood pressure and blood sugar levels, and reduce salt intake.\n"

                        state.llm_output += "\nPlease consult with a healthcare professional before making any significant changes to your diet or lifestyle."

                    state.tools_used.append("kidney_function_tool")

                except Exception as e:
                    state.llm_output += f"\n\n⚠️ Error analyzing kidney function: {str(e)}"
            else:
                state.llm_output += "\n\n⚠️ No valid kidney function data provided. Skipping analysis."

    # Vitals Monitoring
    elif "vital signs" in llm_output_lower or "monitor" in llm_output_lower:
        print("\n🩺 Dr. Deuce: Based on your health score analysis, would you like me to monitor your vital signs? (yes/no)")
        user_response = input("👤 You: ").strip().lower()

        if user_response == "yes":
            user_vitals_data = {}

            print("\n💡 Please enter your vital signs.\n")
            for key in ["Glucose", "SpO2", "ECG (Heart Rate)", "Temperature"]:
                user_input = input(f"{key}: ").strip()
                if user_input:
                    try:
                        user_vitals_data[key] = float(user_input) if user_input.replace('.', '', 1).isdigit() else user_input
                    except ValueError:
                        print(f"⚠️ Invalid input for {key}, skipping.")

            if user_vitals_data:
                try:
                    health_data_json = json.dumps({"data": user_vitals_data})
                    print(f"Debug: JSON passed to vital_sign_monitoring_tool: {health_data_json}")

                    result = vital_sign_monitoring_tool.invoke(health_data_json)

                    if isinstance(result, dict):
                        state.llm_output += f"\n\n🩺 Vital Signs Monitoring:\n{result.get('message', '')}"
                        if result.get("suggest_consultation"):
                            state.llm_output += "\n\n🤖 Dr. Deuce: It seems like you may need to consult with a healthcare professional. Would you like me to help you book an appointment?"
                    else:
                        state.llm_output += f"\n\n🩺 Vital Signs Monitoring:\n{result}"

                    alerts = detect_abnormal_patterns(user_vitals_data)
                    if alerts:
                        state.llm_output += f"\n\n⚠️ Alerts:\n{alerts}"

                    recommendations = recommend_lifestyle_changes(user_vitals_data)
                    state.llm_output += f"\n\n💡 Lifestyle Recommendations:\n{recommendations}"

                    state.tools_used.append("vital_monitor_tool")

                except Exception as e:
                    state.llm_output += f"\n\n⚠️ Error monitoring vital signs: {e}"
            else:
                state.llm_output += "\n\n⚠️ No valid vital signs data provided. Skipping monitoring."

    # Health Consultation
    elif any(keyword in llm_output_lower for keyword in ["consult", "consultation", "doctor", "doctor advice", "see a doctor"]):
        print("[DEBUG] Detected consultation action.")  # Debugging line to verify the flow

        consultation_prompt = "\n\n🩺 Dr. Deuce: Would you like me to help you book an appointment with a doctor? (yes/no)"
        state.llm_output += consultation_prompt  # Add to chatbot output

        print(consultation_prompt)  # Also print for terminal output
        user_response = input("👤 You: ").strip().lower()

        if user_response == "yes":
            # Ensure the health data is being passed correctly to the tool
            if not hasattr(state, "health_data") or not state.health_data_collected:
                state.llm_output += "\n\n⚠️ No health data provided. Using default health data for consultation."
                state.health_data = DEFAULT_HEALTH_DATA  # Use default health data if none is provided

            try:
               # Convert health data to JSON format
                health_data_json = json.dumps({"data": state.health_data})
                print("[DEBUG] Passing health data to consultation tool.")  # Debugging line

                # Invoke the consultation tool
                consultation_result = automated_health_consultation_tool.invoke(health_data_json)
                print(f"[DEBUG] Consultation Result: {consultation_result}")  # Print tool result

                # Process the result from the tool
                result_json = json.loads(consultation_result)
                if 'Medical_Advice' in result_json:
                    state.llm_output += f"\n\n🩺 Dr. Deuce: Here's the analysis based on your health data:\n{result_json['Medical_Advice']}"
                    if result_json.get("Doctor_Visit_Recommended", False):
                        consultation_link = generate_appointment_link()  # Assuming it returns the booking URL
                        state.llm_output += f"\n\n🩺 Dr. Deuce: Based on your health analysis, it's recommended to see a doctor. You can book an appointment here: {consultation_link}"
                else:
                    state.llm_output += "\n\n⚠️ Dr. Deuce: There was an error processing your health data. Please try again."

                # Mark the consultation tool as used
                state.tools_used.append("health_consult_tool")
            except Exception as e:
                state.llm_output += f"\n\n⚠️ Dr. Deuce: An error occurred while processing your consultation request: {e}"
        else:
            state.llm_output += "\n\n🙂 Dr. Deuce: No worries. Let me know whenever you’re ready to book a consultation."


    # Extra Information (Vector Search)
    elif "vector" in llm_output_lower:
        result = vector_search_tool.invoke({"query": state.question})
        state.llm_output += f"\n\n🔍 Extra Info:\n{result}"
        state.tools_used.append("vector_search_tool")

    return state


def generate_appointment_link():
    # Define the base URL for your doctor consultation/appointment system
    consultation_url = "https://your-appointment-booking-system.com/consultation"
    return consultation_url


# === NODE 3: OUTPUT PARSE ===
def parse_output(state: GraphState) -> GraphState:
    return GraphState(
        question=state.question,
        llm_output=state.llm_output,
        final_response=state.llm_output,
        tools_used=state.tools_used,
        health_data_collected=state.health_data_collected,
        kidney_data_collected=state.kidney_data_collected,
        kidney_data=state.kidney_data,
        kidney_analysis_result=state.kidney_analysis_result
    )

# === BUILD LANGGRAPH ===
graph = StateGraph(GraphState)
graph.add_node("llm", RunnableLambda(run_llm))
graph.add_node("tool_logic", RunnableLambda(run_tool_logic))
graph.add_node("parse", RunnableLambda(parse_output))

graph.set_entry_point("llm")
graph.add_edge("llm", "tool_logic")
graph.add_edge("tool_logic", "parse")
graph.add_edge("parse", END)

app = graph.compile()

# === INTERACTIVE LOOP ===
print("\n💬 Welcome to Dr. Deuce! Type 'exit' to quit anytime.\n")

state = GraphState(question="")
while True:
    print("🩺 Dr. Deuce: What would you like to do? (health score, kidney function test, monitor vitals, consultation, exit)")
    user_input = input("👤 You: ").strip().lower()

    if user_input in ["exit", "quit"]:
        print("\n👋 Dr. Deuce says: Take care! See you next time.")
        break

    state.question = user_input
    result = app.invoke(state)
    state = GraphState(**result)

    print(f"\n🩺 Dr. Deuce: {state.final_response}\n")
    print(f"🛠️ Tools used: {state.tools_used}\n")