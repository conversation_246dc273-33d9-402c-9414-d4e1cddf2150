import random
import uuid
from datetime import timedelta
import datetime
from typing import List
import pandas as pd
from fastapi import Depends
from fastapi.responses import JSONResponse
from statsmodels.tsa.arima.model import ARIMA

from project.app.schemas.menstrual_schema import CycleList
from project.app.utils.jwt import get_current_user



general_advice = [
    "🧼 Maintain good menstrual hygiene — change your pad or tampon every 4–6 hours.",
    "💧 Stay hydrated — water helps reduce bloating and cramps.",
     "📆 Track your cycle regularly to understand your patterns.",
    "🩸 Light exercise may ease cramps.",
    "🛌 Rest when needed — hormonal changes can make you tired.",
    "🍫 A bit of dark chocolate can boost your mood.",
    "📦 Keep extra supplies (pads, tampons, cups) handy.",
    "📲 Use this app to track moods, cramps, or spotting."
]

def predict_next_cycle(cycles: List[dict]) -> dict:
    if len(cycles) < 3:
        return {"warning": "Not enough data for prediction"}

    df = pd.DataFrame(cycles)
    df["start_date"] = pd.to_datetime(df["start_date"])
    df = df.sort_values("start_date")
    ts = df["cycle_length"].astype(float)
    train = ts[:-1]
    last = df.iloc[-1]["start_date"]

    try:
        if len(train) < 3 or train.nunique() == 1:
            next_cycle_len = round(train.mean())
            model_type = "mean"
        else:
            model = ARIMA(train, order=(1, 1, 1)).fit()
            forecast = model.forecast()
            next_cycle_len = round(float(forecast[0]))
            model_type = "arima"
    except Exception:
        next_cycle_len = round(train.mean())
        model_type = "mean (fallback)"

    next_start = last + timedelta(days=next_cycle_len)
    ovulation = next_start - timedelta(days=14)
    window = f"{(ovulation - timedelta(days=2)).strftime('%Y-%m-%d')} to {(ovulation + timedelta(days=2)).strftime('%Y-%m-%d')}"

    return {
        "Predicted Cycle Length": next_cycle_len,
        "Prediction Method": model_type,
        "Next Period Start": next_start.strftime('%Y-%m-%d'),
        "Ovulation Window": window
    }
def get_cycle_recommendations(latest_cycle: dict, prediction: dict) -> List[str]:
    recs = []
    
    recs.append(f"Your last cycle was {latest_cycle.get('cycle_length', 'N/A')} days.")
    recs.append(f"Next predicted period start: {prediction.get('Next Period Start', 'N/A')}")
    recs.append("Track your symptoms to improve prediction accuracy.")
    recs.append(f"Ovulation likely between: {prediction.get('Ovulation Window', 'N/A')}")
    
    ovulation_window = prediction.get("Ovulation Window", "")
    next_period = prediction.get("Next Period Start", "")
    
    if ovulation_window and "to" in ovulation_window:
        try:
            fertile_start, fertile_end = ovulation_window.split(" to ")
            fertile_start_dt = datetime.datetime.strptime(fertile_start, "%Y-%m-%d").date()
            fertile_end_dt = datetime.datetime.strptime(fertile_end, "%Y-%m-%d").date()
            today = datetime.date.today()

            recs.append(f"🩸 Your next period is predicted on *{next_period}*. Log PMS symptoms like bloating or irritability 3–7 days before.")
            recs.append(f"🧬 Ovulation is expected between *{ovulation_window}* — this is when your chances of pregnancy are highest.")
            recs.append("💡 Tip: Avoid unprotected sex 5 days before ovulation and 1 day after if not planning pregnancy.")

            if today < fertile_start_dt:
                days_until = (fertile_start_dt - today).days
                recs.append(f"📅 Fertile window starts in {days_until} days.")
            elif fertile_start_dt <= today <= fertile_end_dt:
                recs.append("🔔 You are currently in your fertile window!")
            else:
                recs.append("✅ Fertile window has passed. Consider logging any symptoms and stay aware of next cycle.")
        except Exception as e:
            recs.append("⚠ Unable to parse ovulation window dates.")
    
    return recs

class MenstruationCyclePrediction:

    async def post(
        self,
        request: CycleList,
        current_user: dict = Depends(get_current_user)
    ):
        patient_id = current_user["patient_id"]
        session_id = str(uuid.uuid4())   
        user_key = (patient_id, session_id)

        payload_cycles = [cycle.dict() for cycle in request.cycles]

        cycles = sort_and_recalculate_cycles(payload_cycles)
        latest_cycle = cycles[-1]
        
        if len(cycles) < 3:
            disclaimer = (
                f"⚠ Disclaimer: You've entered {len(cycles)} cycle sample(s). "
                "This tool requires at least 3 months of data for accurate period and ovulation predictions. "
                "Your input has been logged, and we’ve given lifestyle feedback below. "
                "Once 3 entries are available, predictive recommendations will be enabled. "
                "Always consult a medical expertfor personal reproductive guidance."
            )
        
            return JSONResponse(content= {
                "mode": "Cycle Tracking",
                "latest_cycle": latest_cycle,
                "recommendations": random.sample(general_advice, k=5),
                "disclaimer": disclaimer,
                "entry_count": len(cycles),
                "offer_chat": True,
                "chat_prompt": "Would you like to chat with Dr. Deuce for deeper insights and support on your cycle health?"
            }
            )
        
        
        result = predict_next_cycle(cycles)
        
        disclaimer = (
            "⚠ Disclaimer: Predictions are based on your cycle data. While this tool offers data-driven insights, "
            "cycle lengths and symptoms vary. Always seek professional medical consultation when necessary."
        )

        entry = {
            "timestamp": datetime.datetime.now().isoformat(),
            "mode": "cycle",
            "input": payload_cycles,
            "prediction": result if len(cycles) >= 3 else {},
            "recommendations": get_cycle_recommendations(latest_cycle, result if len(cycles) >= 3 else {}),
        }
        # user_health_data.setdefault(user_key, []).append(entry)
        
        recommendations = get_cycle_recommendations(latest_cycle, result)

        response = {
            "message": "Cycle Entry Processed",
            "session_id": session_id,
            "patient_id": patient_id,
            "latest_cycle": latest_cycle,
            "prediction": result,
            "recommendations": recommendations + random.sample(general_advice, k=3),
            "entry_count": len(cycles),
            "disclaimer": disclaimer,
            "chat_prompt": "Would you like to chat with Dr. Deuce for insights on your cycle health?"
        }

        return JSONResponse(content=response, status_code=200)