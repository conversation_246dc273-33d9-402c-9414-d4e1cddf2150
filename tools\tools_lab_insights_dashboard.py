"""
Lab Insights Dashboard Tool

A tool for generating comprehensive lab insights and analytics for healthcare administrators.
Provides analytics on test requests, turnaround times, disease indicators, and geographic distribution.
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import Counter, defaultdict
from langchain.tools import Tool

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LabInsightsDashboardTool:
    """
    Lab Insights Dashboard Tool for monitoring lab usage and health trends
    
    This tool provides comprehensive analytics for healthcare facilities including:
    - Test volume analysis
    - Turnaround time monitoring
    - Disease trend detection
    - Geographic distribution analysis
    - AI-generated insights and recommendations
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.logger.info("🔬 Lab Insights Dashboard Tool initialized")
        
    def generate_dashboard(self, user_health_data: Dict, admin_id: str,
                          date_range_days: int = 30, 
                          test_type_filter: Optional[str] = None,
                          user_group_filter: Optional[str] = None) -> str:
        """
        Generate comprehensive Lab Insights Dashboard
        
        Args:
            user_health_data: Dictionary containing all user health data
            admin_id: ID of the admin requesting the dashboard
            date_range_days: Number of days to analyze (default: 30)
            test_type_filter: Optional filter for specific test types
            user_group_filter: Optional filter for specific user groups
            
        Returns:
            JSON string containing complete dashboard analytics
        """
        try:
            self.logger.info(f"🔬 Generating Lab Insights Dashboard for admin: {admin_id}")
            self.logger.info(f"📊 Analysis parameters - Days: {date_range_days}, Test filter: {test_type_filter}")
            
            cutoff_date = datetime.now() - timedelta(days=date_range_days)
            
            # Initialize analytics containers
            test_requests = Counter()
            test_turnaround_times = defaultdict(list)
            disease_indicators = Counter()
            geographic_distribution = Counter()
            daily_test_counts = defaultdict(int)
            
            # Process user health data
            total_users_analyzed = 0
            for user_key, user_data in user_health_data.items():
                user_id = user_key if isinstance(user_key, str) else f"{user_key[0]}_{user_key[1]}"
                location = self._extract_location(user_id)
                
                # Process different types of health data
                processed = self._process_health_records(
                    user_data, cutoff_date, test_requests, 
                    test_turnaround_times, disease_indicators,
                    geographic_distribution, location, 
                    test_type_filter, user_group_filter, daily_test_counts
                )
                
                if processed:
                    total_users_analyzed += 1
            
            # Generate comprehensive analytics
            dashboard_data = self._compile_dashboard_data(
                test_requests, test_turnaround_times, disease_indicators,
                geographic_distribution, daily_test_counts, total_users_analyzed,
                date_range_days, test_type_filter, user_group_filter, admin_id
            )
            
            self.logger.info(f"✅ Dashboard generated successfully for {total_users_analyzed} users")
            return json.dumps(dashboard_data, indent=2)
            
        except Exception as e:
            error_msg = f"Error generating lab insights dashboard: {str(e)}"
            self.logger.error(error_msg)
            return json.dumps({"error": error_msg, "success": False}, indent=2)
    
    def _process_health_records(self, user_data: Any, cutoff_date: datetime,
                               test_requests: Counter, test_turnaround_times: defaultdict,
                               disease_indicators: Counter, geographic_distribution: Counter,
                               location: str, test_type_filter: Optional[str],
                               user_group_filter: Optional[str], daily_test_counts: defaultdict) -> bool:
        """Process individual user health records"""
        
        processed_any = False
        
        # Handle different data structures
        if isinstance(user_data, dict):
            # Process different types of stored data
            for data_type, data_content in user_data.items():
                if data_type in ['health_score', 'test_results', 'kidney_function', 
                               'lipid_profile', 'lung_capacity', 'liver_function']:
                    if self._process_test_data(data_type, data_content, cutoff_date,
                                             test_requests, test_turnaround_times,
                                             disease_indicators, geographic_distribution,
                                             location, test_type_filter, daily_test_counts):
                        processed_any = True
        
        elif isinstance(user_data, list):
            # Process list of health records (vital signs tracking)
            for record in user_data:
                if isinstance(record, dict) and 'timestamp' in record:
                    try:
                        record_date = datetime.fromisoformat(record['timestamp'].replace('Z', '+00:00').replace('+00:00', ''))
                        if record_date >= cutoff_date:
                            self._process_vital_signs_record(record, test_requests, 
                                                           disease_indicators, 
                                                           geographic_distribution, location,
                                                           daily_test_counts, record_date)
                            processed_any = True
                    except Exception as e:
                        self.logger.warning(f"Error processing vital signs record: {str(e)}")
        
        return processed_any
    
    def _process_test_data(self, test_type: str, data_content: Dict, cutoff_date: datetime,
                          test_requests: Counter, test_turnaround_times: defaultdict,
                          disease_indicators: Counter, geographic_distribution: Counter,
                          location: str, test_type_filter: Optional[str],
                          daily_test_counts: defaultdict) -> bool:
        """Process specific test data"""
        
        if not isinstance(data_content, dict) or 'timestamp' not in data_content:
            return False
            
        try:
            test_date = datetime.fromisoformat(data_content['timestamp'].replace('Z', '+00:00').replace('+00:00', ''))
            if test_date < cutoff_date:
                return False
                
            # Apply test type filter
            if test_type_filter and test_type != test_type_filter:
                return False
                
            # Count test request
            test_requests[test_type] += 1
            geographic_distribution[location] += 1
            
            # Track daily counts
            date_key = test_date.strftime('%Y-%m-%d')
            daily_test_counts[date_key] += 1
            
            # Simulate turnaround time
            turnaround_hours = self._simulate_turnaround_time(test_type)
            test_turnaround_times[test_type].append(turnaround_hours)
            
            # Analyze for disease indicators
            if 'data' in data_content:
                self._extract_disease_indicators(data_content['data'], disease_indicators)
            
            return True
                
        except Exception as e:
            self.logger.warning(f"Error processing test data for {test_type}: {str(e)}")
            return False
    
    def _process_vital_signs_record(self, record: Dict, test_requests: Counter,
                                   disease_indicators: Counter, 
                                   geographic_distribution: Counter, location: str,
                                   daily_test_counts: defaultdict, record_date: datetime) -> None:
        """Process vital signs records"""
        test_requests['vital_signs_monitoring'] += 1
        geographic_distribution[location] += 1
        
        # Track daily counts
        date_key = record_date.strftime('%Y-%m-%d')
        daily_test_counts[date_key] += 1
        
        # Check for concerning vital signs patterns
        if 'Blood Pressure (Systolic)' in record and record['Blood Pressure (Systolic)']:
            try:
                if float(record['Blood Pressure (Systolic)']) > 140:
                    disease_indicators['hypertension_risk'] += 1
            except (ValueError, TypeError):
                pass
                
        if 'Glucose' in record and record['Glucose']:
            try:
                if float(record['Glucose']) > 126:
                    disease_indicators['diabetes_risk'] += 1
            except (ValueError, TypeError):
                pass
    
    def _extract_disease_indicators(self, test_data: Dict, disease_indicators: Counter) -> None:
        """Extract disease indicators from test data"""
        
        # Check for specific test results
        positive_tests = {
            'Malaria': 'malaria_positive',
            'Hepatitis B': 'hepatitis_b_positive', 
            'Widal Test': 'typhoid_positive'
        }
        
        for test_name, indicator_name in positive_tests.items():
            if test_name in test_data and test_data[test_name] == 'Positive':
                disease_indicators[indicator_name] += 1
        
        # Check for abnormal values
        try:
            if 'Glucose' in test_data and isinstance(test_data['Glucose'], (int, float)):
                if test_data['Glucose'] > 126:
                    disease_indicators['diabetes_risk'] += 1
                    
            if 'Blood Pressure (Systolic)' in test_data and isinstance(test_data['Blood Pressure (Systolic)'], (int, float)):
                if test_data['Blood Pressure (Systolic)'] > 140:
                    disease_indicators['hypertension_risk'] += 1
        except (ValueError, TypeError):
            pass
    
    def _extract_location(self, user_id: str) -> str:
        """Extract location from user ID"""
        user_id_lower = user_id.lower()
        location_mapping = {
            'lagos': 'Lagos',
            'abuja': 'Abuja', 
            'kano': 'Kano',
            'ibadan': 'Ibadan',
            'port': 'Port Harcourt',
            'kaduna': 'Kaduna'
        }
        
        for key, location in location_mapping.items():
            if key in user_id_lower:
                return location
        return 'Unknown'
    
    def _simulate_turnaround_time(self, test_type: str) -> float:
        """Simulate realistic turnaround times"""
        turnaround_map = {
            'health_score': 0.5,
            'vital_signs_monitoring': 0.25,
            'test_results': 24.0,
            'kidney_function': 4.0,
            'lipid_profile': 6.0,
            'lung_capacity': 1.0,
            'liver_function': 8.0,
        }
        return turnaround_map.get(test_type, 2.0)
