from fastapi import FastAPI
import uvicorn
import ollama
import faiss
import pickle
import numpy as np
from pydantic import BaseModel
from typing import Dict, List
from langchain_ollama import OllamaEmbeddings
from tools.tools_vector import vector_search_tool
from tools.tools_monitor_vital_signs import google_maps_tool
from tools.tools_health_score import google_calendar_tool
import asyncio

# Initialize FastAPI app
app = FastAPI()

# === MODEL CONSTANTS ===
QWEN_MODEL = "qwen2.5:1.5b"
DEEPSEEK_MODEL = "deepseek-r1:1.5b"

# === VECTOR STORE PATHS ===
VECTOR_STORE_PATHS = {
    QWEN_MODEL: {
        "index": "Vector_Store/index.faiss",
        "metadata": "Vector_Store/index.pkl"
    },
    DEEPSEEK_MODEL: {
        "index": "Vector_Store/index.faiss",
        "metadata": "Vector_Store/index.pkl"
    }
}

# === LOAD FAISS INDEXES & METADATA ===
vector_indexes = {}
vector_docs = {}
embedding_models = {}

for model_name, paths in VECTOR_STORE_PATHS.items():
    try:
        vector_indexes[model_name] = faiss.read_index(paths["index"])
        print(f"✅ FAISS index loaded for {model_name}")
    except Exception as e:
        print(f"❌ Error loading FAISS index for {model_name}: {e}")

    try:
        with open(paths["metadata"], "rb") as f:
            vector_docs[model_name] = pickle.load(f)
        print(f"✅ Metadata loaded for {model_name}")
    except Exception as e:
        print(f"❌ Error loading metadata for {model_name}: {e}")

    try:
        embedding_models[model_name] = OllamaEmbeddings(model="llama3.2:1b")
    except Exception as e:
        print(f"❌ Error loading embedding model {model_name}: {e}")

# === CHAT HISTORY CONFIGURATION ===
chat_histories: Dict[str, List[Dict[str, str]]] = {}
MAX_HISTORY_LENGTH = 10
TOP_K = 1  # Number of relevant documents to fetch

# === REQUEST MODEL ===
class ChatRequest(BaseModel):
    user_id: str
    query: str
    model: str  # ✅ Add model selection from user input

# === ASYNC FUNCTION TO RETRIEVE CONTEXT ===
async def retrieve_context(query: str, model_name: str, top_k: int = TOP_K):
    if model_name not in vector_indexes or model_name not in vector_docs:
        return ""

    embedder = embedding_models[model_name]
    query_embedding = np.array([embedder.embed_query(query)]).astype("float32")

    index = vector_indexes[model_name]
    documents = vector_docs[model_name]

    distances, indices = index.search(query_embedding, top_k)

    relevant_docs = [
        documents[idx].get("text", "") if isinstance(documents[idx], dict) else str(documents[idx])
        for idx in indices[0] if idx < len(documents)
    ]

    return " ".join(relevant_docs)

# === AGENT TOOL INVOCATION LOGIC ===
def run_tool_logic(query: str, llm_response: str) -> str:
    llm_output_lower = llm_response.lower()

    # Tool 1: Vector Search
    if "vector" in llm_output_lower:
        result = vector_search_tool.invoke({"query": query})
        return f"\n\n🔍 Extra Info:\n{result}"

    # Tool 2: Google Maps
    elif "medical facility" in llm_output_lower or "near me" in llm_output_lower:
        result = google_maps_tool.invoke(query)
        return f"\n\n📍 Nearby Facilities:\n{result}"

    # Tool 3: Google Calendar Doctor Booking
    elif "book" in llm_output_lower and "appointment" in llm_output_lower:
        result = google_calendar_tool.invoke(query)
        return f"\n\n📅 Appointment Info:\n{result}"

    return ""

# === QUERY HANDLER ===
@app.post("/query")
async def get_response(chat: ChatRequest):
    user_id, query, selected_model = chat.user_id, chat.query, chat.model

    if selected_model not in [QWEN_MODEL, DEEPSEEK_MODEL]:
        return {"error": "Invalid model selection. Choose either qwen2.5:1.5b or deepseek-r1:1.5b."}

    # Set system prompt
    system_prompt = (
        "You are Dr. Deuce, a real medical assistant that books appointments directly. Provide concise, correct recommendations "
        "with strategic plans for healthcare issues. Always respond in English."
    )

    # Retrieve relevant context
    relevant_info = await retrieve_context(query, selected_model, TOP_K)
    full_context = f"{system_prompt} {relevant_info}"

    # Add context and user message to chat history
    if user_id not in chat_histories:
        chat_histories[user_id] = [{"role": "system", "content": full_context}]

    chat_histories[user_id].append({"role": "user", "content": query})

    # Trim history
    if len(chat_histories[user_id]) > MAX_HISTORY_LENGTH:
        chat_histories[user_id] = chat_histories[user_id][-MAX_HISTORY_LENGTH:]

    try:
        response = ollama.chat(model=selected_model, messages=chat_histories[user_id])
        model_response = response["message"]["content"]

        # Run tool logic and append results
        tool_result = run_tool_logic(query, model_response)
        final_response = model_response + tool_result

        chat_histories[user_id].append({"role": "assistant", "content": final_response})

        return {"response": final_response, "chat_history": chat_histories[user_id]}

    except Exception as e:
        return {"error": f"Failed to generate response: {str(e)}"}

# === RUN SERVER ===
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)
