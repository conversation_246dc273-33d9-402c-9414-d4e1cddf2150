@echo off
echo ===================================================
echo    Dr. <PERSON>uce Health Assistant - Quick Start
echo ===================================================
echo.
echo This script will start both the server and user interface.
echo.

:: Check if Ollama is running
echo Checking if Olla<PERSON> is running...
tasklist /FI "IMAGENAME eq ollama.exe" 2>NUL | find /I /N "ollama.exe">NUL
if "%ERRORLEVEL%"=="1" (
    echo Ollama is not running. Starting Ollama...
    start "" "C:\Program Files\Ollama\ollama.exe"
    echo Waiting for Ollama to start (5 seconds)...
    timeout /t 5 /nobreak > nul
) else (
    echo Ollama is already running! ✓
)

:: Check if models are available
echo Checking if required models are available...
ollama list | find "qwen2.5:1.5b" >nul 2>&1
if %errorlevel% neq 0 (
    echo Model qwen2.5:1.5b not found. Please run setup.bat first.
    pause
    exit /b 1
)
ollama list | find "deepseek-r1:1.5b" >nul 2>&1
if %errorlevel% neq 0 (
    echo Model deepseek-r1:1.5b not found. Please run setup.bat first.
    pause
    exit /b 1
)
echo Required models are available! ✓

echo.
echo ===================================================
echo    STARTING THE SYSTEM
echo ===================================================
echo.
echo 1. Starting the server (agent_server.py)...
start cmd /k "title Dr. Deuce Server && echo Starting Dr. Deuce Health Assistant Server... && python agent_server.py"

echo 2. Waiting for server to initialize...

:: Check if server is responding before starting Streamlit
set /a max_attempts=30
set /a attempt=0
set /a wait_time=2

echo Checking if server is ready (this may take up to 60 seconds)...

:check_server
set /a attempt+=1
echo Attempt %attempt% of %max_attempts%...

:: Use curl to check if server is responding
curl -s -o nul -w "%%{http_code}" http://127.0.0.1:8000/health 2>nul | findstr "200" >nul
if %errorlevel% equ 0 (
    echo Server is ready! ✓
    goto server_ready
)

if %attempt% geq %max_attempts% (
    echo Server did not respond after %max_attempts% attempts.
    echo You may need to check if the server started correctly.
    echo Press any key to continue with starting the UI anyway...
    pause >nul
    goto server_ready
)

echo Waiting %wait_time% seconds before next attempt...
timeout /t %wait_time% /nobreak >nul
goto check_server

:server_ready
echo 3. Starting the user interface (agent_app.py)...
start cmd /k "title Dr. Deuce UI && echo Starting Dr. Deuce Health Assistant UI... && streamlit run agent_app.py"

echo.
echo ===================================================
echo    SYSTEM STARTED SUCCESSFULLY
echo ===================================================
echo.
echo The Dr. Deuce Health Assistant is now running!
echo.
echo - Server is running at: http://127.0.0.1:8000
echo - User interface is at: http://localhost:8501
echo.
echo To stop the system, close both terminal windows.
echo.
echo ===================================================
echo.
pause
