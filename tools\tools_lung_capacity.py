import json
from langchain.tools import Tool
from typing import Dict, List, Any, Optional, Union
import logging
import sys
import os

# Import peak flow calculator functions
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
try:
    from peak_flow_calculator import calculate_expected_fev1, calculate_pef
except ImportError:
    # Define fallback functions if import fails
    def calculate_expected_fev1(age, height_cm, race):
        race_factors = {
            "asian": 0.93, "latino": 0.93, "hispanic": 0.93,
            "african": 0.87, "african american": 0.87,
            "caucasian": 1, "middle eastern": 1,
        }
        race_key = race.strip().lower()
        factor = race_factors.get(race_key, 1)
        expected_fev1 = factor * 1.08 * ((0.0395 * height_cm) - (0.025 * age) - 2.6)
        expected_fvc = factor * 1.15 * ((0.0443 * height_cm) - (0.026 * age) - 2.89)
        return expected_fev1, expected_fvc

    def calculate_pef(age, height_cm, gender):
        height_m = height_cm / 100
        if age < 18:
            pef = (height_cm - 100) * 5 + 100
        else:
            gender_key = gender.strip().lower()
            if gender_key == "male":
                pef = (((height_m * 5.48) + 1.58) - (age * 0.041)) * 60
            elif gender_key == "female":
                pef = (((height_m * 3.72) + 2.24) - (age * 0.03)) * 60
            else:
                pef = (((height_m * 4.6) + 1.9) - (age * 0.035)) * 60
        return pef

def analyze_lung_capacity(data_json: str) -> str:
    """
    Analyzes spirometry data to assess lung capacity and respiratory health.
    Uses pattern recognition and rule-based triggers to identify potential respiratory issues.

    Args:
        data_json: JSON string containing spirometry data and patient information

    Returns:
        JSON string with analysis results, risk assessment, and recommendations
    """
    try:
        # Parse input data
        data = json.loads(data_json)

        # Extract spirometry parameters
        spirometry_data = data.get("data", {})

        # Initialize results
        analysis_results = []
        risk_factors = []
        recommendations = []
        respiratory_conditions = []

        # Define normal ranges for spirometry parameters
        normal_ranges = {
            "FEV1": {"range": (80, 120), "unit": "%"},  # Forced Expiratory Volume in 1 second (percent predicted)
            "FVC": {"range": (80, 120), "unit": "%"},   # Forced Vital Capacity (percent predicted)
            "FEV1_FVC_ratio": {"range": (0.7, 1.0), "unit": "ratio"},  # FEV1/FVC ratio
            "PEF": {"range": (80, 120), "unit": "%"},   # Peak Expiratory Flow (percent predicted)
            "FEF25_75": {"range": (60, 120), "unit": "%"},  # Forced Expiratory Flow at 25-75% (percent predicted)
            "TLC": {"range": (80, 120), "unit": "%"},   # Total Lung Capacity (percent predicted)
            "RV": {"range": (75, 120), "unit": "%"},    # Residual Volume (percent predicted)
            "DLCO": {"range": (80, 120), "unit": "%"},  # Diffusing capacity for carbon monoxide (percent predicted)
        }

        # Extract patient information - prioritizing the core parameters you specified
        age = spirometry_data.get("Age", spirometry_data.get("age", 0))
        sex = spirometry_data.get("Sex", spirometry_data.get("sex", ""))
        gender = "male" if sex.lower() == "male" else "female"  # Normalize for peak flow calculation
        height = spirometry_data.get("Height", spirometry_data.get("height", 170))  # Height in cm
        weight = spirometry_data.get("Weight", spirometry_data.get("weight", 0))  # Weight in kg
        ethnicity = spirometry_data.get("Ethnicity", spirometry_data.get("ethnicity", spirometry_data.get("Race", "caucasian")))
        smoker = spirometry_data.get("Smoker", spirometry_data.get("smoker", spirometry_data.get("Smoking_Status", "")))

        # Normalize smoker status
        if isinstance(smoker, bool):
            smoking_status = "current smoker" if smoker else "non-smoker"
        elif isinstance(smoker, str):
            smoking_status = smoker.lower()
        else:
            smoking_status = "unknown"

        # Extract FEV1 - the key measurement you specified
        fev1 = spirometry_data.get("FEV1", spirometry_data.get("fev1", spirometry_data.get("Fev1", None)))
        fev1 = None if fev1 == "Unknown" or fev1 == "" else fev1

        # Convert FEV1 to float if it's a string
        if fev1 is not None:
            try:
                fev1 = float(fev1)
            except (ValueError, TypeError):
                fev1 = None

        # Calculate BMI if height and weight are available
        bmi = None
        if height > 0 and weight > 0:
            height_m = height / 100  # Convert cm to meters
            bmi = weight / (height_m ** 2)

        # Validate core parameters
        core_params_available = {
            "Age": age > 0,
            "Sex": sex != "",
            "Height": height > 0,
            "Weight": weight > 0,
            "Ethnicity": ethnicity != "",
            "Smoker": smoking_status != "unknown",
            "FEV1": fev1 is not None
        }

        missing_core_params = [param for param, available in core_params_available.items() if not available]

        fvc = spirometry_data.get("FVC", None)    # As percentage of predicted
        fvc = None if fvc == "Unknown" else fvc

        fev1_fvc_ratio = spirometry_data.get("FEV1_FVC_ratio", None)
        fev1_fvc_ratio = None if fev1_fvc_ratio == "Unknown" else fev1_fvc_ratio

        pef = spirometry_data.get("PEF", None)    # As percentage of predicted
        pef = None if pef == "Unknown" else pef

        fef25_75 = spirometry_data.get("FEF25_75", None)
        fef25_75 = None if fef25_75 == "Unknown" else fef25_75

        # Calculate expected values using peak flow calculator if height and age are provided
        expected_values = {}
        if height > 0 and age > 0:
            try:
                expected_fev1, expected_fvc = calculate_expected_fev1(age, height, ethnicity)
                expected_pef = calculate_pef(age, height, gender)

                # Store expected values for later use
                expected_values = {
                    "FEV1": expected_fev1,
                    "FVC": expected_fvc,
                    "PEF": expected_pef
                }

                # Enhanced analysis with absolute values and percentage calculations
                if fev1 is not None and fev1 > 0:
                    if fev1 <= 200:  # Assume it's a percentage
                        absolute_fev1 = (fev1 / 100) * expected_fev1
                        analysis_results.append(f"FEV1: {fev1}% of predicted ({absolute_fev1:.2f}L actual vs {expected_fev1:.2f}L expected)")

                        # Add severity assessment based on percentage
                        if fev1 >= 80:
                            analysis_results.append("✅ FEV1 is within normal range")
                        elif fev1 >= 70:
                            analysis_results.append("⚠️ FEV1 shows mild reduction")
                        elif fev1 >= 60:
                            analysis_results.append("🟡 FEV1 shows moderate reduction")
                        elif fev1 >= 50:
                            analysis_results.append("🟠 FEV1 shows moderately severe reduction")
                        else:
                            analysis_results.append("🔴 FEV1 shows severe reduction")
                    else:  # Assume it's an absolute value
                        percentage = (fev1 / expected_fev1) * 100
                        analysis_results.append(f"FEV1: {fev1:.2f}L ({percentage:.1f}% of predicted {expected_fev1:.2f}L)")

                if fvc is not None and fvc > 0:
                    if fvc <= 200:  # Assume it's a percentage
                        absolute_fvc = (fvc / 100) * expected_fvc
                        analysis_results.append(f"FVC: {fvc}% of predicted ({absolute_fvc:.2f}L actual vs {expected_fvc:.2f}L expected)")

                        # Add severity assessment
                        if fvc >= 80:
                            analysis_results.append("✅ FVC is within normal range")
                        elif fvc >= 70:
                            analysis_results.append("⚠️ FVC shows mild reduction")
                        else:
                            analysis_results.append("🟡 FVC shows significant reduction")
                    else:  # Assume it's an absolute value
                        percentage = (fvc / expected_fvc) * 100
                        analysis_results.append(f"FVC: {fvc:.2f}L ({percentage:.1f}% of predicted {expected_fvc:.2f}L)")

                if pef is not None and pef > 0:
                    if pef <= 200:  # Assume it's a percentage
                        absolute_pef = (pef / 100) * expected_pef
                        analysis_results.append(f"PEF: {pef}% of predicted ({absolute_pef:.0f}L/min actual vs {expected_pef:.0f}L/min expected)")

                        # Add severity assessment
                        if pef >= 80:
                            analysis_results.append("✅ Peak flow is within normal range")
                        else:
                            analysis_results.append("⚠️ Peak flow is reduced - may indicate airway obstruction")
                    else:  # Assume it's an absolute value
                        percentage = (pef / expected_pef) * 100
                        analysis_results.append(f"PEF: {pef:.0f}L/min ({percentage:.1f}% of predicted {expected_pef:.0f}L/min)")

                # Add comprehensive demographic insights
                demographic_info = f"📊 Expected values calculated for {age}-year-old {gender.lower()}, {ethnicity} ethnicity, height {height}cm"
                if weight > 0:
                    demographic_info += f", weight {weight}kg"
                    if bmi:
                        bmi_category = "underweight" if bmi < 18.5 else "normal" if bmi < 25 else "overweight" if bmi < 30 else "obese"
                        demographic_info += f" (BMI: {bmi:.1f} - {bmi_category})"
                analysis_results.append(demographic_info)

                # Add smoking status insight
                if smoking_status != "unknown":
                    smoking_emoji = "🚭" if "non" in smoking_status else "🚬"
                    analysis_results.append(f"{smoking_emoji} Smoking status: {smoking_status}")

                # Add ethnicity-specific information
                ethnicity_factors = {
                    "asian": 0.93, "latino": 0.93, "hispanic": 0.93,
                    "african": 0.87, "african american": 0.87, "black": 0.87,
                    "caucasian": 1.0, "white": 1.0, "middle eastern": 1.0
                }
                ethnicity_factor = ethnicity_factors.get(ethnicity.lower(), 1.0)
                if ethnicity_factor != 1.0:
                    analysis_results.append(f"🧬 Ethnicity adjustment factor of {ethnicity_factor} applied for {ethnicity} background")

            except Exception as e:
                logging.warning(f"Error calculating expected values: {str(e)}")
                analysis_results.append("⚠️ Unable to calculate expected values - analysis based on general population norms")

        # Analyze additional lung capacity parameters if available
        tlc = spirometry_data.get("TLC", None)
        rv = spirometry_data.get("RV", None)
        dlco = spirometry_data.get("DLCO", None)

        # Add analysis for additional parameters
        if tlc is not None and tlc != "Unknown":
            if tlc < 80:
                analysis_results.append(f"🫁 TLC (Total Lung Capacity): {tlc}% - reduced, suggesting restrictive disease")
                risk_factors.append("Reduced TLC")
            else:
                analysis_results.append(f"✅ TLC (Total Lung Capacity): {tlc}% - within normal range")

        if rv is not None and rv != "Unknown":
            if rv > 120:
                analysis_results.append(f"🫁 RV (Residual Volume): {rv}% - elevated, suggesting air trapping")
                risk_factors.append("Elevated RV")
            elif rv < 75:
                analysis_results.append(f"🫁 RV (Residual Volume): {rv}% - reduced")
                risk_factors.append("Reduced RV")
            else:
                analysis_results.append(f"✅ RV (Residual Volume): {rv}% - within normal range")

        if dlco is not None and dlco != "Unknown":
            if dlco < 80:
                analysis_results.append(f"🫁 DLCO (Diffusion Capacity): {dlco}% - reduced, may indicate gas exchange impairment")
                risk_factors.append("Reduced DLCO")
            else:
                analysis_results.append(f"✅ DLCO (Diffusion Capacity): {dlco}% - within normal range")

        # Track missing critical parameters
        missing_parameters = []
        critical_params = ["FEV1", "FVC", "FEV1_FVC_ratio"]
        for param in critical_params:
            if param not in spirometry_data or spirometry_data[param] is None or spirometry_data[param] == "Unknown":
                missing_parameters.append(param)

        # Enhanced FEV1 analysis - the core parameter you specified
        if fev1 is not None:
            # Convert to percentage if it's an absolute value
            fev1_percent = fev1 if fev1 <= 200 else (fev1 / expected_values.get("FEV1", 1)) * 100 if expected_values else fev1

            if fev1_percent < 80:
                severity = "mild" if fev1_percent >= 70 else "moderate" if fev1_percent >= 60 else "moderately severe" if fev1_percent >= 50 else "severe" if fev1_percent >= 35 else "very severe"
                analysis_results.append(f"🚨 FEV1 indicates {severity} airflow limitation ({fev1_percent:.1f}% of predicted)")
                risk_factors.append("Reduced FEV1")

                # Add comprehensive age-specific insights
                if age and age < 40:
                    analysis_results.append("⚠️ Reduced FEV1 at young age suggests possible asthma or congenital condition")
                    if "current" in smoking_status:
                        analysis_results.append("🚬 Early smoking damage - high priority for cessation")
                elif age and age > 65:
                    analysis_results.append("📊 Age-related decline may contribute to reduced FEV1")
                    if fev1_percent < 60:
                        analysis_results.append("👴 Significant decline in older adult - consider COPD evaluation")

                # Add BMI-related insights for FEV1
                if bmi:
                    if bmi >= 30:
                        analysis_results.append("⚖️ Obesity (BMI ≥30) may contribute to reduced lung function")
                        recommendations.append("🏃 Weight reduction may improve respiratory function")
                    elif bmi < 18.5:
                        analysis_results.append("⚖️ Underweight status may indicate respiratory muscle weakness")

                # Add smoking-specific FEV1 insights
                if "current" in smoking_status:
                    analysis_results.append("🚬 Current smoking is likely contributing to FEV1 reduction")
                    if fev1_percent < 50:
                        analysis_results.append("🔴 URGENT: Severe FEV1 reduction in smoker - immediate cessation critical")
                elif "former" in smoking_status:
                    analysis_results.append("🚭 Previous smoking may have contributed to current FEV1 reduction")
            else:
                analysis_results.append(f"✅ FEV1: {fev1_percent:.1f}% of predicted - within normal range")
                if "current" in smoking_status:
                    analysis_results.append("⚠️ Normal FEV1 despite smoking - cessation now prevents future decline")

        if fvc is not None:
            # Convert to percentage if it's an absolute value
            fvc_percent = fvc if fvc <= 200 else (fvc / expected_values.get("FVC", 1)) * 100 if expected_values else fvc

            if fvc_percent < 80:
                severity = "mild" if fvc_percent >= 70 else "moderate" if fvc_percent >= 60 else "severe" if fvc_percent < 60 else ""
                analysis_results.append(f"🚨 FVC indicates {severity} restrictive pattern")
                risk_factors.append("Reduced FVC")

                # Gender-specific insights
                if gender.lower() == "female" and fvc_percent < 70:
                    analysis_results.append("👩 Significantly reduced FVC in females may indicate respiratory muscle weakness")
                elif gender.lower() == "male" and fvc_percent < 70:
                    analysis_results.append("👨 Significantly reduced FVC in males may indicate chest wall restriction")

        if fev1_fvc_ratio is not None:
            if fev1_fvc_ratio < 0.7:
                analysis_results.append(f"🚨 FEV1/FVC ratio: {fev1_fvc_ratio:.2f} - indicates obstructive lung disease")
                risk_factors.append("Low FEV1/FVC ratio")
                respiratory_conditions.append("Obstructive lung disease")

                # Enhanced COPD vs Asthma differentiation using peak flow insights
                if smoking_status and ("current" in smoking_status.lower() or "former" in smoking_status.lower()):
                    respiratory_conditions.append("Possible COPD")
                    if age and age > 40:
                        analysis_results.append("🚬 Pattern consistent with COPD: age >40 + smoking history + obstruction")
                        if fev1 and fev1 < 50:
                            analysis_results.append("🔴 Severe COPD pattern - immediate pulmonary consultation recommended")
                    else:
                        analysis_results.append("⚠️ Early-onset COPD possible - consider alpha-1 antitrypsin deficiency")
                else:
                    respiratory_conditions.append("Possible asthma")
                    analysis_results.append("🫁 Pattern may indicate asthma - consider bronchodilator response testing")
                    if age and age < 30:
                        analysis_results.append("👶 Young age supports asthma diagnosis over COPD")

                # Add severity grading based on FEV1/FVC ratio
                if fev1_fvc_ratio < 0.5:
                    analysis_results.append("🔴 Severe obstruction (FEV1/FVC < 0.5)")
                elif fev1_fvc_ratio < 0.6:
                    analysis_results.append("🟠 Moderate-severe obstruction")
                else:
                    analysis_results.append("🟡 Mild-moderate obstruction")

            elif fev1_fvc_ratio > 0.9 and fvc and fvc < 80:
                analysis_results.append(f"📊 FEV1/FVC ratio: {fev1_fvc_ratio:.2f} with reduced FVC - suggests restrictive disease")
                respiratory_conditions.append("Possible restrictive lung disease")

                # Add restrictive disease insights
                if fvc and fvc < 60:
                    analysis_results.append("🔴 Severe restriction - consider interstitial lung disease")
                elif tlc and tlc < 70:
                    analysis_results.append("🟠 Confirmed restrictive pattern with reduced TLC")

            else:
                analysis_results.append(f"✅ FEV1/FVC ratio: {fev1_fvc_ratio:.2f} - within normal range (≥0.70)")

        # Enhanced PEF analysis using peak flow calculator insights
        if pef is not None:
            # Convert to percentage if it's an absolute value
            pef_percent = pef if pef <= 200 else (pef / expected_values.get("PEF", 1)) * 100 if expected_values else pef

            if pef_percent < 80:
                analysis_results.append(f"🚨 Peak Flow: {pef_percent:.0f}% of predicted - indicates airway obstruction")
                risk_factors.append("Reduced PEF")

                # Add PEF-specific insights
                if pef_percent < 50:
                    analysis_results.append("🔴 Severely reduced peak flow - may indicate acute exacerbation")
                elif pef_percent < 60:
                    analysis_results.append("🟠 Significantly reduced peak flow - poor asthma control if asthmatic")

                # Gender and age-specific PEF insights
                if gender.lower() == "male" and age and age > 50:
                    analysis_results.append("👨 Reduced PEF in older males often indicates COPD progression")
                elif gender.lower() == "female" and age and age < 40:
                    analysis_results.append("👩 Reduced PEF in young females often suggests asthma")
            else:
                analysis_results.append(f"✅ Peak Flow: {pef_percent:.0f}% of predicted - within normal range")

        if fef25_75 is not None:
            if fef25_75 < 60:
                analysis_results.append(f"🚨 FEF25-75: {fef25_75}% - indicates small airway dysfunction")
                risk_factors.append("Reduced FEF25-75")

                if fev1_fvc_ratio and fev1_fvc_ratio >= 0.7:
                    analysis_results.append("⚠️ Normal FEV1/FVC with reduced FEF25-75 suggests early small airway disease")
                    if smoking_status and "current" in smoking_status.lower():
                        analysis_results.append("🚬 Early smoking-related small airway damage detected")
            else:
                analysis_results.append(f"✅ FEF25-75: {fef25_75}% - small airways functioning normally")

        # Determine overall respiratory risk level
        risk_level = "Low"
        if len(risk_factors) >= 3 or "Low FEV1/FVC ratio" in risk_factors:
            risk_level = "High"
        elif len(risk_factors) >= 1:
            risk_level = "Moderate"

        # Generate enhanced recommendations based on findings and peak flow insights
        if "Obstructive lung disease" in respiratory_conditions:
            if "Possible COPD" in respiratory_conditions:
                recommendations.append("🫁 Bronchodilator response testing recommended to confirm COPD and assess reversibility")
                recommendations.append("🚭 Smoking cessation is CRITICAL - reduces disease progression by 50%")
                recommendations.append("💊 Consider bronchodilator therapy (SABA/LABA) for symptom management")
                recommendations.append("👨‍⚕️ Pulmonologist consultation for COPD staging and treatment plan")

                # COPD-specific recommendations based on severity
                if fev1 and fev1 < 50:
                    recommendations.append("🔴 Severe COPD: Consider pulmonary rehabilitation program")
                    recommendations.append("💉 Pneumonia and flu vaccinations essential")
                elif fev1 and fev1 < 80:
                    recommendations.append("🟡 Moderate COPD: Regular exercise within tolerance limits")

            elif "Possible asthma" in respiratory_conditions:
                recommendations.append("🫁 Peak flow monitoring at home to track asthma control")
                recommendations.append("🧪 Allergy testing to identify and avoid triggers")
                recommendations.append("💊 Consider controller medication (ICS) if symptoms persist")
                recommendations.append("👨‍⚕️ Asthma action plan development with healthcare provider")

                # Age-specific asthma recommendations
                if age and age < 18:
                    recommendations.append("👶 Pediatric asthma management - growth monitoring important")
                elif age and age > 65:
                    recommendations.append("👴 Late-onset asthma - rule out COPD overlap syndrome")

        if "Possible restrictive lung disease" in respiratory_conditions:
            recommendations.append("🫁 Complete pulmonary function testing with lung volumes (TLC, RV)")
            recommendations.append("🩻 High-resolution chest CT to evaluate for interstitial changes")
            recommendations.append("� Consider autoimmune markers if connective tissue disease suspected")
            recommendations.append("👨‍⚕️ Pulmonologist referral for restrictive disease workup")

            # Restrictive-specific recommendations
            if dlco and dlco < 60:
                recommendations.append("🔴 Severely reduced DLCO - urgent evaluation needed")

        # Peak flow-specific recommendations
        if pef and pef < 60:
            recommendations.append("📱 Daily peak flow monitoring recommended")
            recommendations.append("� Develop action plan for peak flow <50% of personal best")

        # Small airway disease recommendations
        if fef25_75 and fef25_75 < 60:
            recommendations.append("🫁 Small airway disease detected - avoid respiratory irritants")
            if smoking_status and "current" in smoking_status.lower():
                recommendations.append("🚭 URGENT: Smoking cessation to prevent small airway damage progression")

        if risk_level == "Low" and not respiratory_conditions:
            recommendations.append("✅ Excellent lung function - maintain with regular exercise")
            recommendations.append("🏃 Continue aerobic activities to preserve lung capacity")
            if smoking_status and "current" in smoking_status.lower():
                recommendations.append("🚭 Smoking cessation to maintain optimal lung health")

        # Enhanced general recommendations for moderate to high risk
        if risk_level in ["Moderate", "High"]:
            recommendations.append("💨 Diaphragmatic breathing exercises 10 minutes daily")
            recommendations.append("🏃 Pulmonary rehabilitation if available in your area")
            recommendations.append("😷 N95 masks in polluted environments")
            recommendations.append("🌡️ Annual flu vaccination to prevent respiratory infections")

            # Age-specific recommendations
            if age and age > 65:
                recommendations.append("� Regular pneumonia vaccination for older adults")

            # Gender-specific recommendations
            if gender.lower() == "female" and age and 20 <= age <= 40:
                recommendations.append("👩 Monitor lung function during pregnancy if planning family")

        # Determine confidence level based on core parameters availability
        core_params_score = sum(1 for available in core_params_available.values() if available)
        if core_params_score >= 6:  # Most core parameters available
            confidence_level = "High"
        elif core_params_score >= 4:  # Some core parameters available
            confidence_level = "Moderate"
        else:  # Few core parameters available
            confidence_level = "Low"

        # Create a doctor-like summary
        doctor_summary = ""

        # Add a greeting and introduction
        doctor_summary += "Hi there! I've reviewed your spirometry results, and here's what I'm seeing: "

        # Add a summary based on risk level
        if risk_level == "Low":
            doctor_summary += "Your lung function appears to be in good shape overall. "
        elif risk_level == "Moderate":
            doctor_summary += "I'm noticing some areas of concern in your lung function that we should keep an eye on. "
        elif risk_level == "High":
            doctor_summary += "I'm seeing some significant concerns with your lung function that we should address. "

        # Add a note about confidence
        if confidence_level == "High":
            doctor_summary += "The test provided comprehensive data, so I'm quite confident in this assessment."
        elif confidence_level == "Moderate":
            doctor_summary += "The test provided most of the key measurements, giving us a reasonably good picture of your lung health."
        elif confidence_level == "Low":
            doctor_summary += f"We're missing some measurements ({', '.join(missing_parameters)}), so this is a preliminary assessment."

        # Add information about potential conditions
        if respiratory_conditions and respiratory_conditions != ["No specific respiratory conditions identified"]:
            doctor_summary += " Based on these patterns, I'm seeing indicators that could be associated with "
            doctor_summary += ", ".join(respiratory_conditions) + "."
            doctor_summary += " Remember, these are preliminary findings based on your spirometry results, not a definitive diagnosis."

        # Add device recommendation prompt (following agent_app pattern)
        device_recommendation_prompt = "\n\n🛒 **Would you like to see health monitoring devices from [TurboMedics](https://www.turbomedics.com/products) based on your lung capacity results? (Yes/No)**"

        # Create final result
        result = {
            "analysis": analysis_results,
            "respiratory_risk_level": risk_level,
            "potential_conditions": respiratory_conditions if respiratory_conditions else ["No specific respiratory conditions identified"],
            "recommendations": recommendations,
            "confidence_level": confidence_level,
            "missing_parameters": missing_parameters,
            "doctor_summary": doctor_summary,
            "device_recommendation_prompt": device_recommendation_prompt,
            "test_type": "lung_capacity"
        }

        return json.dumps(result, indent=4)

    except Exception as e:
        logging.error(f"Error analyzing lung capacity: {str(e)}")
        return json.dumps({
            "error": f"Failed to analyze lung capacity: {str(e)}",
            "recommendations": ["⚠️ Unable to process spirometry data. Please consult a healthcare professional."]
        }, indent=4)

# Create the tool
lung_capacity_analyzer_tool = Tool(
    name="LungCapacityAnalyzer",
    func=analyze_lung_capacity,
    description="Analyzes spirometry data to assess lung capacity, identify respiratory risks like COPD/asthma, and provide respiratory health recommendations."
)
