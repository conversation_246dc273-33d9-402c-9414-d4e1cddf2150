from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse
import uvicorn
import ollama
import faiss
import pickle
import numpy as np
from pydantic import BaseModel
from typing import Dict, List, Optional, Any
from langchain_ollama import OllamaEmbeddings
import json
import os
import sys
import traceback
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('agent_server.log', encoding='utf-8')
    ]
)

# Add tools path to system path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), 'tools')))

# Import tools
try:
    from tools.tools_health_score import HealthScoreAnalysisTool
    from tools.tools_monitor_vital_signs import monitor_vital_signs
    from tools.tools_health_data_json import get_default_health_data
    from tools.tools_kidney_function import kidney_function_analysis_tool
    logging.info("Successfully imported health tools")
except ImportError as e:
    logging.error(f"Failed to import tools: {e}")
    sys.exit(1)

# Initialize chat histories and user data
chat_histories: Dict[str, List[Dict[str, str]]] = {}
user_data: Dict[str, Dict[str, Any]] = {}

# Initialize FastAPI app
app = FastAPI(title="Integrated Health Agent API",
              description="API that combines chat, health score analysis, vital signs monitoring, and health consultation")

# Add global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    error_msg = f"Unhandled error: {str(exc)}"
    logging.error(f"Exception: {error_msg}")
    logging.error(f"Traceback: {traceback.format_exc()}")
    return JSONResponse(
        status_code=500,
        content={"error": error_msg}
    )

# === MODEL CONSTANTS ===
QWEN_MODEL = "qwen2.5:1.5b"
DEEPSEEK_MODEL = "deepseek-r1:1.5b"
DEFAULT_MODEL = QWEN_MODEL

# === VECTOR STORE PATHS ===
VECTOR_STORE_PATHS = {
    QWEN_MODEL: {
        "index": r"C:\Users\<USER>\OneDrive\Desktop\MCP\Vector_Store\qwen2.5-1.5b\index.faiss",
        "metadata": r"C:\Users\<USER>\OneDrive\Desktop\MCP\Vector_Store\qwen2.5-1.5b\index.pkl"
    },
    DEEPSEEK_MODEL: {
        "index": r"C:\Users\<USER>\OneDrive\Desktop\MCP\Vector_Store\deepseek-r1-1.5b\index.faiss",
        "metadata": r"C:\Users\<USER>\OneDrive\Desktop\MCP\Vector_Store\deepseek-r1-1.5b\index.pkl"
    }
}

# === STORE CHAT TITLES AND HISTORIES ===
chat_titles = {}  # Dictionary to store session titles per user
chat_histories: Dict[str, List[Dict[str, str]]] = {}
MAX_HISTORY_LENGTH = 10
TOP_K = 1  # Number of relevant documents to fetch

# === LOAD FAISS INDEXES & METADATA ===
vector_indexes = {}
vector_docs = {}
embedding_models = {}

for model_name, paths in VECTOR_STORE_PATHS.items():
    try:
        vector_indexes[model_name] = faiss.read_index(paths["index"])
        logging.info(f"✅ FAISS index loaded for {model_name}")
    except Exception as e:
        logging.error(f"❌ Error loading FAISS index for {model_name}: {e}")

    try:
        with open(paths["metadata"], "rb") as f:
            vector_docs[model_name] = pickle.load(f)
        logging.info(f"✅ Metadata loaded for {model_name}")
    except Exception as e:
        logging.error(f"❌ Error loading metadata for {model_name}: {e}")

    try:
        embedding_models[model_name] = OllamaEmbeddings(model=model_name)
        logging.info(f"✅ Embedding model loaded for {model_name}")
    except Exception as e:
        logging.error(f"❌ Error loading embedding model {model_name}: {e}")

# === REQUEST MODELS ===
class ChatRequest(BaseModel):
    session_id: str
    user_id: str
    query: str
    model: str = DEFAULT_MODEL

class VitalSignsRequest(BaseModel):
    user_id: str
    vital_signs: Dict[str, float]

class HealthScoreRequest(BaseModel):
    user_id: str
    health_data: Dict[str, Any]  # Allow any type of value (string, float, etc.)

class KidneyFunctionRequest(BaseModel):
    user_id: str
    kidney_data: Dict[str, Any]  # Allow any type of value (string, float, etc.)

# === HELPER FUNCTIONS ===
def generate_chat_title(first_query: str) -> str:
    """Generate a title by extracting key words from the query."""
    words = first_query.split()[:5]  # Take the first 5 words
    return " ".join(words).title()

def retrieve_context(query: str, model_name: str, top_k: int = TOP_K):
    """Retrieve relevant context from the vector store"""
    if model_name not in vector_indexes or model_name not in vector_docs:
        logging.warning(f"Vector index or docs not found for model {model_name}")
        return ""

    try:
        # Get the embedding model
        embedder = embedding_models[model_name]

        # Generate embedding for the query
        query_embedding = np.array([embedder.embed_query(query)]).astype("float32")

        # Search the vector index
        index = vector_indexes[model_name]
        documents = vector_docs[model_name]

        _, indices = index.search(query_embedding, top_k)

        # Get the relevant documents
        relevant_docs = []
        for idx in indices[0]:
            # Convert NumPy integer to Python integer
            idx_int = int(idx) if hasattr(idx, 'item') else idx

            # Check if the index is valid
            if idx_int < len(documents):
                doc = documents[idx_int]
                if isinstance(doc, dict):
                    relevant_docs.append(doc.get("text", ""))
                else:
                    relevant_docs.append(str(doc))

        return " ".join(relevant_docs)
    except Exception as e:
        logging.error(f"Error retrieving context: {e}")
        logging.error(traceback.format_exc())
        return ""

def analyze_health_score(health_data: Dict[str, Any]) -> Dict:
    """Analyze health score data"""
    try:
        # Log the input data for debugging
        logging.info(f"Health data received: {json.dumps(health_data)}")

        # Process the health data to ensure it's in the correct format
        processed_data = {}
        for key, value in health_data.items():
            # Handle test results with string values
            if key in ["Malaria", "Hepatitis B", "Widal Test", "Voluntary Serology"]:
                # Ensure "Unknown" is preserved as "Unknown"
                if value == "Unknown" or value is None or value == "" or value == "null":
                    processed_data[key] = "Unknown"
                else:
                    processed_data[key] = value
            # Handle None values
            elif value is None or value == "" or value == "null":
                # For numeric fields, use 0.0 as default
                if key not in ["Malaria", "Hepatitis B", "Widal Test", "Voluntary Serology"]:
                    processed_data[key] = 0.0
                else:
                    processed_data[key] = "Unknown"
            # Convert numeric values to float
            else:
                try:
                    processed_data[key] = float(value)
                except (ValueError, TypeError):
                    # If conversion fails, keep the original value
                    processed_data[key] = value

        logging.info(f"Processed health data: {json.dumps(processed_data)}")

        # Initialize the health score tool
        health_score_tool = HealthScoreAnalysisTool()
        logging.info("HealthScoreAnalysisTool initialized successfully")

        # Create a custom subclass to override the generate_report method
        class CustomHealthScoreAnalysisTool(HealthScoreAnalysisTool):
            def generate_report(self, health_data: dict) -> dict:
                total_score = 0
                max_score = 0
                vitals_needing_improvement = []
                improvement_tips = []

                for key, value in health_data.items():
                    if value in [None, '', 'null']:
                        continue  # Skip missing or null fields

                    # Handle test results properly
                    if key in ["Malaria", "Widal Test", "Hepatitis B", "Voluntary Serology"]:
                        max_score += 5
                        if isinstance(value, str):
                            if value.lower() == "negative":
                                total_score += 5
                            elif value.lower() == "unknown":
                                # Don't count Unknown as needing improvement
                                pass
                            else:
                                vitals_needing_improvement.append(f"{key} (Positive)")
                                improvement_tips.append(f"Seek medical attention for {key}.")
                    # Handle other metrics using the parent class logic
                    elif key == "Weight (BMI)":
                        max_score += 10
                        if isinstance(value, (int, float)):
                            if value < 18.5:
                                vitals_needing_improvement.append(f"{key} (Low)")
                                improvement_tips.append("Gain weight to reach a healthy BMI range.")
                            elif 18.5 <= value <= 24.9:
                                total_score += 10
                            elif 25 <= value <= 29.9:
                                total_score += 5
                                vitals_needing_improvement.append(f"{key} (Moderately High)")
                                improvement_tips.append("Reduce BMI slightly through diet and exercise.")
                            else:
                                vitals_needing_improvement.append(f"{key} (High)")
                                improvement_tips.append("Reduce Weight (BMI) through proper lifestyle changes.")
                    elif key == "Glucose":
                        max_score += 10
                        if isinstance(value, (int, float)):
                            if 70 <= value <= 100:
                                total_score += 10
                            elif 100 < value <= 125:
                                total_score += 5
                                vitals_needing_improvement.append(f"{key} (Moderately High)")
                                improvement_tips.append("Monitor glucose levels and reduce sugar intake.")
                            else:
                                vitals_needing_improvement.append(f"{key} (Abnormal)")
                                improvement_tips.append("Consult a doctor about your glucose levels.")
                    elif key == "SpO2":
                        max_score += 10
                        if isinstance(value, (int, float)):
                            if value >= 95:
                                total_score += 10
                            else:
                                vitals_needing_improvement.append(f"{key} (Low)")
                                improvement_tips.append("Improve oxygen saturation with breathing exercises.")
                    elif key == "Temperature":
                        max_score += 10
                        if isinstance(value, (int, float)):
                            if 36.5 <= value <= 37.5:
                                total_score += 10
                            else:
                                vitals_needing_improvement.append(f"{key} (Abnormal)")
                                improvement_tips.append("Monitor your temperature for any signs of illness.")
                    elif key == "ECG (Heart Rate)":
                        max_score += 10
                        if isinstance(value, (int, float)):
                            if 60 <= value <= 100:
                                total_score += 10
                            else:
                                vitals_needing_improvement.append(f"{key} (Abnormal)")
                                improvement_tips.append("Monitor your heart rate and consult a doctor if irregular.")
                    elif key == "Blood Pressure (Systolic)":
                        max_score += 5
                        if isinstance(value, (int, float)):
                            if 90 <= value <= 120:
                                total_score += 5
                            else:
                                vitals_needing_improvement.append(f"{key} (Abnormal)")
                                improvement_tips.append("Monitor systolic pressure with a doctor.")
                    elif key == "Blood Pressure (Diastolic)":
                        max_score += 5
                        if isinstance(value, (int, float)):
                            if 60 <= value <= 80:
                                total_score += 5
                            else:
                                vitals_needing_improvement.append(f"{key} (Abnormal)")
                                improvement_tips.append("Monitor diastolic pressure with a doctor.")
                    elif key == "Perfusion_index":
                        max_score += 5
                        if isinstance(value, (int, float)) and 0.02 <= value <= 20:
                            total_score += 5
                        else:
                            vitals_needing_improvement.append(f"{key} (Abnormal)")
                            improvement_tips.append("Check perfusion with a professional.")
                    elif key == "Fev":
                        max_score += 5
                        if isinstance(value, (int, float)) and value >= 80:
                            total_score += 5
                        else:
                            vitals_needing_improvement.append(f"{key} (Low)")
                            improvement_tips.append("Improve respiratory function with breathing therapy.")

                # Normalize score
                final_score = round((total_score / max_score) * 100) if max_score > 0 else 0

                # Health status logic
                if final_score >= 85:
                    status = "Excellent"
                elif final_score >= 70:
                    status = "Good"
                elif final_score >= 50:
                    status = "Fair"
                else:
                    status = "Poor"

                return {
                    "Total Score": final_score,
                    "Health Status": status,
                    "Vitals Needing Improvement": ", ".join(vitals_needing_improvement) if vitals_needing_improvement else "None",
                    "Improvement Tips": ". ".join(improvement_tips) if improvement_tips else "Continue maintaining your current health practices."
                }

        # Use the custom tool to generate the report
        custom_tool = CustomHealthScoreAnalysisTool()
        result = custom_tool.generate_report(processed_data)
        logging.info(f"Health score report generated: {json.dumps(result)}")

        return result
    except Exception as e:
        error_msg = f"Failed to analyze health score: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        raise Exception(error_msg)

def process_kidney_function(data: Dict[str, Any]) -> Dict:
    """Analyze kidney function data using the tool"""
    try:
        logging.info(f"Processing kidney function data: {json.dumps(data)}")
        result = kidney_function_analysis_tool(data)

        # Generate personalized recommendations based on abnormal values
        abnormal_params = []
        for analysis_item in result.get("analysis", []):
            if "High" in analysis_item or "Low" in analysis_item:
                param = analysis_item.split(":")[0].strip()
                abnormal_params.append(param)

        personalized_recommendations = []
        if abnormal_params:
            if "eGFR" in abnormal_params:
                personalized_recommendations.append("- **For low eGFR**: Limit protein intake to reduce kidney workload. Aim for 0.8g of protein per kg of body weight daily. Reduce salt consumption to help control blood pressure. Stay well-hydrated but avoid excessive fluid intake.")
            if "Serum Creatinine" in abnormal_params:
                personalized_recommendations.append("- **For high Serum Creatinine**: Avoid creatine supplements which can increase creatinine levels. Limit red meat consumption. Stay hydrated to help your kidneys filter waste efficiently.")
            if "BUN" in abnormal_params or "Serum Urea" in abnormal_params:
                personalized_recommendations.append("- **For elevated BUN/Urea**: Reduce protein intake, especially animal proteins. Ensure adequate hydration to help flush out urea.")
            if "Serum Sodium" in abnormal_params:
                personalized_recommendations.append("- **For abnormal Sodium levels**: Consult with a doctor about appropriate fluid and salt intake. Sodium imbalances can indicate kidney dysfunction.")
            if "Serum Potassium" in abnormal_params:
                personalized_recommendations.append("- **For abnormal Potassium levels**: This can be serious and requires medical attention. Discuss with your doctor about dietary adjustments and possible medication review.")
            if "ACR" in abnormal_params:
                personalized_recommendations.append("- **For elevated ACR (albumin-to-creatinine ratio)**: This indicates protein in urine. Control blood pressure and blood sugar levels, and reduce salt intake.")

            # No general recommendations as per user request
        else:
            personalized_recommendations.append("Your kidney function parameters appear to be within normal ranges. To maintain kidney health:")
            personalized_recommendations.append("- Stay well-hydrated")
            personalized_recommendations.append("- Maintain a balanced diet low in sodium")
            personalized_recommendations.append("- Exercise regularly")
            personalized_recommendations.append("- Avoid smoking and limit alcohol consumption")
            personalized_recommendations.append("- Control blood pressure and blood sugar levels")

        personalized_recommendations.append("\n**Important**: Please consult with a healthcare professional before making any changes to your diet or lifestyle. These recommendations are based on your test results but are not a substitute for medical advice.")

        # Format output
        return {
            "analysis": "\n".join(result["analysis"]),
            "overall_health": result["overall_health"],
            "confidence_level": result["confidence_level"],
            "missing_parameters": result["missing_parameters"],
            "recommendations": "\n".join(result.get("recommendations", [])),
            "personalized_recommendations": "\n".join(personalized_recommendations),
            "abnormal_parameters": abnormal_params,
            "raw_data": data
        }
    except Exception as e:
        error_msg = f"Failed to process kidney function: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

def process_vital_signs(vital_signs: Dict[str, float]) -> Dict:
    """Process vital signs data and return analysis"""
    try:
        # Format data for the tool
        vital_signs_json = json.dumps({"data": vital_signs})
        logging.info(f"Processing vital signs: {vital_signs_json}")

        # Use the vital sign monitoring tool
        result = monitor_vital_signs(vital_signs_json)
        logging.info(f"Vital signs monitoring result: {result}")

        # Check for abnormal patterns
        alerts = []
        if vital_signs.get("Glucose") and vital_signs["Glucose"] > 140:
            alerts.append("High glucose levels detected. Consider consulting a doctor.")
        if vital_signs.get("SpO2") and vital_signs["SpO2"] < 95:
            alerts.append("Low SpO2 levels detected. Ensure proper ventilation.")
        if vital_signs.get("Heart_Rate") and vital_signs["Heart_Rate"] > 100:
            alerts.append("High heart rate detected. Practice stress management.")
        if vital_signs.get("Temperature") and vital_signs["Temperature"] > 37.5:
            alerts.append("Fever detected. Stay hydrated and consult a doctor if it persists.")

        alert_text = "\n".join(alerts) if alerts else "No abnormal patterns detected."

        return {
            "analysis": result,
            "alerts": alert_text,
            "suggest_consultation": len(alerts) > 0
        }
    except Exception as e:
        error_msg = f"Failed to process vital signs: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        raise Exception(error_msg)

def process_agent_query(query: str, user_id: str, model_name: str) -> Dict:
    """Process a query through the agent, detecting intent and using appropriate tools"""
    try:
        # Get relevant context from vector store
        context = retrieve_context(query, model_name)
        logging.info(f"Retrieved context: {context[:100]}...")

        # Detect intent from the query
        query_lower = query.lower()

        # Prepare system prompt based on detected intent
        system_prompt = "You are Dr. Deuce, a certified and authorized medical assistant. You assist users by analyzing their health data, monitoring vitals, and providing health consultations."

        # Add context to the system prompt
        if context:
            system_prompt += f"\n\nRelevant information: {context}"

        # Initialize chat history if first interaction
        if user_id not in chat_histories:
            chat_histories[user_id] = [{"role": "system", "content": system_prompt}]
            chat_titles[user_id] = generate_chat_title(query)

        # Add user query to history
        chat_histories[user_id].append({"role": "user", "content": query})

        # Keep chat history within limit
        if len(chat_histories[user_id]) > MAX_HISTORY_LENGTH:
            # Keep the system message and the most recent messages
            system_message = chat_histories[user_id][0]
            chat_histories[user_id] = [system_message] + chat_histories[user_id][-(MAX_HISTORY_LENGTH-1):]

        # Get response from Ollama
        response = ollama.chat(model=model_name, messages=chat_histories[user_id])
        model_response = response["message"]["content"]

        # Check for health-related intents
        tool_response = ""
        tools_used = []

        # Health score analysis intent
        if any(keyword in query_lower for keyword in ["health score", "analyze my health", "health analysis"]):
            tool_response += "\n\nWould you like to analyze your health score? Type 'yes' to begin."
            tools_used.append("health_score_intent")

        # Kidney function test intent
        if any(keyword in query_lower for keyword in ["kidney", "kidney function", "kidney test", "renal"]):
            tool_response += "\n\nWould you like to analyze your kidney function? Type 'yes' to begin."
            tools_used.append("kidney_function_intent")

        # Vital signs monitoring intent
        if any(keyword in query_lower for keyword in ["vital signs", "monitor vitals", "check vitals"]):
            tool_response += "\n\nWould you like to enter your vital signs for monitoring? Type 'yes' to begin."
            tools_used.append("vital_signs_intent")

        # Health consultation intent
        if any(keyword in query_lower for keyword in ["health consultation", "consult", "medical advice"]):
            tool_response += "\n\nWould you like to start a health consultation? Type 'yes' to begin."
            tools_used.append("consultation_intent")

        # Add tool response to model response if any
        if tool_response:
            model_response += tool_response

        # Add response to chat history
        chat_histories[user_id].append({"role": "assistant", "content": model_response})

        return {
            "response": model_response,
            "chat_title": chat_titles[user_id],
            "chat_history": chat_histories[user_id],
            "tools_used": tools_used
        }
    except Exception as e:
        error_msg = f"Failed to process agent query: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        raise Exception(error_msg)

# === ENDPOINTS ===
@app.get("/")
async def root():
    return {"message": "Integrated Health Agent API is running"}

@app.get("/default-health-data")
async def default_health_data():
    """Get default health data"""
    try:
        # Use the exact DEFAULT_HEALTH_DATA structure as specified
        default_data = {
            "Glucose": None,
            "SpO2": None,
            "ECG (Heart Rate)": None,
            "Blood Pressure (Systolic)": None,
            "Blood Pressure (Diastolic)": None,
            "Weight (BMI)": None,
            "Temperature": None,
            "Malaria": "Unknown",
            "Widal Test": "Unknown",
            "Hepatitis B": "Unknown",
            "Voluntary Serology": "Unknown",
            "Perfusion_index": None,
            "Waist Circumference": None,
            "Fev": None
        }

        logging.info(f"Returning default health data: {json.dumps(default_data)}")
        return default_data
    except Exception as e:
        error_msg = f"Error getting default health data: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

@app.get("/status")
async def status():
    """Check if the server is running and models are loaded"""
    models_status = {}
    for model_name in [QWEN_MODEL, DEEPSEEK_MODEL]:
        models_status[model_name] = {
            "vector_index": model_name in vector_indexes,
            "metadata": model_name in vector_docs,
            "embeddings": model_name in embedding_models
        }

    return {
        "status": "running",
        "models": models_status
    }

@app.get("/default-health-data")
async def get_default_health_data_endpoint():
    """Get default health data template"""
    try:
        # Get the default health data
        default_data = get_default_health_data()

        # Extract just the data part without the wrapper
        if "data" in default_data:
            health_data = default_data["data"]
        else:
            health_data = default_data

        # Remove score fields for the form
        clean_data = {k: v for k, v in health_data.items() if not k.endswith("_Score") and not k.endswith("_Unit")}

        return clean_data
    except Exception as e:
        error_msg = f"Error getting default health data: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

@app.post("/query")
async def get_response(chat: ChatRequest):
    """Handle chat queries through the agent"""
    try:
        user_id, query, model = chat.user_id, chat.query, chat.model

        # Validate model selection
        if model not in [QWEN_MODEL, DEEPSEEK_MODEL]:
            return {"error": f"Invalid model selection. Choose either {QWEN_MODEL} or {DEEPSEEK_MODEL}."}

        # Check if the query is about kidney function recommendations
        query_lower = query.lower()
        if any(keyword in query_lower for keyword in ["kidney recommendation", "kidney advice", "renal advice", "kidney diet", "what should i do about my kidney"]):
            # Check if we have kidney function data for this user
            if user_id in user_data and "kidney_function" in user_data[user_id]:
                kidney_result = user_data[user_id]["kidney_function"]

                # Generate personalized response based on stored kidney function data
                if "personalized_recommendations" in kidney_result:
                    response = {"response": f"Based on your kidney function test results, here are personalized recommendations:\n\n{kidney_result['personalized_recommendations']}"}

                    # Update chat history
                    if user_id not in chat_histories:
                        chat_histories[user_id] = []

                    chat_histories[user_id].append({"role": "user", "content": query})
                    chat_histories[user_id].append({"role": "assistant", "content": response["response"]})

                    return response

        # Process the query through the agent
        result = process_agent_query(query, user_id, model)

        return result
    except Exception as e:
        error_msg = f"Error processing query: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

@app.post("/vital-signs")
async def process_vital_signs_endpoint(request: VitalSignsRequest):
    """Process vital signs data"""
    try:
        user_id = request.user_id
        vital_signs = request.vital_signs

        # Process the vital signs
        result = process_vital_signs(vital_signs)

        # Update chat history if user exists
        if user_id in chat_histories:
            vital_signs_str = ", ".join([f"{k}: {v}" for k, v in vital_signs.items()])
            chat_histories[user_id].append({"role": "user", "content": f"My vital signs are: {vital_signs_str}"})

            response_content = f"I've analyzed your vital signs.\n\n{result.get('analysis', '')}"
            if result.get('alerts'):
                response_content += f"\n\nAlerts: {result['alerts']}"

            chat_histories[user_id].append({"role": "assistant", "content": response_content})

        return result
    except Exception as e:
        error_msg = f"Error processing vital signs: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

@app.post("/health-score")
async def analyze_health_score_endpoint(request: HealthScoreRequest):
    """Analyze health score data"""
    try:
        user_id = request.user_id
        health_data = request.health_data

        # Validate health data
        if not health_data:
            error_msg = "No health data provided"
            logging.error(error_msg)
            return {"error": error_msg}

        # Analyze the health score
        result = analyze_health_score(health_data)

        # Format the result for display
        formatted_result = "\n".join([f"**{k}**: {v}" for k, v in result.items()])

        # Update chat history if user exists
        if user_id in chat_histories:
            chat_histories[user_id].append({"role": "user", "content": "Please analyze my health score."})

            response_content = f"I've analyzed your health score.\n\n{formatted_result}"
            chat_histories[user_id].append({"role": "assistant", "content": response_content})

        return {
            "analysis": formatted_result,
            "score": result.get("Total Score", 0),
            "category": result.get("Health Status", "Unknown")
        }
    except Exception as e:
        error_msg = f"Error analyzing health score: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

@app.post("/kidney-function")
async def analyze_kidney_function_endpoint(request: KidneyFunctionRequest):
    """Analyze kidney function data"""
    try:
        user_id = request.user_id
        kidney_data = request.kidney_data

        if not kidney_data:
            error_msg = "No kidney data provided"
            logging.error(error_msg)
            return {"error": error_msg}

        # Run analysis tool
        result = process_kidney_function(kidney_data)

        # Store kidney function data for this user
        if user_id not in user_data:
            user_data[user_id] = {}
        user_data[user_id]["kidney_function"] = result

        # Format for chat memory (optional)
        if user_id in chat_histories:
            chat_histories[user_id].append({"role": "user", "content": "Please analyze my kidney function."})

            formatted_result = "\n".join(result.get("analysis", []))
            summary = f"🔹 Kidney Function Analysis:\n\n{formatted_result}\n\n"
            summary += f"Based on the available test results, here is my preliminary assessment:\n\n"
            summary += f"🔹 Findings:\n{result.get('overall_health', 'Unknown')}\n\n"
            summary += f"🔹 Confidence Level: {result.get('confidence_level', 'Unknown')} "
            if result.get("missing_parameters"):
                summary += f"(Due to missing parameters: {', '.join(result['missing_parameters'])})\n\n"
                summary += f"Some parameters necessary for a more complete analysis were not provided, which may affect the accuracy of this assessment.\n\n"
                if result.get("recommendations"):
                    summary += f"{result.get('recommendations')}\n\n"
            else:
                summary += "(Due to complete data)\n\n"

            # Add personalized recommendations
            if result.get("personalized_recommendations"):
                summary += f"🔹 Personalized Recommendations:\n{result.get('personalized_recommendations')}\n\n"
                summary += "You can ask me for more specific recommendations based on your test results at any time."

            chat_histories[user_id].append({"role": "assistant", "content": summary})

        return result

    except Exception as e:
        error_msg = f"Error analyzing kidney function: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

# === RUN SERVER ===
if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=8000)
