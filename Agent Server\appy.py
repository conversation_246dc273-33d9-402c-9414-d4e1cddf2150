import streamlit as st
import requests

# Styling
st.markdown("""
<style>
    .main {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    .sidebar .sidebar-content {
        background-color: #2d2d2d;
    }
    .stTextInput textarea {
        color: #ffffff !important;
    }
    .stSelectbox div[data-baseweb="select"] {
        color: white !important;
        background-color: #3d3d3d !important;
    }
    .stSelectbox svg {
        fill: white !important;
    }
    .stSelectbox option {
        background-color: #2d2d2d !important;
        color: white !important;
    }
    div[role="listbox"] div {
        background-color: #2d2d2d !important;
        color: white !important;
    }
</style>
""", unsafe_allow_html=True)

st.title("🧠 Dr Deuce")
st.caption("🚀 Your AI Assistant On Healthcare Issues")

# Sidebar
with st.sidebar:
    st.header("⚙️ Configuration")
    selected_model = st.selectbox(
        "Choose Model",
        ["deepseek-r1:1.5b", "qwen2.5:1.5b"],
        index=0
    )

# Session state management
if "message_log" not in st.session_state:
    st.session_state.message_log = [{"role": "ai", "content": "Hi! I'm Dr Deuce. How can I help you today?"}]

# Chat UI
chat_container = st.container()
with chat_container:
    for message in st.session_state.message_log:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])

# Chat input field
user_query = st.chat_input("Ask a health-related question here...")

# Call to MCP server
def query_mcp_server(query, model, user_id="Dr Deuce"):
    try:
        response = requests.post(
            "http://localhost:8004/query",
            json={
                "user_id": user_id,
                "query": query,
                "model": selected_model  # ✅ Send model selection
            },
            timeout=320
        )

        response.raise_for_status()
        data = response.json()
        return data.get("response", "⚠️ No response received from MCP server.")
    except requests.exceptions.RequestException as e:
        return f"❌ MCP Server Error: {e}"

# 📥 Process new user input
if user_query:
    # Save user message
    st.session_state.message_log.append({"role": "user", "content": user_query})

    # Process AI response
    with st.spinner(f"🧠 Processing using {selected_model}..."):
        ai_response = query_mcp_server(user_query, selected_model)

    # Save assistant response
    st.session_state.message_log.append({"role": "ai", "content": ai_response})

    # Refresh display
    st.rerun()
