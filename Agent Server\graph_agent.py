from langchain_core.runnables import <PERSON>nableLambda
from langgraph.graph import StateGraph, END
import json
import os
import sys
from dotenv import load_dotenv
from langchain_core.prompts import ChatPromptTemplate
from langchain_ollama import ChatOllama
from pydantic import BaseModel

# === Add custom tools path ===
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), 'tools')))
from tools.tools_vector import vector_search_tool
from tools.tools_health_score import health_score_analysis_tool
from tools.tools_monitor_vital_signs import vital_sign_monitoring_tool
from tools.tools_health_consult import automated_health_consultation_tool
from tools.tools_health_data_json import get_default_health_data

# === Load environment variables ===
load_dotenv()

# === MODEL SELECTION ===
model_choice = input("🧠 Choose a model (deepseek-r1:1.5b or qwen2.5:1.5b): ").strip()
llm = ChatOllama(model=model_choice)

# === PROMPT TEMPLATE ===
prompt = ChatPromptTemplate.from_messages([
    ("system", 
     "You are <PERSON>. <PERSON><PERSON>, a certified and authorized medical assistant. "
     "You assist users by analyzing their health data, monitoring vitals, and providing health consultations. "
     "Engage users in a friendly, conversational tone. Ask relevant follow-up questions when needed. "
     "Be concise, empathetic, and helpful. Always respond in English."),
    ("human", "{question}")
])

# === STATE MODEL ===
class GraphState(BaseModel):
    question: str
    llm_output: str = ""
    final_response: str = ""
    tools_used: list[str] = []
    health_data: dict = {}
    appointment_link: str = ""

# === NODE 1: LLM PROCESSING ===
def run_llm(state: GraphState) -> GraphState:
    chain = prompt | llm
    result = chain.invoke({"question": state.question})
    return GraphState(
        question=state.question,
        llm_output=result.content,
        tools_used=[],
        health_data=state.health_data
    )

# === NODE 2: TOOL HANDLING ===
def run_tool_logic(state: GraphState) -> GraphState:
    llm_output_lower = state.llm_output.lower()
    health_data_json = json.dumps(get_default_health_data())
    
    if "health score" in llm_output_lower:
        result = health_score_analysis_tool.invoke(health_data_json)
        state.llm_output += f"\n\n📊 Health Score Analysis:\n{result}"
        state.tools_used.append("health_score_tool")
    elif "vital signs" in llm_output_lower or "monitor" in llm_output_lower:
        result = vital_sign_monitoring_tool.invoke(state.question)
        state.llm_output += f"\n\n🩺 Vital Signs Monitoring:\n{result}"
        state.tools_used.append("vital_monitor_tool")
    elif "consult" in llm_output_lower or "doctor advice" in llm_output_lower:
        result = automated_health_consultation_tool.invoke(state.question)
        state.llm_output += f"\n\n👨‍⚕️ Health Consultation:\n{result}"
        state.tools_used.append("health_consult_tool")
    elif "vector" in llm_output_lower:
        result = vector_search_tool.invoke({"query": state.question})
        state.llm_output += f"\n\n🔍 Extra Info:\n{result}"
        state.tools_used.append("vector_search_tool")
    
    return state

# === NODE 3: CHECK CRITICAL HEALTH STATUS ===
def check_health_risk(state: GraphState) -> GraphState:
    health_score = state.health_data.get("Total_Health_Score", 100)
    critical_vitals = [
        (state.health_data.get("SpO2", 100) < 90, "Your oxygen levels are low."),
        (state.health_data.get("Blood Pressure (Systolic)", 120) > 140, "Your blood pressure is high."),
        (state.health_data.get("ECG (Heart Rate)", 70) > 100, "Your heart rate is above normal.")
    ]
    
    critical_issues = [msg for is_critical, msg in critical_vitals if is_critical]
    
    if health_score < 50 or critical_issues:
        state.llm_output += "\n\n🚨 It looks like your health score is low or some vitals are critical. "
        state.llm_output += "Would you like to book a doctor's appointment? (yes/no)"
    
    return state

# === NODE 4: APPOINTMENT HANDLING ===
def handle_appointment(state: GraphState) -> GraphState:
    if "yes" in state.question.lower():
        state.appointment_link = "https://app.turbomedics.com/patient/appointment"
        state.llm_output += f"\n\n📅 You can book an appointment here: {state.appointment_link}"
    return state

# === NODE 5: OUTPUT PARSE ===
def parse_output(state: GraphState) -> GraphState:
    return GraphState(
        question=state.question,
        llm_output=state.llm_output,
        final_response=state.llm_output,
        tools_used=state.tools_used,
        health_data=state.health_data,
        appointment_link=state.appointment_link
    )

# === BUILD LANGGRAPH ===
graph = StateGraph(GraphState)
graph.add_node("llm", RunnableLambda(run_llm))
graph.add_node("tool_logic", RunnableLambda(run_tool_logic))
graph.add_node("check_health_risk", RunnableLambda(check_health_risk))
graph.add_node("handle_appointment", RunnableLambda(handle_appointment))
graph.add_node("parse", RunnableLambda(parse_output))

graph.set_entry_point("llm")
graph.add_edge("llm", "tool_logic")
graph.add_edge("tool_logic", "check_health_risk")
graph.add_edge("check_health_risk", "handle_appointment")
graph.add_edge("handle_appointment", "parse")
graph.add_edge("parse", END)

app = graph.compile()

# === CONVERSATIONAL FLOW ===
print("\n💬 Welcome to Dr. Deuce! Let's check your health today.")
state = GraphState(question="", health_data=get_default_health_data())

while True:
    user_input = input("👤 You: ").strip().lower()
    if user_input in ["exit", "quit"]:
        print("\n👋 Dr. Deuce: Take care! See you next time.")
        break
    
    state.question = user_input
    result = app.invoke(state)
    state = GraphState(**result)
    
    print(f"\n🩺 Dr. Deuce: {state.final_response}\n")
