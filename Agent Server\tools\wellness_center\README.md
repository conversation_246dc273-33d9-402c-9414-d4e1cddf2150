# Wellness Center Tools

This folder contains specialized tools for wellness center management and analytics.

## Lab Insights Dashboard

### Overview
The Lab Insights Dashboard is a centralized analytics tool for admins and doctors to monitor lab usage and health trends across the platform.

### Features

#### 📊 Analytics Provided
- **Top 10 Most Requested Tests**: Shows which tests are most popular
- **Average Turnaround Time**: Monitors lab efficiency
- **Disease Trend Indicators**: Tracks patterns in disease indicators (e.g., rise in malaria cases)
- **Geographic Test Distribution**: Shows test distribution by location
- **Insights and Recommendations**: AI-generated insights based on data patterns

#### 🔍 Filtering Options
- **Date Range**: Analyze data for specific time periods (default: 30 days)
- **Test Type Filter**: Focus on specific test types (health_score, kidney_function, etc.)
- **User Group Filter**: Filter by specific user groups (future enhancement)

### API Endpoint

```http
POST /wellness-center/lab-insights-dashboard
```

#### Request Body
```json
{
  "admin_id": "admin_doctor_001",
  "date_range_days": 30,
  "test_type_filter": null,
  "user_group_filter": null
}
```

#### Response Structure
```json
{
  "success": true,
  "dashboard_data": {
    "dashboard_generated": "2025-07-07T16:26:00",
    "analysis_period_days": 30,
    "filters_applied": {
      "test_type": null,
      "user_group": null
    },
    "summary_statistics": {
      "total_test_requests": 150,
      "unique_tests_requested": 8,
      "active_users": 25,
      "average_turnaround_hours": 4.5
    },
    "top_requested_tests": [
      {
        "test_name": "health_score",
        "request_count": 45,
        "percentage_of_total": 30.0
      }
    ],
    "disease_trend_indicators": [
      {
        "condition": "malaria_positive",
        "case_count": 5,
        "percentage": 15.0,
        "trend_status": "Normal"
      }
    ],
    "geographic_distribution": [
      {
        "location": "Lagos",
        "test_count": 75,
        "percentage_of_total": 50.0
      }
    ],
    "insights_and_recommendations": [
      "Most requested test: health_score (45 requests)",
      "Excellent turnaround times - lab operating efficiently"
    ]
  },
  "message": "Lab insights dashboard generated successfully for 30 days of data"
}
```

### Usage Examples

#### 1. Basic Dashboard Request
```python
import requests

payload = {
    "admin_id": "admin_001",
    "date_range_days": 30
}

response = requests.post(
    "http://localhost:8002/wellness-center/lab-insights-dashboard",
    json=payload
)

dashboard = response.json()
```

#### 2. Filtered Dashboard (Specific Test Type)
```python
payload = {
    "admin_id": "admin_001",
    "date_range_days": 7,
    "test_type_filter": "kidney_function"
}

response = requests.post(
    "http://localhost:8002/wellness-center/lab-insights-dashboard",
    json=payload
)
```

#### 3. Extended Analysis Period
```python
payload = {
    "admin_id": "admin_001",
    "date_range_days": 90  # 3 months of data
}

response = requests.post(
    "http://localhost:8002/wellness-center/lab-insights-dashboard",
    json=payload
)
```

### Testing

Run the test script to see the dashboard in action:

```bash
cd Agent Server/tools/wellness_center
python test_lab_insights.py
```

The test script will:
1. Create sample health data
2. Generate dashboard analytics
3. Show filtered results
4. Display key insights and trends

### Data Sources

The dashboard analyzes data from:
- **Health Score Assessments**: Basic vital signs and health metrics
- **Test Results**: Lab test results (Malaria, Widal, Hepatitis B, etc.)
- **Kidney Function Tests**: Creatinine, BUN, eGFR values
- **Lipid Profiles**: Cholesterol and triglyceride levels
- **Lung Capacity Tests**: Spirometry data
- **Liver Function Tests**: LFT parameters
- **Vital Signs Monitoring**: Continuous monitoring data

### Key Metrics Tracked

#### Test Volume Metrics
- Total test requests
- Tests per day/week/month
- Peak testing periods

#### Efficiency Metrics
- Average turnaround time by test type
- Lab capacity utilization
- Processing bottlenecks

#### Health Trend Metrics
- Disease indicator patterns
- Geographic health trends
- Risk factor prevalence

#### Quality Metrics
- Test completion rates
- Result accuracy indicators
- Patient satisfaction metrics

### Future Enhancements

1. **Real-time Dashboard**: Live updates as new data comes in
2. **Predictive Analytics**: Forecast test demand and health trends
3. **Alert System**: Automated alerts for concerning trends
4. **Export Functionality**: PDF/Excel export of dashboard reports
5. **User Group Analytics**: Detailed analysis by patient demographics
6. **Cost Analysis**: Financial metrics and cost-per-test analytics

### Integration Notes

- The dashboard integrates with the existing `user_health_data` storage
- Compatible with all current health assessment tools
- Supports both individual test data and batch vital signs monitoring
- Handles different data formats (dict and list structures)

### Security Considerations

- Admin authentication required for dashboard access
- Data anonymization for privacy protection
- Audit logging of dashboard access
- Role-based access control for different analytics levels
