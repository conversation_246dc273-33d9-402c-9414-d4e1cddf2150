"""
Test script for Lab Insights Dashboard

This script demonstrates how to use the Lab Insights Dashboard tool
with sample data to generate analytics.
"""

import json
import sys
import os
from datetime import datetime, timedelta

# Add the parent directory to the path to import the dashboard tool
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from wellness_center.lab_insights_dashboard import generate_lab_insights_dashboard

def create_sample_health_data():
    """Create sample health data for testing the dashboard"""
    
    # Sample user health data structure
    sample_data = {}
    
    # Create sample users with different types of health data
    users = [
        ("user_lagos_001", "Lagos"),
        ("user_abuja_002", "Abuja"), 
        ("user_kano_003", "Kano"),
        ("user_lagos_004", "Lagos"),
        ("user_abuja_005", "Abuja")
    ]
    
    for user_id, location in users:
        user_key = (user_id, "default")
        sample_data[user_key] = {}
        
        # Add health score data
        sample_data[user_key]["health_score"] = {
            "data": {
                "Glucose": 95.0,
                "SpO2": 98,
                "ECG (Heart Rate)": 72,
                "Blood Pressure (Systolic)": 120,
                "Blood Pressure (Diastolic)": 80,
                "Weight (BMI)": 23.5,
                "Temperature": 98.6,
                "Malaria": "Negative" if user_id.endswith("001") else "Positive",
                "Widal Test": "Negative",
                "Hepatitis B": "Negative",
                "Voluntary Serology": "Negative"
            },
            "timestamp": (datetime.now() - timedelta(days=5)).isoformat()
        }
        
        # Add test results
        sample_data[user_key]["test_results"] = {
            "data": {
                "Malaria": "Positive" if user_id.endswith("003") else "Negative",
                "Widal Test": "Positive" if user_id.endswith("004") else "Negative",
                "Hepatitis B": "Negative"
            },
            "timestamp": (datetime.now() - timedelta(days=3)).isoformat()
        }
        
        # Add kidney function data
        sample_data[user_key]["kidney_function"] = {
            "data": {
                "Creatinine": 1.0,
                "BUN": 15.0,
                "eGFR": 90.0
            },
            "timestamp": (datetime.now() - timedelta(days=7)).isoformat()
        }
        
        # Add lipid profile data
        sample_data[user_key]["lipid_profile"] = {
            "data": {
                "Total_Cholesterol": 180.0,
                "HDL": 45.0,
                "LDL": 110.0,
                "Triglycerides": 150.0
            },
            "timestamp": (datetime.now() - timedelta(days=10)).isoformat()
        }
    
    # Add some vital signs monitoring data (list format)
    sample_data[("user_lagos_006", "default")] = [
        {
            "Glucose": 110.0,
            "Blood Pressure (Systolic)": 145.0,
            "Blood Pressure (Diastolic)": 90.0,
            "timestamp": (datetime.now() - timedelta(days=2)).isoformat()
        },
        {
            "Glucose": 105.0,
            "Blood Pressure (Systolic)": 140.0,
            "Blood Pressure (Diastolic)": 85.0,
            "timestamp": (datetime.now() - timedelta(days=1)).isoformat()
        }
    ]
    
    return sample_data

def test_lab_insights_dashboard():
    """Test the Lab Insights Dashboard with sample data"""
    
    print("🔬 Testing Lab Insights Dashboard")
    print("=" * 50)
    
    # Create sample data
    sample_health_data = create_sample_health_data()
    
    print(f"📊 Sample data created with {len(sample_health_data)} users")
    
    # Test 1: Basic dashboard generation
    print("\n🧪 Test 1: Basic Dashboard (30 days)")
    dashboard_result = generate_lab_insights_dashboard(sample_health_data)
    dashboard_data = json.loads(dashboard_result)
    
    if "error" in dashboard_data:
        print(f"❌ Error: {dashboard_data['error']}")
        return
    
    print("✅ Dashboard generated successfully!")
    print(f"📈 Total test requests: {dashboard_data['summary_statistics']['total_test_requests']}")
    print(f"🧪 Unique tests: {dashboard_data['summary_statistics']['unique_tests_requested']}")
    print(f"👥 Active users: {dashboard_data['summary_statistics']['active_users']}")
    print(f"⏱️ Average turnaround: {dashboard_data['summary_statistics']['average_turnaround_hours']} hours")
    
    # Test 2: Filtered dashboard (test type filter)
    print("\n🧪 Test 2: Filtered Dashboard (health_score only)")
    filtered_result = generate_lab_insights_dashboard(
        sample_health_data, 
        date_range_days=30,
        test_type_filter="health_score"
    )
    filtered_data = json.loads(filtered_result)
    
    if "error" not in filtered_data:
        print("✅ Filtered dashboard generated successfully!")
        print(f"📈 Filtered test requests: {filtered_data['summary_statistics']['total_test_requests']}")
    
    # Test 3: Show top requested tests
    print("\n🏆 Top Requested Tests:")
    for i, test in enumerate(dashboard_data['top_requested_tests'][:5], 1):
        print(f"{i}. {test['test_name']}: {test['request_count']} requests ({test['percentage_of_total']}%)")
    
    # Test 4: Show disease trends
    print("\n🦠 Disease Trend Indicators:")
    if dashboard_data['disease_trend_indicators']:
        for trend in dashboard_data['disease_trend_indicators'][:3]:
            print(f"• {trend['condition']}: {trend['case_count']} cases ({trend['percentage']}%) - {trend['trend_status']}")
    else:
        print("• No significant disease trends detected")
    
    # Test 5: Show geographic distribution
    print("\n🌍 Geographic Distribution:")
    for geo in dashboard_data['geographic_distribution']:
        print(f"• {geo['location']}: {geo['test_count']} tests ({geo['percentage_of_total']}%)")
    
    # Test 6: Show insights
    print("\n💡 Key Insights:")
    for insight in dashboard_data['insights_and_recommendations']:
        print(f"• {insight}")
    
    print("\n" + "=" * 50)
    print("🎉 Lab Insights Dashboard testing completed!")

def test_api_payload():
    """Generate sample API payload for testing the endpoint"""
    
    payload = {
        "admin_id": "admin_doctor_001",
        "date_range_days": 30,
        "test_type_filter": None,
        "user_group_filter": None
    }
    
    print("\n📋 Sample API Payload:")
    print("POST /wellness-center/lab-insights-dashboard")
    print(json.dumps(payload, indent=2))
    
    return payload

if __name__ == "__main__":
    # Run the tests
    test_lab_insights_dashboard()
    test_api_payload()
