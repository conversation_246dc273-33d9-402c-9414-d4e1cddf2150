"""
API Test Script for Standalone Wellness Center

This script tests the wellness center API endpoint that routes to the standalone module.
"""

import requests
import json
import time
from datetime import datetime

def check_server_status():
    """Check if the agent server is running"""
    try:
        response = requests.get("http://localhost:8002/docs", timeout=5)
        if response.status_code == 200:
            print("✅ Agent Server is running and accessible")
            return True
        else:
            print(f"⚠️ Server responded with status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Agent Server is not running or not accessible")
        return False
    except Exception as e:
        print(f"❌ Error checking server status: {str(e)}")
        return False

def test_basic_dashboard_request():
    """Test basic dashboard request"""
    
    print("\n🧪 Test 1: Basic Dashboard Request")
    print("-" * 50)
    
    url = "http://localhost:8002/wellness-center/lab-insights-dashboard"
    payload = {
        "admin_id": "admin_doctor_001",
        "date_range_days": 30,
        "test_type_filter": None,
        "user_group_filter": None
    }
    
    print(f"🌐 URL: {url}")
    print(f"📋 Payload: {json.dumps(payload, indent=2)}")
    
    try:
        print("🚀 Sending request...")
        response = requests.post(url, json=payload, timeout=30)
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get("success"):
                print("✅ Request successful!")
                
                dashboard_data = result["dashboard_data"]
                stats = dashboard_data["summary_statistics"]
                
                print(f"\n📈 Dashboard Summary:")
                print(f"• Admin: {dashboard_data['requested_by']}")
                print(f"• Analysis Period: {dashboard_data['analysis_period_days']} days")
                print(f"• Total Requests: {stats['total_test_requests']}")
                print(f"• Unique Tests: {stats['unique_tests_requested']}")
                print(f"• Active Users: {stats['active_users_analyzed']}")
                print(f"• Avg Turnaround: {stats['average_turnaround_hours']} hours")
                print(f"• Daily Average: {stats['daily_average_tests']} tests/day")
                
                # Show top tests
                print(f"\n🏆 Top 3 Requested Tests:")
                for test in dashboard_data['top_requested_tests'][:3]:
                    print(f"{test['rank']}. {test['test_name']}: {test['request_count']} ({test['percentage_of_total']}%)")
                
                # Show geographic distribution
                print(f"\n🌍 Geographic Distribution:")
                for geo in dashboard_data['geographic_distribution'][:3]:
                    print(f"• {geo['location']}: {geo['test_count']} tests ({geo['percentage_of_total']}%)")
                
                # Show insights
                print(f"\n💡 Key Insights:")
                for insight in dashboard_data['insights_and_recommendations'][:3]:
                    print(f"• {insight}")
                
                return True
                
            else:
                print(f"❌ Dashboard generation failed: {result.get('error', 'Unknown error')}")
                return False
                
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timeout - server may be overloaded")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_filtered_dashboard_request():
    """Test dashboard request with filters"""
    
    print("\n🧪 Test 2: Filtered Dashboard Request")
    print("-" * 50)
    
    url = "http://localhost:8002/wellness-center/lab-insights-dashboard"
    payload = {
        "admin_id": "admin_doctor_002",
        "date_range_days": 7,
        "test_type_filter": "health_score",
        "user_group_filter": None
    }
    
    print(f"🔍 Testing with filter: {payload['test_type_filter']}")
    print(f"📅 Date range: {payload['date_range_days']} days")
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get("success"):
                print("✅ Filtered request successful!")
                
                dashboard_data = result["dashboard_data"]
                stats = dashboard_data["summary_statistics"]
                filters = dashboard_data["filters_applied"]
                
                print(f"📊 Filtered Results:")
                print(f"• Filter Applied: {filters['test_type']}")
                print(f"• Filtered Requests: {stats['total_test_requests']}")
                print(f"• Analysis Period: {dashboard_data['analysis_period_days']} days")
                
                return True
            else:
                print(f"❌ Error: {result.get('error')}")
                return False
        else:
            print(f"❌ Request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_different_time_ranges():
    """Test dashboard with different time ranges"""
    
    print("\n🧪 Test 3: Different Time Ranges")
    print("-" * 50)
    
    url = "http://localhost:8002/wellness-center/lab-insights-dashboard"
    time_ranges = [7, 14, 30, 90]
    
    results = []
    
    for days in time_ranges:
        payload = {
            "admin_id": f"admin_test_{days}d",
            "date_range_days": days,
            "test_type_filter": None,
            "user_group_filter": None
        }
        
        try:
            response = requests.post(url, json=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get("success"):
                    stats = result["dashboard_data"]["summary_statistics"]
                    results.append({
                        "days": days,
                        "requests": stats["total_test_requests"],
                        "daily_avg": stats["daily_average_tests"]
                    })
                    print(f"📅 {days:2d} days: {stats['total_test_requests']:3d} requests ({stats['daily_average_tests']:5.1f}/day)")
                else:
                    print(f"❌ {days} days: Error - {result.get('error')}")
            else:
                print(f"❌ {days} days: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ {days} days: {str(e)}")
    
    if results:
        print(f"\n📊 Time Range Analysis:")
        print(f"• Tested {len(results)} different time ranges")
        print(f"• Max requests: {max(r['requests'] for r in results)}")
        print(f"• Avg daily rate: {sum(r['daily_avg'] for r in results) / len(results):.1f}")
        return True
    
    return False

def test_error_handling():
    """Test error handling with invalid requests"""
    
    print("\n🧪 Test 4: Error Handling")
    print("-" * 50)
    
    url = "http://localhost:8002/wellness-center/lab-insights-dashboard"
    
    # Test with missing admin_id
    invalid_payload = {
        "date_range_days": 30,
        "test_type_filter": None,
        "user_group_filter": None
    }
    
    try:
        response = requests.post(url, json=invalid_payload, timeout=30)
        
        if response.status_code == 422:
            print("✅ Validation error handled correctly (missing admin_id)")
        else:
            print(f"⚠️ Unexpected response for invalid payload: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing invalid payload: {str(e)}")
    
    # Test with invalid date range
    invalid_date_payload = {
        "admin_id": "admin_test",
        "date_range_days": -5,  # Invalid negative days
        "test_type_filter": None,
        "user_group_filter": None
    }
    
    try:
        response = requests.post(url, json=invalid_date_payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if "error" in result:
                print("✅ Invalid date range handled gracefully")
            else:
                print("⚠️ Invalid date range not caught")
        else:
            print(f"⚠️ Unexpected response for invalid date: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing invalid date: {str(e)}")

def performance_test():
    """Test performance with multiple concurrent requests"""
    
    print("\n🧪 Test 5: Performance Test")
    print("-" * 50)
    
    url = "http://localhost:8002/wellness-center/lab-insights-dashboard"
    payload = {
        "admin_id": "admin_performance_test",
        "date_range_days": 30,
        "test_type_filter": None,
        "user_group_filter": None
    }
    
    # Test response time
    start_time = time.time()
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        end_time = time.time()
        
        response_time = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print(f"✅ Performance test successful")
                print(f"⏱️ Response time: {response_time:.2f} seconds")
                
                if response_time < 2.0:
                    print("🚀 Excellent performance (< 2s)")
                elif response_time < 5.0:
                    print("✅ Good performance (< 5s)")
                else:
                    print("⚠️ Slow performance (> 5s)")
                
                return True
            else:
                print(f"❌ Performance test failed: {result.get('error')}")
        else:
            print(f"❌ Performance test failed: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ Performance test error: {str(e)}")
    
    return False

def main():
    """Main test function"""
    
    print("🏥 Wellness Center API Testing")
    print("=" * 60)
    print(f"🕒 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Check server status
    if not check_server_status():
        print("\n🚨 Cannot run tests - server is not accessible")
        print("\n📝 To start the server:")
        print("1. Navigate to the Agent Server directory")
        print("2. Run: python agent_server.py")
        print("3. Wait for the server to start")
        print("4. Run this test script again")
        return
    
    # Run tests
    test_results = []
    
    print(f"\n🧪 Running Wellness Center API Tests...")
    print("=" * 60)
    
    test_results.append(("Basic Dashboard", test_basic_dashboard_request()))
    test_results.append(("Filtered Dashboard", test_filtered_dashboard_request()))
    test_results.append(("Time Ranges", test_different_time_ranges()))
    test_results.append(("Error Handling", test_error_handling()))
    test_results.append(("Performance", performance_test()))
    
    # Summary
    print(f"\n📊 Test Results Summary")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall Results: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Wellness Center API is working perfectly!")
    elif passed > total * 0.8:
        print("✅ Most tests passed! Minor issues may need attention.")
    else:
        print("⚠️ Several tests failed. Please check the server and configuration.")
    
    print(f"\n🏥 Wellness Center API testing completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
