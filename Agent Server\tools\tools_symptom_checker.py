from langchain.tools import Tool
import json
from datetime import datetime
import logging
import re

class SmartSymptomChecker:
    """
    A tool that analyzes user-reported symptoms, provides potential diagnoses,
    recommends appropriate tests, and offers first-line self-assessment and home care advice.
    """
    
    def __init__(self):
        # Common symptom patterns and their associated conditions
        self.symptom_patterns = {
            # Respiratory symptoms
            "cough|phlegm|mucus|chest congestion": {
                "conditions": ["Common cold", "Bronchitis", "Pneumonia", "COVID-19", "Asthma", "COPD"],
                "tests": ["Chest X-ray", "Pulse oximetry", "COVID-19 test", "Spirometry"],
                "urgency": "moderate"
            },
            "shortness of breath|difficulty breathing|can't breathe|trouble breathing": {
                "conditions": ["Asthma", "COPD", "Pneumonia", "Heart failure", "COVID-19", "Pulmonary embolism"],
                "tests": ["Chest X-ray", "ECG", "Pulse oximetry", "D-dimer", "COVID-19 test"],
                "urgency": "high"
            },
            "wheezing|whistling sound": {
                "conditions": ["Asthma", "COPD", "Bronchitis", "Allergic reaction"],
                "tests": ["Spirometry", "Peak flow measurement", "Allergy testing"],
                "urgency": "moderate"
            },
            
            # Cardiovascular symptoms
            "chest pain|chest pressure|chest tightness": {
                "conditions": ["Angina", "Heart attack", "GERD", "Anxiety", "Costochondritis", "Pulmonary embolism"],
                "tests": ["ECG", "Cardiac enzymes", "Chest X-ray", "Stress test"],
                "urgency": "high"
            },
            "palpitations|racing heart|irregular heartbeat": {
                "conditions": ["Arrhythmia", "Anxiety", "Hyperthyroidism", "Anemia", "Electrolyte imbalance"],
                "tests": ["ECG", "Holter monitor", "Thyroid function tests", "Complete blood count"],
                "urgency": "moderate"
            },
            "swelling|edema|puffy|swollen (legs|ankles|feet)": {
                "conditions": ["Heart failure", "Venous insufficiency", "Kidney disease", "Liver disease", "Lymphedema"],
                "tests": ["Echocardiogram", "Kidney function tests", "Liver function tests", "Ultrasound"],
                "urgency": "moderate"
            },
            
            # Gastrointestinal symptoms
            "abdominal pain|stomach pain|belly pain": {
                "conditions": ["Gastritis", "Appendicitis", "Gallstones", "Pancreatitis", "IBS", "Diverticulitis", "Intestinal obstruction"],
                "tests": ["Abdominal ultrasound", "CT scan", "Stool analysis", "Endoscopy"],
                "urgency": "moderate"
            },
            "nausea|vomiting": {
                "conditions": ["Gastroenteritis", "Food poisoning", "Migraine", "Pregnancy", "Vestibular disorders", "Medication side effect"],
                "tests": ["Pregnancy test", "Stool culture", "Abdominal ultrasound"],
                "urgency": "moderate"
            },
            "diarrhea|loose stool": {
                "conditions": ["Gastroenteritis", "Food poisoning", "IBS", "IBD", "Celiac disease", "Medication side effect"],
                "tests": ["Stool culture", "Stool analysis", "Colonoscopy"],
                "urgency": "moderate"
            },
            "constipation|hard stool": {
                "conditions": ["Dietary issues", "IBS", "Hypothyroidism", "Medication side effect", "Colorectal cancer"],
                "tests": ["Thyroid function tests", "Colonoscopy"],
                "urgency": "low"
            },
            
            # Neurological symptoms
            "headache|head pain": {
                "conditions": ["Tension headache", "Migraine", "Sinusitis", "Hypertension", "Brain tumor", "Meningitis"],
                "tests": ["Blood pressure measurement", "CT scan", "MRI", "Lumbar puncture"],
                "urgency": "moderate"
            },
            "dizziness|vertigo|lightheaded": {
                "conditions": ["Vestibular disorders", "Low blood pressure", "Anemia", "Dehydration", "Medication side effect", "Anxiety"],
                "tests": ["Blood pressure measurement", "Complete blood count", "Vestibular testing"],
                "urgency": "moderate"
            },
            "numbness|tingling|pins and needles": {
                "conditions": ["Peripheral neuropathy", "Vitamin B12 deficiency", "Stroke", "Multiple sclerosis", "Pinched nerve"],
                "tests": ["Vitamin B12 level", "Nerve conduction studies", "MRI"],
                "urgency": "moderate"
            },
            
            # Musculoskeletal symptoms
            "joint pain|arthralgia": {
                "conditions": ["Osteoarthritis", "Rheumatoid arthritis", "Gout", "Lupus", "Lyme disease", "Fibromyalgia"],
                "tests": ["X-ray", "Rheumatoid factor", "Uric acid level", "ANA test", "Lyme disease test"],
                "urgency": "low"
            },
            "muscle pain|myalgia": {
                "conditions": ["Fibromyalgia", "Viral infection", "Medication side effect", "Vitamin D deficiency", "Polymyalgia rheumatica"],
                "tests": ["CK level", "Vitamin D level", "ESR", "CRP"],
                "urgency": "low"
            },
            "back pain|backache": {
                "conditions": ["Muscle strain", "Herniated disc", "Sciatica", "Spinal stenosis", "Kidney stones", "Ankylosing spondylitis"],
                "tests": ["X-ray", "MRI", "Urinalysis"],
                "urgency": "low"
            },
            
            # General symptoms
            "fever|high temperature": {
                "conditions": ["Viral infection", "Bacterial infection", "COVID-19", "Inflammatory conditions", "Malaria"],
                "tests": ["Complete blood count", "Blood culture", "COVID-19 test", "Malaria test"],
                "urgency": "moderate"
            },
            "fatigue|tired|exhaustion|no energy": {
                "conditions": ["Anemia", "Hypothyroidism", "Depression", "Sleep apnea", "Chronic fatigue syndrome", "Vitamin deficiency"],
                "tests": ["Complete blood count", "Thyroid function tests", "Vitamin levels", "Sleep study"],
                "urgency": "low"
            },
            "weight loss|losing weight": {
                "conditions": ["Hyperthyroidism", "Diabetes", "Cancer", "Depression", "Inflammatory bowel disease", "Malabsorption"],
                "tests": ["Thyroid function tests", "Blood glucose", "Complete blood count", "Stool analysis"],
                "urgency": "moderate"
            },
            
            # Skin symptoms
            "rash|skin eruption": {
                "conditions": ["Allergic reaction", "Eczema", "Psoriasis", "Drug reaction", "Viral exanthem", "Contact dermatitis"],
                "tests": ["Allergy testing", "Skin biopsy"],
                "urgency": "low"
            },
            "itching|pruritus": {
                "conditions": ["Allergic reaction", "Eczema", "Psoriasis", "Liver disease", "Kidney disease", "Parasitic infection"],
                "tests": ["Allergy testing", "Liver function tests", "Kidney function tests", "Stool analysis"],
                "urgency": "low"
            }
        }
        
        # First-line self-assessment and home care recommendations
        self.home_care_recommendations = {
            "Common cold": [
                "Rest and stay hydrated",
                "Use over-the-counter pain relievers like acetaminophen for fever and discomfort",
                "Try saline nasal sprays or rinses",
                "Use a humidifier to add moisture to the air",
                "Gargle with salt water for sore throat"
            ],
            "Gastroenteritis": [
                "Stay hydrated with water, clear broths, or oral rehydration solutions",
                "Eat bland, easy-to-digest foods like bananas, rice, applesauce, and toast (BRAT diet)",
                "Avoid dairy, fatty, spicy, or highly seasoned foods",
                "Avoid caffeine and alcohol",
                "Rest and gradually return to normal diet as symptoms improve"
            ],
            "Tension headache": [
                "Take over-the-counter pain relievers like acetaminophen or ibuprofen",
                "Apply a heating pad or ice pack to your head or neck",
                "Take a warm shower or bath",
                "Practice relaxation techniques like deep breathing or meditation",
                "Maintain good posture and take frequent breaks from screens"
            ],
            "Muscle strain": [
                "Rest the affected area and avoid activities that cause pain",
                "Apply ice for 15-20 minutes several times a day for the first 48-72 hours",
                "After 48-72 hours, apply heat to promote healing",
                "Take over-the-counter pain relievers like ibuprofen or acetaminophen",
                "Use compression bandages and elevate the affected area if possible"
            ],
            "Allergic reaction (mild)": [
                "Take an over-the-counter antihistamine like cetirizine or loratadine",
                "Avoid known allergens",
                "Apply hydrocortisone cream for skin reactions",
                "Use cold compresses for itching or swelling",
                "Stay hydrated and monitor symptoms"
            ]
        }
        
        # Emergency symptoms that require immediate medical attention
        self.emergency_symptoms = [
            "severe chest pain",
            "difficulty breathing",
            "sudden severe headache",
            "sudden weakness or numbness",
            "severe abdominal pain",
            "coughing up blood",
            "vomiting blood",
            "loss of consciousness",
            "seizure",
            "severe allergic reaction",
            "suicidal thoughts"
        ]
    
    def analyze_symptoms(self, symptoms_json):
        """
        Analyze symptoms and provide potential diagnoses, recommended tests,
        and first-line self-assessment and home care advice.
        
        Args:
            symptoms_json: JSON string containing symptoms and user information
            
        Returns:
            JSON string with analysis results
        """
        try:
            # Parse input data
            input_data = json.loads(symptoms_json)
            
            # Extract symptoms and user info
            symptoms = input_data.get("symptoms", "")
            age = input_data.get("age", "unknown")
            sex = input_data.get("sex", "unknown")
            duration = input_data.get("duration", "unknown")
            severity = input_data.get("severity", "moderate")
            
            # Check for emergency symptoms
            for emergency in self.emergency_symptoms:
                if emergency in symptoms.lower():
                    return json.dumps({
                        "urgency_level": "emergency",
                        "message": "SEEK IMMEDIATE MEDICAL ATTENTION",
                        "recommendation": "These symptoms require immediate medical evaluation. Please call emergency services or go to the nearest emergency room."
                    })
            
            # Match symptoms to conditions
            matched_conditions = {}
            matched_tests = {}
            urgency_level = "low"
            
            for pattern, data in self.symptom_patterns.items():
                if re.search(pattern, symptoms.lower()):
                    # Add conditions with confidence scores
                    for condition in data["conditions"]:
                        if condition in matched_conditions:
                            matched_conditions[condition] += 1
                        else:
                            matched_conditions[condition] = 1
                    
                    # Add recommended tests
                    for test in data["tests"]:
                        if test in matched_tests:
                            matched_tests[test] += 1
                        else:
                            matched_tests[test] = 1
                    
                    # Update urgency level if higher
                    if data["urgency"] == "high" and urgency_level != "emergency":
                        urgency_level = "high"
                    elif data["urgency"] == "moderate" and urgency_level == "low":
                        urgency_level = "moderate"
            
            # Sort conditions and tests by confidence score
            sorted_conditions = sorted(matched_conditions.items(), key=lambda x: x[1], reverse=True)
            sorted_tests = sorted(matched_tests.items(), key=lambda x: x[1], reverse=True)
            
            # Get top conditions and tests
            top_conditions = [condition for condition, score in sorted_conditions[:5]]
            top_tests = [test for test, score in sorted_tests[:5]]
            
            # Generate home care recommendations for top condition
            home_care = []
            if top_conditions and top_conditions[0] in self.home_care_recommendations:
                home_care = self.home_care_recommendations[top_conditions[0]]
            
            # Create response
            response = {
                "timestamp": datetime.now().isoformat(),
                "symptoms": symptoms,
                "user_info": {
                    "age": age,
                    "sex": sex,
                    "duration": duration,
                    "severity": severity
                },
                "potential_conditions": top_conditions,
                "recommended_tests": top_tests,
                "urgency_level": urgency_level,
                "home_care_recommendations": home_care,
                "disclaimer": "This is not a medical diagnosis. Please consult with a healthcare professional for proper evaluation and treatment."
            }
            
            return json.dumps(response)
        
        except Exception as e:
            logging.error(f"Error in symptom analysis: {str(e)}")
            return json.dumps({"error": f"Failed to analyze symptoms: {str(e)}"})

# Create a Tool instance for use with LangChain
symptom_checker_tool = Tool(
    name="SmartSymptomChecker",
    func=lambda x: SmartSymptomChecker().analyze_symptoms(x),
    description="Analyzes user-reported symptoms, provides potential diagnoses, recommends appropriate tests, and offers first-line self-assessment and home care advice."
)

def analyze_symptoms(symptoms_json):
    """
    Standalone function to analyze symptoms using the SmartSymptomChecker class.
    
    Args:
        symptoms_json: JSON string containing symptoms and user information
        
    Returns:
        JSON string with analysis results
    """
    return SmartSymptomChecker().analyze_symptoms(symptoms_json)
