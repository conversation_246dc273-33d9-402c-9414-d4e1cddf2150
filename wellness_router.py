# wellness_center/wellness_router.py

from fastapi import APIRouter
from typing import Dict, Any, List, Union
from pydantic import BaseModel
import json

# --- Import AI Tools ---
from wellness_center.tools_lab_insights_dashboard import generate_lab_insights_dashboard, lab_insights_dashboard_tool
# TODO: Add other wellness center tools when they are created
# from wellness_center.tools_specialist_monitoring import generate_clinical_dashboard_insight
# from wellness_center.tools_patient_trends import analyze_vital_trends
# from wellness_center.tools_doctor_summary import generate_doctor_summary_v2
# from wellness_center.tools_differential_diagnosis import differential_diagnosis
# from wellness_center.tools_discharge_summary import generate_discharge_summary
# from wellness_center.tools_mental_health_screener import mental_health_screener
# from wellness_center.tools_unstable_detector import unstable_patient_detector



router = APIRouter()

# --- Request Schemas ---

class ClinicalDashboardRequest(BaseModel):
    data: Dict[str, List[Dict[str, Any]]]

class PatientVitalTrendsRequest(BaseModel):
    patient_id: str
    patient_data: List[Dict[str, Any]]

class DoctorSummaryRequest(BaseModel):
    patient_id: str
    records: Dict[str, Any]

class DifferentialDiagnosisRequest(BaseModel):
    vitals: Dict[str, Union[float, str, Dict[str, str]]]
    symptoms: List[str]
    history: List[str]

class DischargeRequest(BaseModel):
    patient_id: str
    diagnosis: str
    treatment_summary: str
    hospital_course: List[str]
    medications_on_discharge: List[str]
    follow_up_instructions: List[str]

class MentalHealthRequest(BaseModel):
    phq9: List[int]
    gad7: List[int]
    history: List[str] = []

class PatientSeriesRequest(BaseModel):
    data: Dict[str, List[Dict[str, Any]]]

class LabInsightsDashboardRequest(BaseModel):
    admin_id: str
    date_range_days: int = 30
    test_type_filter: str = None
    user_group_filter: str = None



# --- Endpoints ---

# TODO: Uncomment when tools are created
# @router.post("/wellness/clinical-dashboard")
# async def generate_clinical_dashboard(request: ClinicalDashboardRequest):
#     return generate_clinical_dashboard_insight(request.data)

# @router.get("/wellness/dashboard-live")
# async def dashboard_from_saved_file():
#     with open("data/health_user_data.json", "r") as f:
#         user_data = json.load(f)
#     return generate_clinical_dashboard_insight(user_data)

# TODO: Uncomment when tools are created
# @router.post("/wellness/patient-trends")
# def get_patient_vital_trends(request: PatientVitalTrendsRequest):
#     return analyze_vital_trends(request.patient_id, request.patient_data)

# @router.post("/wellness/doctor-summary")
# def doctor_summary_endpoint(request: DoctorSummaryRequest):
#     return generate_doctor_summary_v2(request.patient_id, request.records)

# @router.post("/wellness/differential-diagnosis")
# def get_differential_diagnosis(request: DifferentialDiagnosisRequest):
#     return differential_diagnosis(vitals=request.vitals, symptoms=request.symptoms, history=request.history)

# @router.post("/wellness/discharge-summary")
# def discharge_summary_endpoint(request: DischargeRequest):
#     return generate_discharge_summary(request.dict())

# @router.post("/wellness/mental-health-screener")
# def run_mental_health_screener(request: MentalHealthRequest):
#     return mental_health_screener(request.phq9, request.gad7, request.history)

# @router.post("/wellness/unstable-detector")
# def detect_unstable_patients(request: PatientSeriesRequest):
#     return unstable_patient_detector(request.data)

@router.post("/wellness/lab-insights-dashboard")
async def lab_insights_dashboard_endpoint(request: LabInsightsDashboardRequest):
    """
    Generate Lab Insights Dashboard for admins and doctors

    Provides analytics on:
    - Top 10 most requested tests
    - Average turnaround time
    - Disease trend indicators
    - Geographic test distribution
    - Insights and recommendations
    """
    try:
        # Note: This would need access to user_health_data from the main application
        # For now, we'll return a placeholder response
        # In a real implementation, you'd pass the actual user_health_data

        # Placeholder user health data - replace with actual data source
        user_health_data = {}

        # Use the tool to generate dashboard
        dashboard_result = lab_insights_dashboard_tool.generate_dashboard(
            user_health_data=user_health_data,
            admin_id=request.admin_id,
            date_range_days=request.date_range_days,
            test_type_filter=request.test_type_filter,
            user_group_filter=request.user_group_filter
        )

        return {
            "success": True,
            "dashboard_data": dashboard_result,
            "message": f"Lab insights dashboard generated successfully for {request.date_range_days} days of data"
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to generate lab insights dashboard"
        }


    