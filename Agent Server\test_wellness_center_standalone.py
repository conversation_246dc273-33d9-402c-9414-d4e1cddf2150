"""
Test script for the standalone Wellness Center module

This script tests the wellness center as a standalone module that gets
called through the agent server route.
"""

import json
import sys
import os
from datetime import datetime, timedelta

# Import the standalone wellness center
from wellness_center import get_lab_insights_dashboard, wellness_center

def create_comprehensive_sample_data():
    """Create comprehensive sample health data for testing"""
    
    sample_data = {}
    
    # Create diverse users from different locations
    users = [
        ("user_lagos_001", "Lagos"),
        ("user_abuja_002", "Abuja"), 
        ("user_kano_003", "Kano"),
        ("user_lagos_004", "Lagos"),
        ("user_abuja_005", "Abuja"),
        ("user_ibadan_006", "Ibadan"),
        ("user_port_007", "Port Harcourt"),
        ("user_kaduna_008", "Kaduna")
    ]
    
    for user_id, location in users:
        user_key = (user_id, "default")
        sample_data[user_key] = {}
        
        # Add health score data with varied results
        sample_data[user_key]["health_score"] = {
            "data": {
                "Glucose": 95.0 + (hash(user_id) % 50),  # Vary glucose levels
                "SpO2": 95 + (hash(user_id) % 5),
                "ECG (Heart Rate)": 65 + (hash(user_id) % 25),
                "Blood Pressure (Systolic)": 110 + (hash(user_id) % 40),
                "Blood Pressure (Diastolic)": 70 + (hash(user_id) % 20),
                "Weight (BMI)": 20.0 + (hash(user_id) % 15),
                "Temperature": 98.0 + (hash(user_id) % 3),
                "Malaria": "Positive" if user_id.endswith(("001", "003", "007")) else "Negative",
                "Widal Test": "Positive" if user_id.endswith(("004", "008")) else "Negative",
                "Hepatitis B": "Positive" if user_id.endswith("002") else "Negative",
                "Voluntary Serology": "Negative"
            },
            "timestamp": (datetime.now() - timedelta(days=hash(user_id) % 25)).isoformat()
        }
        
        # Add test results
        sample_data[user_key]["test_results"] = {
            "data": {
                "Malaria": "Positive" if user_id.endswith(("003", "006")) else "Negative",
                "Widal Test": "Positive" if user_id.endswith("005") else "Negative",
                "Hepatitis B": "Negative"
            },
            "timestamp": (datetime.now() - timedelta(days=hash(user_id) % 20)).isoformat()
        }
        
        # Add kidney function data
        sample_data[user_key]["kidney_function"] = {
            "data": {
                "Creatinine": 0.8 + (hash(user_id) % 5) * 0.1,
                "BUN": 10.0 + (hash(user_id) % 20),
                "eGFR": 80.0 + (hash(user_id) % 30)
            },
            "timestamp": (datetime.now() - timedelta(days=hash(user_id) % 15)).isoformat()
        }
        
        # Add lipid profile data
        sample_data[user_key]["lipid_profile"] = {
            "data": {
                "Total_Cholesterol": 160.0 + (hash(user_id) % 80),
                "HDL": 35.0 + (hash(user_id) % 25),
                "LDL": 90.0 + (hash(user_id) % 60),
                "Triglycerides": 120.0 + (hash(user_id) % 100)
            },
            "timestamp": (datetime.now() - timedelta(days=hash(user_id) % 12)).isoformat()
        }
        
        # Add liver function data for some users
        if hash(user_id) % 3 == 0:
            sample_data[user_key]["liver_function"] = {
                "data": {
                    "ALT": 20.0 + (hash(user_id) % 40),
                    "AST": 18.0 + (hash(user_id) % 35),
                    "Bilirubin": 0.5 + (hash(user_id) % 3) * 0.2
                },
                "timestamp": (datetime.now() - timedelta(days=hash(user_id) % 10)).isoformat()
            }
    
    # Add some vital signs monitoring data (list format)
    for i in range(3):
        user_key = (f"user_monitor_{i:03d}", "default")
        sample_data[user_key] = []
        
        for day in range(7):  # 7 days of monitoring
            sample_data[user_key].append({
                "Glucose": 90.0 + (hash(f"{i}_{day}") % 40),
                "Blood Pressure (Systolic)": 115 + (hash(f"{i}_{day}") % 35),
                "Blood Pressure (Diastolic)": 75 + (hash(f"{i}_{day}") % 20),
                "Heart Rate": 65 + (hash(f"{i}_{day}") % 25),
                "timestamp": (datetime.now() - timedelta(days=day)).isoformat()
            })
    
    return sample_data

def test_standalone_wellness_center():
    """Test the standalone wellness center functionality"""
    
    print("🏥 Testing Standalone Wellness Center")
    print("=" * 60)
    
    # Create comprehensive sample data
    sample_health_data = create_comprehensive_sample_data()
    print(f"📊 Created sample data with {len(sample_health_data)} users")
    
    # Test 1: Basic dashboard generation
    print("\n🧪 Test 1: Basic Dashboard Generation")
    print("-" * 40)
    
    dashboard_result = get_lab_insights_dashboard(
        user_health_data=sample_health_data,
        admin_id="admin_doctor_001",
        date_range_days=30
    )
    
    if dashboard_result.get("success"):
        print("✅ Dashboard generated successfully!")
        stats = dashboard_result["summary_statistics"]
        print(f"📈 Total test requests: {stats['total_test_requests']}")
        print(f"🧪 Unique tests: {stats['unique_tests_requested']}")
        print(f"👥 Active users: {stats['active_users_analyzed']}")
        print(f"⏱️ Average turnaround: {stats['average_turnaround_hours']} hours")
        print(f"📊 Daily average: {stats['daily_average_tests']} tests/day")
        print(f"🎯 Data quality score: {dashboard_result['wellness_center_metadata']['data_quality_score']}%")
    else:
        print(f"❌ Error: {dashboard_result.get('error')}")
        return
    
    # Test 2: Filtered dashboard
    print("\n🧪 Test 2: Filtered Dashboard (health_score only)")
    print("-" * 40)
    
    filtered_result = get_lab_insights_dashboard(
        user_health_data=sample_health_data,
        admin_id="admin_doctor_002",
        date_range_days=30,
        test_type_filter="health_score"
    )
    
    if filtered_result.get("success"):
        print("✅ Filtered dashboard generated successfully!")
        print(f"📈 Filtered requests: {filtered_result['summary_statistics']['total_test_requests']}")
        print(f"🔍 Filter applied: {filtered_result['filters_applied']['test_type']}")
    
    # Test 3: Show detailed analytics
    print("\n🏆 Top Requested Tests:")
    for test in dashboard_result['top_requested_tests'][:5]:
        print(f"{test['rank']}. {test['test_name']}: {test['request_count']} requests ({test['percentage_of_total']}%)")
    
    print("\n🦠 Disease Trend Indicators:")
    for trend in dashboard_result['disease_trend_indicators'][:5]:
        status_emoji = "🚨" if trend['risk_level'] == "High" else "⚠️" if trend['risk_level'] == "Medium" else "✅"
        print(f"{status_emoji} {trend['condition']}: {trend['case_count']} cases ({trend['percentage']}%) - {trend['trend_status']}")
    
    print("\n🌍 Geographic Distribution:")
    for geo in dashboard_result['geographic_distribution']:
        volume_emoji = "🔥" if geo['status'] == "High Volume" else "📊" if geo['status'] == "Moderate" else "📉"
        print(f"{volume_emoji} {geo['location']}: {geo['test_count']} tests ({geo['percentage_of_total']}%) - {geo['status']}")
    
    print("\n💡 Key Insights & Recommendations:")
    for insight in dashboard_result['insights_and_recommendations']:
        print(f"• {insight}")
    
    # Test 4: Different time ranges
    print("\n🧪 Test 4: Different Time Ranges")
    print("-" * 40)
    
    time_ranges = [7, 14, 30, 90]
    for days in time_ranges:
        result = get_lab_insights_dashboard(
            user_health_data=sample_health_data,
            admin_id="admin_doctor_003",
            date_range_days=days
        )
        
        if result.get("success"):
            stats = result["summary_statistics"]
            print(f"📅 {days:2d} days: {stats['total_test_requests']:3d} requests, {stats['daily_average_tests']:5.1f} tests/day")
    
    print("\n" + "=" * 60)
    print("🎉 Standalone Wellness Center testing completed!")
    
    return dashboard_result

def test_api_integration():
    """Test how the wellness center integrates with the API"""
    
    print("\n🔗 Testing API Integration")
    print("=" * 60)
    
    # Sample API payload
    api_payload = {
        "admin_id": "admin_doctor_001",
        "date_range_days": 30,
        "test_type_filter": None,
        "user_group_filter": None
    }
    
    print("📋 Sample API Request:")
    print("POST /wellness-center/lab-insights-dashboard")
    print(json.dumps(api_payload, indent=2))
    
    print("\n✅ The wellness center is now standalone and ready for API integration!")
    print("🚀 Start the agent server and make requests to the wellness center route.")

def demonstrate_wellness_center_features():
    """Demonstrate key features of the wellness center"""
    
    print("\n🌟 Wellness Center Features Demonstration")
    print("=" * 60)
    
    features = [
        "🔬 Lab Insights Dashboard - Comprehensive analytics for healthcare facilities",
        "📊 Test Volume Analysis - Track test requests and patterns over time",
        "⏱️ Turnaround Time Monitoring - Measure lab efficiency and performance",
        "🦠 Disease Trend Detection - Identify health patterns and outbreaks",
        "🌍 Geographic Distribution - Analyze test distribution by location",
        "💡 AI-Generated Insights - Actionable recommendations for administrators",
        "🎯 Data Quality Scoring - Assess completeness and reliability of data",
        "🔍 Advanced Filtering - Filter by test type, date range, and user groups",
        "📈 Performance Metrics - Daily averages and trend analysis",
        "🚨 Risk Assessment - Categorize health trends by risk level"
    ]
    
    for feature in features:
        print(f"• {feature}")
    
    print(f"\n🏥 Wellness Center Version: {wellness_center._WellnessCenter__class__.__name__} v1.0.0")
    print("✨ Ready for production use!")

if __name__ == "__main__":
    # Run comprehensive tests
    dashboard_result = test_standalone_wellness_center()
    test_api_integration()
    demonstrate_wellness_center_features()
    
    print(f"\n📋 Full Dashboard Data Structure:")
    print("=" * 60)
    if dashboard_result:
        # Show the structure without overwhelming output
        structure_keys = list(dashboard_result.keys())
        print(f"Dashboard contains {len(structure_keys)} main sections:")
        for key in structure_keys:
            print(f"• {key}")
    
    print("\n🎯 Next Steps:")
    print("1. Start the agent server: python agent_server.py")
    print("2. Test the API endpoint: python test_wellness_center_api.py")
    print("3. Access the dashboard via POST /wellness-center/lab-insights-dashboard")
