def calculate_expected_fev1(age, height_cm, race):
    """
    Calculate the expected FEV1 and FVC based on age, height, and race adjustment.
    """
    race_factors = {
        "asian": 0.93,
        "latino": 0.93,
        "hispanic": 0.93,
        "african": 0.87,
        "african american": 0.87,
        "caucasian": 1,
        "middle eastern": 1,
    }

    race_key = race.strip().lower()
    factor = race_factors.get(race_key)
    if factor is None:
        raise ValueError("Invalid race input. Please enter a valid race.")

    expected_fev1 = factor * 1.08 * ((0.0395 * height_cm) - (0.025 * age) - 2.6)
    expected_fvc = factor * 1.15 * ((0.0443 * height_cm) - (0.026 * age) - 2.89)

    return expected_fev1, expected_fvc

def calculate_pef(age, height_cm, gender):
    """
    Estimate Peak Expiratory Flow Rate (PEFR) based on gender and age.
    Uses separate formulas for children, adult males, and adult females.
    Height is assumed to be in centimeters.
    """
    height_m = height_cm / 100  # Convert to meters

    if age < 18:
        # Child formula
        pef = (height_cm - 100) * 5 + 100
    else:
        gender_key = gender.strip().lower()
        if gender_key == "male":
            pef = (((height_m * 5.48) + 1.58) - (age * 0.041)) * 60
        elif gender_key == "female":
            pef = (((height_m * 3.72) + 2.24) - (age * 0.03)) * 60
        else:
            raise ValueError("Invalid gender. Please enter 'Male' or 'Female'.")
    
    return pef

def main():
    print("🔹 Peak Flow Calculator 🔹")
    
    age = int(input("Enter your age (years): "))
    height_cm = float(input("Enter your height (cm): "))
    race = input("Enter your race (Asian/Latino/Hispanic, African, Caucasian, Middle Eastern): ")
    gender = input("Enter your gender (Male/Female): ")

    expected_fev1, expected_fvc = calculate_expected_fev1(age, height_cm, race)
    estimated_pef = calculate_pef(age, height_cm, gender)

    print("\n🔹 RESULTS 🔹")
    print(f"🔹 Expected FEV1: {expected_fev1:.2f} L")
    print(f"🔹 Expected FVC: {expected_fvc:.2f} L")
    print(f"🔹 Estimated PEF: {estimated_pef:.2f} L/min")

# Run the calculator
if __name__ == "__main__":
    main()
