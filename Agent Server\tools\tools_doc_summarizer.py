from io import BytesIO
from fastapi import UploadFile

def extract_text_from_pdf(pdf_bytes: bytes) -> str:
    """Extract text from PDF bytes - simplified version"""
    try:
        from PyPDF2 import PdfReader
        reader = PdfReader(BytesIO(pdf_bytes))
        return " ".join([page.extract_text() or "" for page in reader.pages])
    except ImportError:
        return "PDF text extraction requires PyPDF2 library. Please install it with: pip install PyPDF2"
    except Exception as e:
        return f"Error extracting PDF text: {str(e)}"

def extract_text_from_docx(docx_bytes: bytes) -> str:
    """Extract text from DOCX bytes - simplified version"""
    try:
        from docx import Document
        doc = Document(BytesIO(docx_bytes))
        return "\n".join([para.text for para in doc.paragraphs])
    except ImportError:
        return "DOCX text extraction requires python-docx library. Please install it with: pip install python-docx"
    except Exception as e:
        return f"Error extracting DOCX text: {str(e)}"

def extract_text_from_upload(file: UploadFile) -> str:
    """Extract text from uploaded file - supports PDF and DOCX"""
    try:
        contents = file.file.read()
        if file.filename.lower().endswith(".pdf"):
            return extract_text_from_pdf(contents)
        elif file.filename.lower().endswith(".docx"):
            return extract_text_from_docx(contents)
        else:
            raise ValueError("Unsupported file type. Only PDF and DOCX are supported.")
    except Exception as e:
        return f"Error processing uploaded file: {str(e)}"

def summarize_medical_text(text: str, model: str = "default") -> str:
    """Summarize medical text - simplified version"""
    if len(text) < 100:
        return "Document too short for meaningful summary."

    # Simple text truncation for now - can be enhanced with actual AI summarization
    if len(text) > 10000:
        summary_text = text[:10000] + "..."
    else:
        summary_text = text

    return f"📄 Summary (using {model}):\n\n{summary_text}"
