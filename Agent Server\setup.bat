@echo off
echo Starting setup process...

:: Step 1: Open Ollama download page
echo Opening Ollama download page in your default browser...
start https://ollama.com/download/windows
echo Please download and install Ollama manually before proceeding...
pause

:: Step 2: Verify Ollama installation
echo Checking if Ollama is installed...
where ollama >nul 2>&1
if %errorlevel% neq 0 (
    echo Ollama is not installed. Please install it from the website before proceeding.
    exit /b 1
)

:: Step 3: Pull the Qwen2.5 model
echo Pulling the qwen2.5:1.5b model...
ollama pull qwen2.5:1.5b
if %errorlevel% neq 0 (
    echo Failed to pull qwen2.5:1.5b model. Exiting.
    exit /b %errorlevel%
)

:: Step 4: Pull the DeepSeek model
echo Pulling the deepseek-r1:1.5b model...
ollama pull deepseek-r1:1.5b
if %errorlevel% neq 0 (
    echo Failed to pull deepseek-r1:1.5b model. Exiting.
    exit /b %errorlevel%
)

:: Step 5: Confirm Qwen model is downloaded
echo Confirming the qwen2.5:1.5b model download...
ollama list | find "qwen2.5:1.5b" >nul 2>&1
if %errorlevel% neq 0 (
    echo Model qwen2.5:1.5b not found. Please ensure it was downloaded successfully and try again.
    exit /b %errorlevel%
)
echo Qwen2.5 model found!

:: Step 6: Confirm DeepSeek model is downloaded
echo Confirming the deepseek-r1:1.5b model download...
ollama list | find "deepseek-r1:1.5b" >nul 2>&1
if %errorlevel% neq 0 (
    echo Model deepseek-r1:1.5b not found. Please ensure it was downloaded successfully and try again.
    exit /b %errorlevel%
)
echo DeepSeek model found!

:: Step 7: Install Python dependencies from requirements.txt
echo Installing Python dependencies from requirements.txt...
pip install --no-cache-dir -r requirements.txt
if %errorlevel% neq 0 (
    echo Failed to install Python packages. Exiting.
    exit /b %errorlevel%
)
echo Python dependencies installed successfully!

:: Step 8: Run the FastAPI server
echo Running the FastAPI application...
python agent_server.py
if %errorlevel% neq 0 (
    echo Failed to run the FastAPI application. Exiting.
    exit /b %errorlevel%
)

:: Step 9: Run the Streamlit application
echo Running the Streamlit application...
streamlit run agent_app.py
if %errorlevel% neq 0 (
    echo Failed to run the Streamlit application. Exiting.
    exit /b %errorlevel%
)

echo Setup completed successfully!
pause
