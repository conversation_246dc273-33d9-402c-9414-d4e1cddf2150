import datetime
import json
import time

# --- Constants ---
SYMPTOMS_LIST = [
    "Light spotting (pink or brown)", "Mild cramping",
    "Moderate cramps or back pain with bleeding", "Heavy bleeding with clots + strong cramps",
    "Sharp, stabbing pain on one side + dizziness", "Painless, bright red bleeding",
    "Severe, constant abdominal pain + bleeding", "Decreased fetal movements",
    "Bloody show (mucus mixed with blood)", "Severe headaches + swelling or vision changes"
]

CHECKLISTS = {
    "Nursery Preparation Progress": [
        "Choose a room for the nursery", "Paint and decorate the nursery",
        "Set up crib or bassinet", "Install baby monitor", "Install blackout curtains",
        "Assemble changing table and storage", "Organize baby clothes by size",
        "Set up diaper changing station", "Ensure proper room temperature control",
        "Baby-proof electrical outlets and furniture", "Add a rocking chair or nursing station"
    ],
    "Baby Item Shopping List": [
        "Newborn onesies (5–7)", "Sleepers or footed pajamas (4–6)",
        "Socks or booties (4 pairs)", "Hats (2–3)", "Swaddles and receiving blankets",
        "Bottles and nipples (if bottle feeding)", "Breast pump (if breastfeeding)",
        "Nursing bras and pads", "Burp cloths (6+)", "Newborn diapers (2–3 packs)",
        "Baby wipes", "Diaper cream", "Changing pad and covers", "Baby bathtub",
        "Baby shampoo and wash", "Soft towels and washcloths", "Infant car seat (installed)",
        "Diaper bag", "Baby carrier or wrap", "Stroller"
    ],
    "Hospital Bag Packing Checklist": [
        "ID, insurance, birth plan", "Comfortable clothing and robe", "Nursing bras/tanks",
        "Maternity underwear", "Toiletries (toothbrush, shampoo, lip balm)",
        "Phone & charger", "Snacks and water bottle", "Slippers or flip flops",
        "Newborn outfit (2–3)", "Swaddle or blanket", "Diapers and wipes", "Hat and socks",
        "Car seat (for discharge)", "Change of clothes for partner", "Toiletries for partner",
        "Snacks and drinks for partner", "Entertainment (books, tablet, etc.) for partner",
        "List of emergency contacts"
    ],
    "Parental Leave & Insurance Planning": [
        "Notify HR of pregnancy and due date", "Review company’s maternity/paternity leave policy",
        "Submit formal parental leave request", "Set up coverage or handover at work",
        "Understand health insurance coverage (prenatal visits, delivery)",
        "Add baby to insurance plan post-birth", "Apply for family leave (FMLA or local policy)",
        "Set financial budget for maternity leave", "Plan for any additional childcare or household help",
        "Explore benefits for breastfeeding support (lactation consultation, pump coverage)"
    ]
}

# --- Helper Functions ---
def calculate_gestational_age(lmp_date, conception_date=None):
    today = datetime.date.today()
    start_date = conception_date if conception_date else lmp_date
    delta = (today - start_date).days
    return delta // 7, delta % 7


def predict_diagnosis(symptoms, gestational_age):
    weeks, _ = gestational_age
    trimester = (
        "First Trimester" if weeks <= 12 else
        "Second Trimester" if weeks <= 27 else
        "Third Trimester"
    )
    trimester_risks = {
        "First Trimester": {
            "Light spotting (pink or brown)": "Implantation Bleeding (Low Risk - Normal in early pregnancy)",
            "Mild cramping": "Implantation Bleeding (Low Risk - Normal in early pregnancy)",
            "Moderate cramps or back pain with bleeding": "Threatened Miscarriage (Moderate Risk - Needs monitoring & rest)",
            "Heavy bleeding with clots + strong cramps": "Miscarriage (High Risk - Urgent medical attention required)",
            "Sharp, stabbing pain on one side + dizziness": "Ectopic Pregnancy (Critical Risk - Immediate medical attention required)",
        },
        "Second Trimester": {
            "Moderate cramps or back pain with bleeding": "Possible Cervical Insufficiency (Moderate Risk - Needs medical review)",
            "Painless, bright red bleeding": "Placenta Previa (High Risk - Requires hospital monitoring)",
            "Severe, constant abdominal pain + bleeding": "Placental Abruption (Critical Risk - Immediate medical help needed)",
        },
        "Third Trimester": {
            "Bloody show (mucus mixed with blood)": "Labor-Related Bleeding (Low Risk - Normal before labor)",
            "Painless, bright red bleeding": "Placenta Previa (High Risk - Requires hospital monitoring)",
            "Severe, constant abdominal pain + bleeding": "Placental Abruption (Critical Risk - Immediate medical help needed)",
            "Decreased fetal movements": "Potential Fetal Distress (Critical Risk - Seek immediate care)",
            "Severe headaches + swelling or vision changes": "Possible Preeclampsia (Critical Risk - Contact doctor immediately)",
        },
    }
    diagnoses = [trimester_risks.get(trimester, {}).get(s, f"Symptom '{s}' not typical in {trimester}") for s in symptoms]
    return diagnoses

def expected_delivery_window(lmp_date):
    return lmp_date + datetime.timedelta(weeks=37), lmp_date + datetime.timedelta(weeks=42)

def contraction_timer():
    print("\nContraction Timer Started. Press Enter to start/stop timing each contraction.")
    times = []
    try:
        while True:
            input("Press Enter to log a contraction time...")
            current_time = datetime.datetime.now()
            times.append(current_time)
            print(f"Logged at {current_time.strftime('%H:%M:%S')}")

            if len(times) > 1:
                intervals = [(times[i] - times[i - 1]).seconds for i in range(1, len(times))]
                print("Contraction intervals (seconds):", intervals)
    except KeyboardInterrupt:
        print("\nTimer stopped.")

def kick_counter():
    count = 0
    try:
        while True:
            cmd = input("Type 'kick' to log a kick, 'reset' to reset, or 'done' to finish: ").lower()
            if cmd == 'kick':
                count += 1
                print(f"Kicks logged: {count}")
            elif cmd == 'reset':
                count = 0
                print("Kick count reset.")
            elif cmd == 'done':
                break
    except KeyboardInterrupt:
        print("\nKick counter stopped.")

def save_user_data(data):
    with open("pregnancy_tracker_data.json", "w") as f:
        json.dump(data, f)

def load_user_data():
    try:
        with open("pregnancy_tracker_data.json", "r") as f:
            return json.load(f)
    except FileNotFoundError:
        return {}

def show_trimester_progress(weeks):
    if weeks <= 12:
        trimester = "First Trimester"
        progress = (weeks / 12) * 100
    elif weeks <= 27:
        trimester = "Second Trimester"
        progress = ((weeks - 13) / (27 - 13)) * 100
    else:
        trimester = "Third Trimester"
        progress = ((weeks - 28) / (40 - 28)) * 100
    print(f"\nCurrent Trimester: {trimester}")
    print(f"Progress: {int(progress)}% complete")

def show_ultrasound_and_gender_timing(weeks):
    print("\n📅 Key Pregnancy Milestones:")
    if 8 <= weeks <= 12:
        print("✅ Time for First Ultrasound (Week 8–12)")
    elif weeks < 8:
        print("ℹ️ First Ultrasound usually between Week 8–12")

    if 18 <= weeks <= 22:
        print("🎉 Time for Gender Reveal Scan (Week 18–22)")
    elif weeks < 18:
        print("ℹ️ Gender Reveal usually between Week 18–22")

def display_checklist(title, items):
    print(f"\n📋 {title}")
    completed = [False] * len(items)
    for i, item in enumerate(items):
        response = input(f"{item} [y/n]: ").strip().lower()
        completed[i] = response == "y"
    done = sum(completed)
    print(f"{done}/{len(items)} tasks completed")

# --- Main Program ---
def main():
    print("🤰 Pregnancy Tracker CLI")
    user_data = load_user_data()

    lmp_input = input("Enter Last Menstrual Period (YYYY-MM-DD): ")
    lmp_date = datetime.datetime.strptime(lmp_input, "%Y-%m-%d").date()
    conception_date = lmp_date + datetime.timedelta(days=14)
    print(f"Estimated Conception Date: {conception_date}")

    gestational_age = calculate_gestational_age(lmp_date)
    print(f"Gestational Age: {gestational_age[0]} weeks and {gestational_age[1]} days")

    show_trimester_progress(gestational_age[0])
    show_ultrasound_and_gender_timing(gestational_age[0])

    print("\n--- Symptom Checker ---")
    for i, s in enumerate(SYMPTOMS_LIST, 1):
        print(f"{i}. {s}")
    selected_indices = input("Enter symptom numbers separated by commas (e.g., 1,3,6): ")
    selected_symptoms = [SYMPTOMS_LIST[int(i.strip()) - 1] for i in selected_indices.split(",")]
    diagnosis = predict_diagnosis(selected_symptoms, gestational_age)
    print("\nDiagnosis:")
    for d in diagnosis:
        print(f"→ {d}")


    edd_start, edd_end = expected_delivery_window(lmp_date)
    print(f"\nExpected Delivery Window: {edd_start} to {edd_end}")

    action = input("\nStart Contraction Timer? (y/n): ").lower()
    if action == 'y':
        contraction_timer()

    action = input("\nStart Kick Counter? (y/n): ").lower()
    if action == 'y':
        kick_counter()

    for checklist_title, checklist_items in CHECKLISTS.items():
        display_checklist(checklist_title, checklist_items)

    save_user_data(user_data)

if __name__ == "__main__":
    main()

# ****** STREAMLIT VERSION COMMENTED OUT *******


# import streamlit as st
# import datetime
# import json
# import time


# # --- Constants ---
# SYMPTOMS_LIST = [
#     "Light spotting (pink or brown)", "Mild cramping",
#     "Moderate cramps or back pain with bleeding", "Heavy bleeding with clots + strong cramps",
#     "Sharp, stabbing pain on one side + dizziness", "Painless, bright red bleeding",
#     "Severe, constant abdominal pain + bleeding", "Decreased fetal movements",
#     "Bloody show (mucus mixed with blood)", "Severe headaches + swelling or vision changes"
# ]

# CHECKLISTS = {
#     "Nursery Preparation Progress": [
#         "Choose a room for the nursery", "Paint and decorate the nursery",
#         "Set up crib or bassinet", "Install baby monitor", "Install blackout curtains",
#         "Assemble changing table and storage", "Organize baby clothes by size",
#         "Set up diaper changing station", "Ensure proper room temperature control",
#         "Baby-proof electrical outlets and furniture", "Add a rocking chair or nursing station"
#     ],
#     "Baby Item Shopping List": [
#         "Newborn onesies (5–7)", "Sleepers or footed pajamas (4–6)",
#         "Socks or booties (4 pairs)", "Hats (2–3)", "Swaddles and receiving blankets",
#         "Bottles and nipples (if bottle feeding)", "Breast pump (if breastfeeding)",
#         "Nursing bras and pads", "Burp cloths (6+)", "Newborn diapers (2–3 packs)",
#         "Baby wipes", "Diaper cream", "Changing pad and covers", "Baby bathtub",
#         "Baby shampoo and wash", "Soft towels and washcloths", "Infant car seat (installed)",
#         "Diaper bag", "Baby carrier or wrap", "Stroller"
#     ],
#     "Hospital Bag Packing Checklist": [
#         "ID, insurance, birth plan", "Comfortable clothing and robe", "Nursing bras/tanks",
#         "Maternity underwear", "Toiletries (toothbrush, shampoo, lip balm)",
#         "Phone & charger", "Snacks and water bottle", "Slippers or flip flops",
#         "Newborn outfit (2–3)", "Swaddle or blanket", "Diapers and wipes", "Hat and socks",
#         "Car seat (for discharge)", "Change of clothes for partner", "Toiletries for partner",
#         "Snacks and drinks for partner", "Entertainment (books, tablet, etc.) for partner",
#         "List of emergency contacts"
#     ],
#     "Parental Leave & Insurance Planning": [
#         "Notify HR of pregnancy and due date", "Review company’s maternity/paternity leave policy",
#         "Submit formal parental leave request", "Set up coverage or handover at work",
#         "Understand health insurance coverage (prenatal visits, delivery)",
#         "Add baby to insurance plan post-birth", "Apply for family leave (FMLA or local policy)",
#         "Set financial budget for maternity leave", "Plan for any additional childcare or household help",
#         "Explore benefits for breastfeeding support (lactation consultation, pump coverage)"
#     ]
# }


# # --- Helper Functions ---

# def calculate_gestational_age(lmp_date, conception_date=None):
#     today = datetime.date.today()
#     start_date = conception_date if conception_date else lmp_date
#     delta = (today - start_date).days
#     return delta // 7, delta % 7


# def predict_diagnosis(symptoms, gestational_age):
#     weeks, _ = gestational_age
#     trimester = (
#         "First Trimester" if weeks <= 12 else
#         "Second Trimester" if weeks <= 27 else
#         "Third Trimester"
#     )

#     trimester_risks = {
#         "First Trimester": {
#             "Light spotting (pink or brown)": "Implantation Bleeding (Low Risk - Normal in early pregnancy)",
#             "Mild cramping": "Implantation Bleeding (Low Risk - Normal in early pregnancy)",
#             "Moderate cramps or back pain with bleeding": "Threatened Miscarriage (Moderate Risk - Needs monitoring & rest)",
#             "Heavy bleeding with clots + strong cramps": "Miscarriage (High Risk - Urgent medical attention required)",
#             "Sharp, stabbing pain on one side + dizziness": "Ectopic Pregnancy (Critical Risk - Immediate medical attention required)",
#         },
#         "Second Trimester": {
#             "Moderate cramps or back pain with bleeding": "Possible Cervical Insufficiency (Moderate Risk - Needs medical review)",
#             "Painless, bright red bleeding": "Placenta Previa (High Risk - Requires hospital monitoring)",
#             "Severe, constant abdominal pain + bleeding": "Placental Abruption (Critical Risk - Immediate medical help needed)",
#         },
#         "Third Trimester": {
#             "Bloody show (mucus mixed with blood)": "Labor-Related Bleeding (Low Risk - Normal before labor)",
#             "Painless, bright red bleeding": "Placenta Previa (High Risk - Requires hospital monitoring)",
#             "Severe, constant abdominal pain + bleeding": "Placental Abruption (Critical Risk - Immediate medical help needed)",
#             "Decreased fetal movements": "Potential Fetal Distress (Critical Risk - Seek immediate care)",
#             "Severe headaches + swelling or vision changes": "Possible Preeclampsia (Critical Risk - Contact doctor immediately)",
#         },
#     }

#     diagnoses = []
#     unmatched = []

#     for symptom in symptoms:
#         diagnosis = trimester_risks.get(trimester, {}).get(symptom)
#         if diagnosis:
#             diagnoses.append(diagnosis)
#         else:
#             unmatched.append(symptom)

#     if diagnoses:
#         return diagnoses
#     if unmatched:
#         return [f"Symptoms ({', '.join(unmatched)}) not typically associated with the {trimester}. Consult your doctor."]
#     return ["No high-risk symptoms detected."]

# def expected_delivery_window(lmp_date):
#     return lmp_date + datetime.timedelta(weeks=37), lmp_date + datetime.timedelta(weeks=42)

# def kick_counter():
#     if "kick_count" not in st.session_state:
#         st.session_state.kick_count = 0
#     if st.button("Log Kick"):
#         st.session_state.kick_count += 1
#     st.write(f"Total kicks logged: {st.session_state.kick_count}")
#     if st.button("Reset Counter"):
#         st.session_state.kick_count = 0


# def contraction_timer():
#     if "contraction_times" not in st.session_state:
#         st.session_state.contraction_times = []
#     if "contraction_start_time" not in st.session_state:
#         st.session_state.contraction_start_time = None
#     if "contraction_active" not in st.session_state:
#         st.session_state.contraction_active = False

#     start_clicked = st.button("Start Contraction")
#     if start_clicked:
#         st.session_state.contraction_start_time = datetime.datetime.now()
#         st.session_state.contraction_times.append(st.session_state.contraction_start_time)
#         st.session_state.contraction_active = True

#     stop_clicked = st.button("Stop Contraction")
#     if stop_clicked:
#         st.session_state.contraction_active = False
#         st.session_state.contraction_start_time = None

#     if st.session_state.contraction_active and st.session_state.contraction_start_time:
#         timer_placeholder = st.empty()
#         while st.session_state.contraction_active:
#             elapsed = datetime.datetime.now() - st.session_state.contraction_start_time
#             timer_placeholder.info(f"⏱️ Contraction Duration: {elapsed.seconds} seconds")
#             time.sleep(1)
#             st.rerun()
#   # Re-runs the script to update the timer

#     if len(st.session_state.contraction_times) > 1:
#         intervals = [
#             (st.session_state.contraction_times[i] - st.session_state.contraction_times[i-1]).seconds
#             for i in range(1, len(st.session_state.contraction_times))
#         ]
#         st.write("Contraction intervals (seconds):", intervals)

#     if st.button("Reset Timer"):
#         st.session_state.contraction_times = []
#         st.session_state.contraction_start_time = None
#         st.session_state.contraction_active = False


# def save_user_data(data):
#     with open("pregnancy_tracker_data.json", "w") as f:
#         json.dump(data, f)

# def load_user_data():
#     try:
#         with open("pregnancy_tracker_data.json", "r") as f:
#             return json.load(f)
#     except FileNotFoundError:
#         return {}

# def show_trimester_progress(weeks):
#     st.subheader("Trimester Progress")
#     if weeks <= 12:
#         trimester = "First Trimester"
#         progress = (weeks / 12) * 100
#     elif weeks <= 27:
#         trimester = "Second Trimester"
#         progress = ((weeks - 13) / (27 - 13)) * 100
#     else:
#         trimester = "Third Trimester"
#         progress = ((weeks - 28) / (40 - 28)) * 100

#     safe_progress = max(0, min(int(progress), 100))
#     st.write(f"Current Trimester: **{trimester}**")
#     st.progress(safe_progress, text=f"{safe_progress}% complete")

# def show_ultrasound_and_gender_timing(weeks):
#     st.subheader("📅 Key Pregnancy Milestones")
#     if 8 <= weeks <= 12:
#         st.info("✅ It's time for your **First Ultrasound** (Week 8–12). Schedule it with your doctor.")
#     elif weeks < 8:
#         st.info("ℹ️ First Ultrasound is typically recommended between **Week 8–12**.")
    
#     if 18 <= weeks <= 22:
#         st.success("🎉 It's time for your **Gender Reveal Scan** (Week 18–22).")
#     elif weeks < 18:
#         st.info("ℹ️ Gender Reveal Ultrasound is usually between **Week 18–22**.")

# def display_checklist(title, items):
#     st.subheader(f"📋 {title}")
#     checklist_key = f"{title}_completed"
#     if checklist_key not in st.session_state:
#         st.session_state[checklist_key] = [False] * len(items)

#     for i, item in enumerate(items):
#         st.session_state[checklist_key][i] = st.checkbox(
#             item, value=st.session_state[checklist_key][i], key=f"{title}_{i}"
#         )
#     completed = sum(st.session_state[checklist_key])
#     st.success(f"{completed}/{len(items)} tasks completed")

# # --- Main App ---
# def main():
#     st.title("🤰 Pregnancy Tracker")
#     user_data = load_user_data()

#     # --- Gestational Age ---
#     st.header("🧮 Gestational Age Calculator")
#     lmp_date = st.date_input("Last Menstrual Period (LMP)")
#     conception_date = lmp_date + datetime.timedelta(days=14)
#     st.write(f"Estimated Conception Date: {conception_date}")

#     weeks, days = calculate_gestational_age(lmp_date, conception_date)
#     st.success(f"Gestational Age: {weeks} weeks, {days} days")
#     user_data["gestational_age"] = (weeks, days)
#     save_user_data(user_data)

#     # --- Trimester Progress ---
#     show_trimester_progress(weeks)

#     # --- Milestones ---
#     show_ultrasound_and_gender_timing(weeks)

#     # --- Expected Delivery Window ---
#     st.header("🗓️ Expected Delivery Window")
#     edd_start, edd_end = expected_delivery_window(lmp_date)
#     st.write(f"Expected Range: **{edd_start.strftime('%B %d, %Y')}** to **{edd_end.strftime('%B %d, %Y')}**")


#     # --- Risk Prediction ---
#     st.header("🚨 Pregnancy Risk Prediction")
#     symptoms = st.multiselect("Select Symptoms", SYMPTOMS_LIST)
#     if st.button("Check Risks"):
#         if "gestational_age" in user_data:
#             alerts = predict_diagnosis(symptoms, user_data["gestational_age"])
#             for alert in alerts:
#                 st.warning(alert)
#         else:
#             st.error("Please calculate gestational age first.")

#     # --- Fetal Kick Counter ---
#     st.header("👶 Fetal Kick Counter")
#     kick_counter()

#     # --- Contraction Timer ---
#     st.header("⏱️ Contraction Timer")
#     contraction_timer()

# # Checklists Section
#     st.header("🗂️ Pregnancy Preparation Checklists")
#     for title, items in CHECKLISTS.items():
#         display_checklist(title, items)

# # --- Run App ---
# if __name__ == "__main__":
#     main()




