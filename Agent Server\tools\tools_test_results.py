"""
Malaria & Widal Result Explainer Tool

This tool interprets malaria and widal test results, explains their significance,
and flags urgent cases that require immediate medical attention. It uses pattern
recognition to identify critical combinations and a sophisticated urgency model
to determine when medical attention is needed.
"""

import json
import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple, Set

# === Pattern Matcher Module ===
def identify_critical_patterns(test_results: Dict[str, Any], patient_age: int, patient_sex: str) -> Dict[str, Any]:
    """
    Identify critical patterns in test results that may indicate severe conditions.

    Args:
        test_results: Dictionary containing all test results
        patient_age: Patient's age in years
        patient_sex: Patient's biological sex (Male or Female)

    Returns:
        Dictionary containing identified patterns, severity, and confidence level
    """
    patterns = []
    severity = "Low"
    confidence = 0.0
    urgent_action_needed = False

    # Check for malaria and widal co-infection
    malaria_positive = False
    typhoid_positive = False
    malaria_species = None

    if "malaria" in test_results and test_results["malaria"].lower() == "positive":
        malaria_positive = True
        if "malaria_species" in test_results:
            malaria_species = test_results["malaria_species"]

    if "widal" in test_results:
        widal_results = test_results["widal"]
        typhi_o_reactive = False
        typhi_h_reactive = False

        # Check for Typhi O reactivity
        if "Typhi O" in widal_results:
            reactivity, _ = extract_titer_value(widal_results["Typhi O"])
            if reactivity == "reactive":
                typhi_o_reactive = True

        # Check for Typhi H reactivity
        if "Typhi H" in widal_results:
            reactivity, _ = extract_titer_value(widal_results["Typhi H"])
            if reactivity == "reactive":
                typhi_h_reactive = True

        # Both Typhi O and Typhi H reactive indicates typhoid
        if typhi_o_reactive and typhi_h_reactive:
            typhoid_positive = True

    # Pattern 1: Co-infection of malaria and typhoid
    if malaria_positive and typhoid_positive:
        # If P. falciparum is involved, this is extremely serious
        if malaria_species and ("falciparum" in malaria_species.lower()):
            patterns.append("Severe Malaria and Typhoid Co-infection")
            severity = "High"
            confidence = 0.95
            urgent_action_needed = True
        else:
            patterns.append("Malaria and Typhoid Co-infection")
            severity = "High"
            confidence = 0.9
            urgent_action_needed = True

    # Pattern 2: Severe malaria risk factors
    if malaria_positive and (patient_age < 5 or patient_age > 65):
        patterns.append("Malaria in High-Risk Age Group")
        severity = "High"
        confidence = 0.85
        urgent_action_needed = True

    # Pattern 3: Pregnancy and malaria
    if malaria_positive and patient_sex.lower() == "female" and 15 <= patient_age <= 50:
        patterns.append("Possible Malaria during Reproductive Age")
        severity = "Medium"
        confidence = 0.7

    # Pattern 4: Multiple Salmonella antigens reactive
    if "widal" in test_results:
        reactive_antigens = []
        for antigen, result in test_results["widal"].items():
            reactivity, _ = extract_titer_value(result)
            if reactivity == "reactive":
                reactive_antigens.append(antigen)

        if len(reactive_antigens) >= 3:
            patterns.append("Multiple Salmonella Antigens Reactive")
            severity = "Medium" if severity != "High" else severity
            confidence = 0.75

    # Pattern 5: Falciparum malaria (most severe form)
    if malaria_positive and malaria_species and ("falciparum" in malaria_species.lower()):
        patterns.append("Falciparum Malaria (Severe Form)")
        severity = "High"
        confidence = 0.9
        urgent_action_needed = True

    return {
        "identified_patterns": patterns,
        "pattern_severity": severity,
        "confidence_level": confidence,
        "urgent_action_needed": urgent_action_needed
    }

def analyze_malaria_test(result: str, patient_age: int, patient_sex: str, species: str = None) -> Dict[str, Any]:
    """
    Analyze malaria test results and provide interpretation and recommendations.

    Args:
        result: The test result (Positive, Negative, or Unknown)
        patient_age: Patient's age in years
        patient_sex: Patient's biological sex (Male or Female)
        species: Malaria species if identified (P. falciparum, P. vivax, etc.)

    Returns:
        Dictionary containing interpretation, recommendations, and urgency level
    """
    interpretation = []
    recommendations = []
    urgency_level = "Low"
    flagged = False
    doctor_visit = False

    if result.lower() == "positive":
        # Add species-specific information if available
        if species:
            if species.lower() == "p. falciparum" or species.lower() == "plasmodium falciparum":
                interpretation.append(f"Your malaria test is positive for {species}, which is the most severe form of malaria and can lead to complications if not treated promptly.")
                urgency_level = "High"
                flagged = True
                doctor_visit = True
            elif species.lower() == "p. vivax" or species.lower() == "plasmodium vivax":
                interpretation.append(f"Your malaria test is positive for {species}, which can cause recurring infections if not completely treated.")
                urgency_level = "Medium"
                flagged = True
                doctor_visit = True
            elif species.lower() == "p. malariae" or species.lower() == "plasmodium malariae":
                interpretation.append(f"Your malaria test is positive for {species}, which typically causes a milder form of malaria but still requires treatment.")
                urgency_level = "Medium"
                flagged = True
                doctor_visit = True
            elif species.lower() == "p. ovale" or species.lower() == "plasmodium ovale":
                interpretation.append(f"Your malaria test is positive for {species}, which can remain dormant in the liver and cause relapses if not properly treated.")
                urgency_level = "Medium"
                flagged = True
                doctor_visit = True
            else:
                interpretation.append(f"Your malaria test is positive for {species}, indicating the presence of malaria parasites in your blood.")
                urgency_level = "Medium"
                flagged = True
                doctor_visit = True
        else:
            interpretation.append("Your malaria test is positive, indicating the presence of malaria parasites in your blood.")
            urgency_level = "Medium"
            flagged = True
            doctor_visit = True

        # Determine severity based on age (children and elderly are higher risk)
        if patient_age < 5 or patient_age > 65:
            # Upgrade urgency if not already high
            if urgency_level != "High":
                urgency_level = "High"
            flagged = True
            doctor_visit = True
            interpretation.append("Given your age, malaria can be more severe and requires immediate medical attention.")
            recommendations.append("🚨 Seek immediate medical care at the nearest healthcare facility.")
            recommendations.append("🩺 Antimalarial treatment should be started as soon as possible.")
        else:
            # Only add these if urgency level wasn't already set to high by species
            if urgency_level != "High":
                interpretation.append("Malaria requires prompt treatment to prevent complications.")
                recommendations.append("👨‍⚕️ Consult a healthcare provider within 24 hours for appropriate antimalarial medication.")

        # Add general recommendations for positive malaria
        recommendations.append("🦟 Use bed nets and insect repellent to prevent mosquito bites.")
        recommendations.append("💧 Eliminate standing water around your home to reduce mosquito breeding sites.")
        recommendations.append("🌡️ Monitor your temperature regularly and stay well-hydrated.")

        # Add species-specific treatment recommendations
        if species:
            if species.lower() == "p. falciparum" or species.lower() == "plasmodium falciparum":
                recommendations.append("🩺 Treatment typically includes artemisinin-based combination therapies (ACTs).")
                recommendations.append("⚠️ P. falciparum can rapidly lead to severe malaria, so prompt treatment is essential.")
            elif species.lower() == "p. vivax" or species.lower() == "plasmodium vivax" or species.lower() == "p. ovale" or species.lower() == "plasmodium ovale":
                recommendations.append("🩺 Treatment typically includes chloroquine plus primaquine to clear liver stages and prevent relapses.")
                recommendations.append("⚠️ A complete course of treatment is essential to prevent relapses from dormant liver stages.")

        # Add pregnancy-specific recommendations
        if patient_sex.lower() == "female" and 15 <= patient_age <= 50:
            recommendations.append("🤰 If you are pregnant or planning pregnancy, inform your healthcare provider as malaria can be more severe during pregnancy and treatment options may differ.")

    elif result.lower() == "negative":
        interpretation.append("Your malaria test is negative, suggesting no detectable malaria parasites in your blood at this time.")
        recommendations.append("🦟 Continue using preventive measures like bed nets and insect repellent in malaria-endemic areas.")

        # Add note about possible false negatives
        interpretation.append("Note that early infections may not always be detected. If symptoms persist, consider retesting in 24-48 hours.")

    else:  # Unknown or invalid result
        interpretation.append("Your malaria test result is inconclusive or not available.")
        recommendations.append("🔄 Consider retesting to obtain a clear result, especially if you have symptoms like fever, chills, headache, or body aches.")

    return {
        "interpretation": interpretation,
        "recommendations": recommendations,
        "urgency_level": urgency_level,
        "flagged": flagged,
        "doctor_visit_recommended": doctor_visit
    }

def extract_titer_value(result: str) -> Tuple[str, Optional[str]]:
    """
    Extract titer value from a result string if present.

    Args:
        result: Result string (e.g., "Reactive (1:160)")

    Returns:
        Tuple of (reactivity status, titer value if present)
    """
    if not result:
        return ("non-reactive", None)

    result_lower = result.lower()

    # Explicitly check for non-reactive first
    if "non-reactive" in result_lower or "non reactive" in result_lower or "negative" in result_lower:
        return ("non-reactive", None)

    # Check if result contains a titer value in parentheses
    titer_match = re.search(r'reactive.*?\(([^)]+)\)', result_lower)

    if titer_match:
        return ("reactive", titer_match.group(1))
    elif "reactive" in result_lower or "positive" in result_lower:
        return ("reactive", None)
    else:
        return ("non-reactive", None)

def analyze_widal_test(widal_results: Dict[str, str], patient_age: int, patient_sex: str) -> Dict[str, Any]:
    """
    Analyze Widal test results and provide interpretation and recommendations.

    Args:
        widal_results: Dictionary containing reactivity status for different Salmonella antigens
                      (Typhi O, Typhi H, Paratyphi A,H, Paratyphi B,H)
        patient_age: Patient's age in years
        patient_sex: Patient's biological sex (Male or Female)

    Returns:
        Dictionary containing interpretation, recommendations, and urgency level
    """
    interpretation = []
    recommendations = []
    urgency_level = "Low"
    flagged = False
    doctor_visit = False

    # Check if we have any reactive results and extract titer values
    reactive_antigens = []
    titer_values = {}

    for antigen, result in widal_results.items():
        # Skip if result is None or empty
        if not result:
            continue

        reactivity, titer = extract_titer_value(result)
        if reactivity == "reactive":
            reactive_antigens.append(antigen)
            if titer:
                titer_values[antigen] = titer

    # Interpret based on reactive antigens
    if reactive_antigens:
        # Format the list of reactive antigens for display with titer values if available
        antigen_display = []
        for antigen in reactive_antigens:
            if antigen in titer_values:
                antigen_display.append(f"{antigen} ({titer_values[antigen]})")
            else:
                antigen_display.append(antigen)

        antigens_str = ", ".join(antigen_display)
        interpretation.append(f"Your Widal test shows reactivity to {antigens_str}.")

        # Check for Typhoid fever pattern (both Typhi O and Typhi H reactive)
        if "Typhi O" in reactive_antigens and "Typhi H" in reactive_antigens:
            interpretation.append("Reactivity to both Typhi O and Typhi H antigens strongly suggests typhoid fever infection. This indicates your immune system is responding to Salmonella Typhi bacteria.")
            flagged = True
            doctor_visit = True

            # Determine urgency based on titer values if available
            if "Typhi O" in titer_values and "Typhi H" in titer_values:
                try:
                    # Extract numerical values from titer strings like "1:160"
                    o_titer = int(titer_values["Typhi O"].split(":")[-1]) if ":" in titer_values["Typhi O"] else 0
                    h_titer = int(titer_values["Typhi H"].split(":")[-1]) if ":" in titer_values["Typhi H"] else 0

                    if o_titer >= 160 and h_titer >= 160:
                        urgency_level = "High"
                        interpretation.append(f"Your titer values (O: {titer_values['Typhi O']}, H: {titer_values['Typhi H']}) are significantly elevated, suggesting an active infection that requires prompt medical attention.")
                    else:
                        urgency_level = "Medium"
                        interpretation.append(f"Your titer values (O: {titer_values['Typhi O']}, H: {titer_values['Typhi H']}) suggest an immune response to Salmonella Typhi.")
                except (ValueError, IndexError):
                    # If we can't parse the titer values, default to medium urgency
                    urgency_level = "Medium"
            else:
                urgency_level = "Medium"

            # Add more detailed explanation
            interpretation.append("The O (somatic) antigen reactivity indicates current infection, while H (flagellar) antigen reactivity suggests your immune system has been mounting a response for some time.")

            # Treatment recommendations based on severity
            if urgency_level == "High":
                recommendations.append("🚨 Consult a healthcare provider within 24 hours for appropriate antibiotic treatment.")
            else:
                recommendations.append("👨‍⚕️ Consult a healthcare provider within 24-48 hours for appropriate antibiotic treatment.")

            recommendations.append("🩺 Treatment typically includes antibiotics such as fluoroquinolones (e.g., ciprofloxacin), third-generation cephalosporins (e.g., ceftriaxone), or azithromycin.")

            # Add fever-specific recommendations
            recommendations.append("🌡️ Monitor your temperature every 4-6 hours. Fever in typhoid typically rises gradually and may reach 103-104°F (39-40°C).")

            # Add hydration recommendations
            recommendations.append("💧 Maintain proper hydration with clean water or oral rehydration solution, especially if you have fever or diarrhea.")

        # Check for single Typhi antigen reactivity
        elif "Typhi O" in reactive_antigens or "Typhi H" in reactive_antigens:
            if "Typhi O" in reactive_antigens:
                interpretation.append("Reactivity to Typhi O antigen alone may indicate early typhoid infection or past exposure to Salmonella Typhi bacteria.")

                # Check titer value for O antigen
                if "Typhi O" in titer_values:
                    try:
                        o_titer = int(titer_values["Typhi O"].split(":")[-1]) if ":" in titer_values["Typhi O"] else 0
                        if o_titer >= 160:
                            interpretation.append(f"Your O antigen titer ({titer_values['Typhi O']}) is elevated, which may indicate a recent infection.")
                            urgency_level = "Medium"
                            flagged = True
                    except (ValueError, IndexError):
                        pass
            else:  # Typhi H only
                interpretation.append("Reactivity to Typhi H antigen alone may indicate past typhoid infection, carrier state, or cross-reactivity with other Salmonella species.")

                # Check titer value for H antigen
                if "Typhi H" in titer_values:
                    try:
                        h_titer = int(titer_values["Typhi H"].split(":")[-1]) if ":" in titer_values["Typhi H"] else 0
                        if h_titer >= 160:
                            interpretation.append(f"Your H antigen titer ({titer_values['Typhi H']}) is elevated, which may indicate a past infection or carrier state.")
                    except (ValueError, IndexError):
                        pass

            recommendations.append("👨‍⚕️ Consult a healthcare provider for clinical correlation and possible follow-up testing such as blood culture or PCR.")
            doctor_visit = True

        # Check for Paratyphoid patterns
        paratyphi_antigens = [a for a in reactive_antigens if "Paratyphi" in a]
        if paratyphi_antigens:
            paratyphi_str = ", ".join([f"{a} ({titer_values[a]})" if a in titer_values else a for a in paratyphi_antigens])
            interpretation.append(f"Reactivity to {paratyphi_str} suggests possible paratyphoid fever, caused by Salmonella Paratyphi. This illness is similar to typhoid but often presents with milder symptoms.")
            recommendations.append("👨‍⚕️ Consult a healthcare provider for evaluation and appropriate antibiotic treatment.")
            doctor_visit = True
            flagged = True

            # If both A and B are reactive, increase urgency
            if "Paratyphi A,H" in reactive_antigens and "Paratyphi B,H" in reactive_antigens:
                urgency_level = "Medium"
                interpretation.append("Reactivity to multiple Paratyphi antigens suggests a significant infection that requires medical attention.")

                # Check titer values for Paratyphi antigens
                high_titers = False
                for antigen in ["Paratyphi A,H", "Paratyphi B,H"]:
                    if antigen in titer_values:
                        try:
                            titer = int(titer_values[antigen].split(":")[-1]) if ":" in titer_values[antigen] else 0
                            if titer >= 160:
                                high_titers = True
                        except (ValueError, IndexError):
                            pass

                if high_titers:
                    interpretation.append("The high titer values for Paratyphi antigens suggest an active infection.")

        # Add general recommendations for positive Widal test
        recommendations.append("🧼 Practice strict hand hygiene, especially before eating and after using the toilet.")
        recommendations.append("🥤 Drink only purified or boiled water and avoid raw foods that may be contaminated.")
        recommendations.append("🍽️ Avoid eating food from street vendors or restaurants with questionable hygiene practices.")
        recommendations.append("🚫 Avoid preparing food for others until cleared by a healthcare provider to prevent spreading the infection.")

        # Special considerations for children and elderly
        if patient_age < 5 or patient_age > 65:
            urgency_level = "High"
            interpretation.append("Given your age, typhoid or paratyphoid fever can be more severe and requires immediate medical attention.")
            recommendations.append("🚨 Seek medical care within 24 hours as complications are more common in your age group.")
            recommendations.append("⚠️ Monitor closely for signs of dehydration, confusion, or severe weakness which may indicate complications.")

        # Pregnancy considerations
        if patient_sex.lower() == "female" and 15 <= patient_age <= 50:
            recommendations.append("🤰 If you are pregnant, inform your healthcare provider immediately as typhoid fever requires careful management during pregnancy.")

    else:
        interpretation.append("Your Widal test shows no reactivity to Salmonella antigens, suggesting no evidence of typhoid or paratyphoid fever.")
        recommendations.append("🧼 Continue practicing good hygiene and safe food handling to prevent enteric infections.")
        recommendations.append("🍽️ When traveling to areas where typhoid is common, remember to 'boil it, cook it, peel it, or forget it' to prevent foodborne illness.")

    return {
        "interpretation": interpretation,
        "recommendations": recommendations,
        "urgency_level": urgency_level,
        "flagged": flagged,
        "doctor_visit_recommended": doctor_visit
    }

def interpret_test_results(data_json: str) -> str:
    """
    Interpret test results and provide recommendations.

    Args:
        data_json: JSON string containing test results and patient information

    Returns:
        JSON string with interpretation, recommendations, urgency level, and identified patterns
    """
    try:
        # Parse input data
        data = json.loads(data_json) if isinstance(data_json, str) else data_json

        # Extract test data
        selected_tests = data.get("selected_tests", [])
        patient_age = data.get("patient_age", 30)  # Default age if not provided
        patient_sex = data.get("patient_sex", "Unknown")  # Default sex if not provided

        # Initialize results
        tests_analyzed = []
        all_interpretations = []
        all_recommendations = []
        overall_urgency = "Low"
        flagged_results = []
        doctor_visit_recommended = False

        # Create a copy of data for pattern matching
        test_data_for_patterns = {}

        # Analyze each selected test
        if "Malaria Test" in selected_tests:
            malaria_result = data.get("malaria", "Unknown")
            malaria_species = data.get("malaria_species", None)
            tests_analyzed.append("Malaria Test")

            # Add to pattern matching data
            test_data_for_patterns["malaria"] = malaria_result
            if malaria_species:
                test_data_for_patterns["malaria_species"] = malaria_species

            # Analyze malaria test
            malaria_analysis = analyze_malaria_test(malaria_result, patient_age, patient_sex, malaria_species)

            all_interpretations.extend(malaria_analysis["interpretation"])
            all_recommendations.extend(malaria_analysis["recommendations"])

            if malaria_analysis["urgency_level"] == "High":
                overall_urgency = "High"
            elif malaria_analysis["urgency_level"] == "Medium" and overall_urgency != "High":
                overall_urgency = "Medium"

            if malaria_analysis["flagged"]:
                flagged_results.append("Malaria Test")

            if malaria_analysis["doctor_visit_recommended"]:
                doctor_visit_recommended = True

        if "Widal Test (Typhoid)" in selected_tests:
            widal_results = data.get("widal", {})
            tests_analyzed.append("Widal Test (Typhoid)")

            # Add to pattern matching data
            test_data_for_patterns["widal"] = widal_results

            # Analyze widal test
            widal_analysis = analyze_widal_test(widal_results, patient_age, patient_sex)

            all_interpretations.extend(widal_analysis["interpretation"])
            all_recommendations.extend(widal_analysis["recommendations"])

            if widal_analysis["urgency_level"] == "High":
                overall_urgency = "High"
            elif widal_analysis["urgency_level"] == "Medium" and overall_urgency != "High":
                overall_urgency = "Medium"

            if widal_analysis["flagged"]:
                flagged_results.append("Widal Test (Typhoid)")

            if widal_analysis["doctor_visit_recommended"]:
                doctor_visit_recommended = True

        # Run pattern matcher if we have multiple tests
        identified_patterns = []
        pattern_confidence = 0.0

        if len(selected_tests) > 1:
            # Add patient data to pattern matching
            test_data_for_patterns["patient_age"] = patient_age
            test_data_for_patterns["patient_sex"] = patient_sex

            # Identify critical patterns
            pattern_analysis = identify_critical_patterns(test_data_for_patterns, patient_age, patient_sex)

            identified_patterns = pattern_analysis["identified_patterns"]
            pattern_confidence = pattern_analysis["confidence_level"]

            # Update urgency based on pattern analysis
            if pattern_analysis["pattern_severity"] == "High" and overall_urgency != "High":
                overall_urgency = "High"
            elif pattern_analysis["pattern_severity"] == "Medium" and overall_urgency == "Low":
                overall_urgency = "Medium"

            # Add pattern information to interpretations if patterns were found
            if identified_patterns:
                all_interpretations.append("\nImportant patterns identified in your test results:")
                for pattern in identified_patterns:
                    all_interpretations.append(f"• {pattern}")

                if pattern_analysis["urgent_action_needed"]:
                    all_interpretations.append("\nThese patterns suggest a condition that requires prompt medical attention.")
                    doctor_visit_recommended = True

        # Create doctor-like summary
        doctor_summary = "Hello! I've reviewed your test results, and here's what I'm seeing: "

        if flagged_results or identified_patterns:
            if overall_urgency == "High":
                doctor_summary += "I'm seeing some concerning results that require immediate medical attention. "
            else:
                doctor_summary += "I'm noticing some results that should be discussed with a healthcare provider. "

            # Add pattern information to summary if available
            if identified_patterns:
                pattern_str = ", ".join(identified_patterns)
                doctor_summary += f"I've identified important patterns in your results ({pattern_str}) that provide additional context. "

            doctor_summary += "Let me explain what these results mean and what steps you should consider taking."
        else:
            doctor_summary += "Good news! Your test results look normal. This is reassuring, but remember that these tests are just one part of your overall health picture."

        # Create final result
        result = {
            "timestamp": datetime.now().isoformat(),
            "tests_analyzed": tests_analyzed,
            "interpretation": all_interpretations,
            "recommendations": all_recommendations,
            "urgency_level": overall_urgency,
            "flagged_results": flagged_results,
            "doctor_visit_recommended": doctor_visit_recommended,
            "doctor_summary": doctor_summary,
            "identified_patterns": identified_patterns,
            "pattern_confidence": pattern_confidence
        }

        return json.dumps(result, indent=4)

    except Exception as e:
        error_result = {
            "error": f"Failed to interpret test results: {str(e)}",
            "recommendations": ["⚠️ Unable to process test results. Please consult a healthcare professional."]
        }
        return json.dumps(error_result, indent=4)

# For testing the tool directly
if __name__ == "__main__":
    # Test 1: Malaria positive with species information
    test_data = {
        "selected_tests": ["Malaria Test"],
        "malaria": "Positive",
        "malaria_species": "P. falciparum",
        "patient_age": 30,
        "patient_sex": "Male"
    }

    print("\n=== TEST 1: Malaria Positive (P. falciparum) ===")
    result = interpret_test_results(json.dumps(test_data))
    print(result)

    # Test 2: Widal positive with titer values
    test_data = {
        "selected_tests": ["Widal Test (Typhoid)"],
        "widal": {
            "Typhi O": "Reactive (1:160)",
            "Typhi H": "Reactive (1:320)",
            "Paratyphi A,H": "Non-Reactive",
            "Paratyphi B,H": "Non-Reactive"
        },
        "patient_age": 30,
        "patient_sex": "Male"
    }

    print("\n=== TEST 2: Widal Positive with Titer Values ===")
    result = interpret_test_results(json.dumps(test_data))
    print(result)

    # Test 3: Co-infection pattern (both malaria and typhoid)
    test_data = {
        "selected_tests": ["Malaria Test", "Widal Test (Typhoid)"],
        "malaria": "Positive",
        "malaria_species": "P. vivax",
        "widal": {
            "Typhi O": "Reactive (1:80)",
            "Typhi H": "Reactive (1:160)",
            "Paratyphi A,H": "Non-Reactive",
            "Paratyphi B,H": "Non-Reactive"
        },
        "patient_age": 30,
        "patient_sex": "Male"
    }

    print("\n=== TEST 3: Co-infection Pattern (Malaria + Typhoid) ===")
    result = interpret_test_results(json.dumps(test_data))
    print(result)

    # Test 4: High-risk age group with malaria
    test_data = {
        "selected_tests": ["Malaria Test"],
        "malaria": "Positive",
        "patient_age": 3,
        "patient_sex": "Male"
    }

    print("\n=== TEST 4: High-Risk Age Group with Malaria ===")
    result = interpret_test_results(json.dumps(test_data))
    print(result)

    # Test 5: Pregnancy risk pattern
    test_data = {
        "selected_tests": ["Malaria Test", "Widal Test (Typhoid)"],
        "malaria": "Positive",
        "widal": {
            "Typhi O": "Reactive (1:80)",
            "Typhi H": "Non-Reactive",
            "Paratyphi A,H": "Non-Reactive",
            "Paratyphi B,H": "Non-Reactive"
        },
        "patient_age": 28,
        "patient_sex": "Female"
    }

    print("\n=== TEST 5: Pregnancy Risk Pattern ===")
    result = interpret_test_results(json.dumps(test_data))
    print(result)

    # Test 6: Multiple Paratyphi antigens reactive
    test_data = {
        "selected_tests": ["Widal Test (Typhoid)"],
        "widal": {
            "Typhi O": "Non-Reactive",
            "Typhi H": "Non-Reactive",
            "Paratyphi A,H": "Reactive (1:160)",
            "Paratyphi B,H": "Reactive (1:80)"
        },
        "patient_age": 45,
        "patient_sex": "Male"
    }

    print("\n=== TEST 6: Multiple Paratyphi Antigens Reactive ===")
    result = interpret_test_results(json.dumps(test_data))
    print(result)

    # Test 7: All negative results
    test_data = {
        "selected_tests": ["Malaria Test", "Widal Test (Typhoid)"],
        "malaria": "Negative",
        "widal": {
            "Typhi O": "Non-Reactive",
            "Typhi H": "Non-Reactive",
            "Paratyphi A,H": "Non-Reactive",
            "Paratyphi B,H": "Non-Reactive"
        },
        "patient_age": 35,
        "patient_sex": "Female"
    }

    print("\n=== TEST 7: All Negative Results ===")
    result = interpret_test_results(json.dumps(test_data))
    print(result)
