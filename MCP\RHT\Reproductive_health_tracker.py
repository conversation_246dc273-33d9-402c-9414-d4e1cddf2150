import json
import numpy as np
import pandas as pd
import datetime
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from statsmodels.tsa.arima.model import ARIMA #INSTALL statmodels version 0.12.0 or later using -> 'pip install statsmodels"


DATA_FILE = "user_data.json"
ACTIVITY_FILE = "activity_data.json"

# ---------- JSON Utilities ----------
def load_json(file):
    try:
        with open(file, "r") as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return {}

def save_json(file, data):
    with open(file, "w") as f:
        json.dump(data, f, indent=4)

# ---------- Cycle Handling ----------
def sort_and_recalculate_cycles(cycle_data):
    df = pd.DataFrame(cycle_data)
    df["start_date"] = pd.to_datetime(df["start_date"])
    df = df.sort_values("start_date").reset_index(drop=True)

    cycle_lengths = [28]  # default first cycle
    for i in range(1, len(df)):
        prev = df.loc[i - 1, "start_date"]
        curr = df.loc[i, "start_date"]
        cycle_lengths.append((curr - prev).days)

    df["cycle_length"] = cycle_lengths
    df["end_date"] = df["start_date"] + pd.to_timedelta(df["period_duration"], unit='D')
    df["start_date"] = df["start_date"].dt.strftime('%Y-%m-%d')
    df["end_date"] = df["end_date"].dt.strftime('%Y-%m-%d')
    return df.to_dict(orient="records")

def add_cycle_data(user, start_date, period_duration, luteal_phase, stress, exercise, sleep, weight_change):
    user_data = load_json(DATA_FILE)
    cycle_data = user_data.setdefault(user, {}).setdefault("cycle_data", [])
    
    new_entry = {
        "start_date": pd.to_datetime(start_date).strftime('%Y-%m-%d'),
        "period_duration": period_duration,
        "luteal_phase": luteal_phase,
        "stress": stress,
        "exercise": exercise,
        "sleep": sleep,
        "weight_change": weight_change
    }
    cycle_data.append(new_entry)
    user_data[user]["cycle_data"] = sort_and_recalculate_cycles(cycle_data)
    save_json(DATA_FILE, user_data)

# ---------- ARIMA Forecasting ----------
def preprocess_for_arima(df):
    df = df.copy()
    df["start_date"] = pd.to_datetime(df["start_date"])
    df = df.sort_values("start_date")
    return df["cycle_length"].astype(float)

def train_arima_model(ts):
    train = ts[:-1]
    test = ts[-1:]

    try:
        order = (1, 1, 1) if len(train) < 10 else (2, 1, 1)
        model = ARIMA(train, order=order).fit()
        pred = model.predict(start=len(train), end=len(train), typ='levels')
        metrics = {
            'MAE': mean_absolute_error(test, pred),
            'MSE': mean_squared_error(test, pred),
            'RMSE': np.sqrt(mean_squared_error(test, pred)),
            'R2 Score': r2_score(test, pred),
            'MAPE': np.mean(np.abs((test - pred) / test)) * 100
        }
        return model, metrics
    except Exception as e:
        return None, {"error": str(e)}

def predict_next_cycle(user):
    user_data = load_json(DATA_FILE)
    data = user_data.get(user, {}).get("cycle_data", [])

    if len(data) < 3:
        return "Not enough data for prediction."

    df = pd.DataFrame(data)
    df["start_date"] = pd.to_datetime(df["start_date"])
    df = df.sort_values("start_date").reset_index(drop=True)

    last_cycle = df.iloc[-1]
    last_start_date = last_cycle["start_date"]

    if len(data) == 3:
        mean_cycle_len = round(df["cycle_length"].astype(float).mean())
        metrics = {"Method": "Mean-Based", "Predicted Cycle Length": mean_cycle_len}
    else:
        ts = preprocess_for_arima(df)
        model, metrics = train_arima_model(ts)
        if model is None:
            return "Model training failed."
        mean_cycle_len = round(model.forecast(steps=1).iloc[0])

    next_start = last_start_date + pd.Timedelta(days=mean_cycle_len)
    ovulation = next_start - pd.Timedelta(days=14)
    window = f"{(ovulation - pd.Timedelta(days=2)).strftime('%Y-%m-%d')} to {(ovulation + pd.Timedelta(days=2)).strftime('%Y-%m-%d')}"

    return {
        "Predicted Cycle Length": mean_cycle_len,
        "Next Period Start Date": next_start.strftime('%Y-%m-%d'),
        "Ovulation Date": ovulation.strftime('%Y-%m-%d'),
        "Ovulation Window": window,
        "Evaluation Metrics": pd.DataFrame([metrics]),
        "Training Data": df
    }

# ---------- Activity Tracking ----------
def add_activity(user, date, activity_type, details):
    data = load_json(ACTIVITY_FILE)
    user_acts = data.setdefault(user, {})
    user_acts.setdefault(date, []).append({"type": activity_type, "details": details})
    save_json(ACTIVITY_FILE, data)

def log_activity(user):
    print("\n--- Log Activity ---")
    date = input("Enter date (YYYY-MM-DD): ")
    kind = input("Activity Type (Sex, Water Intake, Exercise, Symptoms, Other): ")

    if kind.lower() == "sex":
        sex_type = input("Sex Type (Unprotected/Protected): ")
        details = f"Type: {sex_type}"
    elif kind.lower() == "water intake":
        litres = input("Water Intake (L): ")
        details = f"Intake: {litres} L"
    elif kind.lower() == "exercise":
        mins = input("Duration (minutes): ")
        details = f"Duration: {mins} min"
    elif kind.lower() == "symptoms":
        symptoms = input("List symptoms (comma-separated): ").split(',')
        details = []
        for s in symptoms:
            s = s.strip()
            severity = input(f"{s} Severity (1-5): ")
            details.append(f"{s} (Severity: {severity})")
        details = ", ".join(details)
    else:
        kind = input("Custom Type: ") or "Other"
        details = input("Details: ")

    add_activity(user, date, kind, details)
    print("Activity saved.\n")

def view_activities(user):
    print("\n--- Activity Log ---")
    data = load_json(ACTIVITY_FILE)
    user_data = data.get(user, {})
    if not user_data:
        print("No activities found.")
        return

    for date, acts in user_data.items():
        for entry in acts:
            print(f"{date} | {entry['type']} | {entry['details']}")

# ---------- Main App ----------
def main():
    print("Welcome to the Health & Cycle Tracker")
    username = input("Enter your username: ").strip()
    if not username:
        print("Username is required.")
        return

    while True:
        print("\nSelect an option:")
        print("1. Add Cycle")
        print("2. Predict Next Cycle")
        print("3. Log Activity")
        print("4. View Activities")
        print("5. Exit")

        option = input("Choice (1-5): ").strip()

        if option == "1":
            print("\n--- Add Menstrual Cycle ---")
            start = input("Start Date (YYYY-MM-DD): ")
            pdur = int(input("Period Duration (1-10): "))
            luteal = int(input("Luteal Phase (10-16): "))
            stress = input("Stress (Low/Moderate/High): ")
            exercise = input("Exercise (None/Light/Moderate/Intense): ")
            sleep = input("Sleep (Poor/Moderate/Good): ")
            weight = input("Weight Change (None/Gained/Lost): ")
            add_cycle_data(username, start, pdur, luteal, stress, exercise, sleep, weight)
            print("Cycle data saved.")

        elif option == "2":
            result = predict_next_cycle(username)
            if isinstance(result, str):
                print(result)
            else:
                for k, v in result.items():
                    print(f"\n{k}:")
                    if isinstance(v, pd.DataFrame):
                        print(v.to_string(index=False))
                    else:
                        print(v)

        elif option == "3":
            log_activity(username)

        elif option == "4":
            view_activities(username)

        elif option == "5":
            print("Goodbye!")
            break

        else:
            print("Invalid choice. Try again.")

if __name__ == "__main__":
    main()



# ******** STREAMLIT VERSION COMMENTED OUT *******

# import streamlit as st
# import json
# import numpy as np
# import pandas as pd
# import datetime
# from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
# from statsmodels.tsa.arima.model import ARIMA

# DATA_FILE = "user_data.json"
# ACTIVITY_FILE = "activity_data.json"

# # ---------- JSON Utilities ----------
# def load_json(file):
#     try:
#         with open(file, "r") as f:
#             return json.load(f)
#     except (FileNotFoundError, json.JSONDecodeError):
#         return {}

# def save_json(file, data):
#     with open(file, "w") as f:
#         json.dump(data, f, indent=4)

# # ---------- Cycle Handling ----------
# def sort_and_recalculate_cycles(cycle_data):
#     df = pd.DataFrame(cycle_data)
#     df["start_date"] = pd.to_datetime(df["start_date"])
#     df = df.sort_values("start_date").reset_index(drop=True)

#     cycle_lengths = [28]  # default first cycle
#     for i in range(1, len(df)):
#         prev = df.loc[i - 1, "start_date"]
#         curr = df.loc[i, "start_date"]
#         cycle_lengths.append((curr - prev).days)

#     df["cycle_length"] = cycle_lengths
#     df["end_date"] = df["start_date"] + pd.to_timedelta(df["period_duration"], unit='D')
#     df["start_date"] = df["start_date"].dt.strftime('%Y-%m-%d')
#     df["end_date"] = df["end_date"].dt.strftime('%Y-%m-%d')
#     return df.to_dict(orient="records")

# def add_cycle_data(user, start_date, period_duration, luteal_phase, stress, exercise, sleep, weight_change):
#     user_data = load_json(DATA_FILE)
#     cycle_data = user_data.setdefault(user, {}).setdefault("cycle_data", [])
    
#     new_entry = {
#         "start_date": pd.to_datetime(start_date).strftime('%Y-%m-%d'),
#         "period_duration": period_duration,
#         "luteal_phase": luteal_phase,
#         "stress": stress,
#         "exercise": exercise,
#         "sleep": sleep,
#         "weight_change": weight_change
#     }
#     cycle_data.append(new_entry)
#     user_data[user]["cycle_data"] = sort_and_recalculate_cycles(cycle_data)
#     save_json(DATA_FILE, user_data)

# # ---------- ARIMA Forecasting ----------
# def preprocess_for_arima(df):
#     df = df.copy()
#     df["start_date"] = pd.to_datetime(df["start_date"])
#     df = df.sort_values("start_date")
#     return df["cycle_length"].astype(float)

# def train_arima_model(ts):
#     train = ts[:-1]
#     test = ts[-1:]

#     try:
#         order = (1, 1, 1) if len(train) < 10 else (2, 1, 1)
#         model = ARIMA(train, order=order).fit()
#         pred = model.predict(start=len(train), end=len(train), typ='levels')
#         metrics = {
#             'MAE': mean_absolute_error(test, pred),
#             'MSE': mean_squared_error(test, pred),
#             'RMSE': np.sqrt(mean_squared_error(test, pred)),
#             'R2 Score': r2_score(test, pred),
#             'MAPE': np.mean(np.abs((test - pred) / test)) * 100
#         }
#         return model, metrics
#     except Exception as e:
#         return None, {"error": str(e)}

# def predict_next_cycle(user):
#     user_data = load_json(DATA_FILE)
#     data = user_data.get(user, {}).get("cycle_data", [])

#     if len(data) < 3:
#         return "Not enough data for prediction."

#     df = pd.DataFrame(data)
#     df["start_date"] = pd.to_datetime(df["start_date"])
#     df = df.sort_values("start_date").reset_index(drop=True)

#     last_cycle = df.iloc[-1]
#     last_start_date = last_cycle["start_date"]

#     if len(data) == 3:
#         mean_cycle_len = round(df["cycle_length"].astype(float).mean())
#         metrics = {"Method": "Mean-Based", "Predicted Cycle Length": mean_cycle_len}
#     else:
#         ts = preprocess_for_arima(df)
#         model, metrics = train_arima_model(ts)
#         if model is None:
#             return "Model training failed."
#         mean_cycle_len = round(model.forecast(steps=1).iloc[0])

#     next_start = last_start_date + pd.Timedelta(days=mean_cycle_len)
#     ovulation = next_start - pd.Timedelta(days=14)
#     window = f"{(ovulation - pd.Timedelta(days=2)).strftime('%Y-%m-%d')} to {(ovulation + pd.Timedelta(days=2)).strftime('%Y-%m-%d')}"

#     return {
#         "Predicted Cycle Length": mean_cycle_len,
#         "Next Period Start Date": next_start.strftime('%Y-%m-%d'),
#         "Ovulation Date": ovulation.strftime('%Y-%m-%d'),
#         "Ovulation Window": window,
#         "Evaluation Metrics": pd.DataFrame([metrics]),
#         "Training Data": df
#     }

# # ---------- Activity Tracking ----------
# def add_activity(user, date, activity_type, details):
#     data = load_json(ACTIVITY_FILE)
#     user_acts = data.setdefault(user, {})
#     user_acts.setdefault(date, []).append({"type": activity_type, "details": details})
#     save_json(ACTIVITY_FILE, data)

# def log_activity_ui(user):
#     st.subheader("Log Activity")
#     date = st.date_input("Date", datetime.date.today())
#     kind = st.selectbox("Activity Type", ["Sex", "Water Intake", "Exercise", "Symptoms", "Other"])

#     if kind == "Sex":
#         details = f"Type: {st.selectbox('Sex Type', ['Unprotected', 'Protected'])}"
#     elif kind == "Water Intake":
#         details = f"Intake: {st.slider('Litres', 1, 30, 2)} L"
#     elif kind == "Exercise":
#         details = f"Duration: {st.slider('Minutes', 1, 300, 30)} min"
#     elif kind == "Symptoms":
#         options = ["Fatigue", "Bloating", "Headache", "Cramps", "Diarrhea", "Nausea"]
#         selected = st.multiselect("Symptoms", options)
#         details = ", ".join([f"{s} (Severity: {st.slider(f'{s} Severity', 1, 5, 3)})" for s in selected])
#     else:
#         kind = st.text_input("Custom Type") or "Other"
#         details = st.text_area("Details")

#     if st.button("Save") and details:
#         add_activity(user, str(date), kind, details)
#         st.success("Activity saved.")

# def view_activities(user):
#     st.subheader("Activity Log")
#     data = load_json(ACTIVITY_FILE)
#     user_data = data.get(user, {})
#     if not user_data:
#         st.warning("No activities found.")
#         return

#     rows = [{"Date": d, "Type": entry["type"], "Details": entry["details"]} for d, acts in user_data.items() for entry in acts]
#     st.dataframe(pd.DataFrame(rows))

# # ---------- Main App ----------
# def main():
#     st.title("Health & Cycle Tracker")
#     if "username" not in st.session_state:
#         st.session_state.username = ""

#     if not st.session_state.username:
#         username = st.text_input("Enter your username to continue")
#         if username:
#             st.session_state.username = username
#             try:
#                st.rerun()  
#             except AttributeError:
#                st.experimental_rerun()  
#         else:
#            st.stop()


#     user = st.session_state.username
#     option = st.sidebar.selectbox("Menu", ["Add Cycle", "Predict Cycle", "Log Activity", "View Activities"])

#     if option == "Add Cycle":
#         st.subheader("Add Menstrual Cycle")
#         start = st.date_input("Start Date")
#         pdur = st.number_input("Period Duration", 1, 10)
#         luteal = st.number_input("Luteal Phase", 10, 16)
#         stress = st.selectbox("Stress", ["Low", "Moderate", "High"])
#         exercise = st.selectbox("Exercise", ["None", "Light", "Moderate", "Intense"])
#         sleep = st.selectbox("Sleep", ["Poor", "Moderate", "Good"])
#         weight = st.selectbox("Weight Change", ["None", "Gained", "Lost"])
#         if st.button("Submit"):
#             add_cycle_data(user, start, pdur, luteal, stress, exercise, sleep, weight)
#             st.success("Cycle data saved.")

#     elif option == "Predict Cycle":
#         result = predict_next_cycle(user)
#         if isinstance(result, str):
#             st.warning(result)
#         else:
#             for k, v in result.items():
#                 if isinstance(v, pd.DataFrame):
#                     st.subheader(k)
#                     st.dataframe(v)
#                 else:
#                     st.markdown(f"**{k}:** {v}")

#     elif option == "Log Activity":
#         log_activity_ui(user)

#     elif option == "View Activities":
#         view_activities(user)

# if __name__ == "__main__":
#     main()
