from langchain.tools import Tool
import json
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional

def chronic_tracker_tool(data_json: str) -> str:
    """
    Chronic Tracker Tool for monitoring chronic conditions over time.
    Provides trend analysis, personalized feedback, and continuous care recommendations.
    Supports daily/weekly vitals input and continuous chronic care.

    Args:
        data_json: JSON string containing chronic condition data and tracking information

    Returns:
        JSON string with analysis, trends, and recommendations
    """
    try:
        # Parse input data
        input_data = json.loads(data_json)

        # Extract user data and chronic condition information
        user_id = input_data.get("user_id", "unknown")
        condition_data = input_data.get("condition_data", {})
        historical_data = input_data.get("historical_data", [])

        # Get condition type and tracking frequency
        condition_type = condition_data.get("condition_type", "")
        tracking_frequency = condition_data.get("tracking_frequency", "as_needed")

        # Get measurement date/time (default to current time if not provided)
        measurement_date = condition_data.get("measurement_date", datetime.now().isoformat())

        # Add tracking metadata to condition data
        condition_data["tracking_metadata"] = {
            "tracking_frequency": tracking_frequency,
            "measurement_date": measurement_date,
            "recorded_at": datetime.now().isoformat()
        }

        # Process based on condition type
        if condition_type == "diabetes":
            return analyze_diabetes_data(condition_data, historical_data)
        elif condition_type == "hypertension":
            return analyze_hypertension_data(condition_data, historical_data)
        elif condition_type == "asthma":
            return analyze_asthma_data(condition_data, historical_data)
        elif condition_type == "heart_disease":
            return analyze_heart_disease_data(condition_data, historical_data)
        elif condition_type == "kidney_disease":
            return analyze_kidney_disease_data(condition_data, historical_data)
        else:
            return json.dumps({
                "error": f"Unsupported condition type: {condition_type}",
                "supported_conditions": ["diabetes", "hypertension", "asthma", "heart_disease", "kidney_disease"]
            })

    except Exception as e:
        logging.error(f"Error in chronic tracker tool: {str(e)}")
        return json.dumps({"error": f"Error analyzing chronic condition data: {str(e)}"})

def analyze_diabetes_data(condition_data, historical_data):
    """Analyze diabetes tracking data and provide recommendations"""
    # Extract current readings
    glucose = condition_data.get("glucose", 0)
    hba1c = condition_data.get("hba1c", 0)
    medication_adherence = condition_data.get("medication_adherence", "")
    symptoms = condition_data.get("symptoms", [])

    # Analyze current state
    current_analysis = analyze_current_diabetes_state(glucose, hba1c, medication_adherence, symptoms)

    # Analyze trends if historical data exists
    trend_analysis = {}
    if historical_data:
        trend_analysis = analyze_diabetes_trends(historical_data, glucose, hba1c)

    # Generate recommendations
    recommendations = generate_diabetes_recommendations(glucose, hba1c, trend_analysis, symptoms)

    # Create doctor-like summary
    summary = create_diabetes_summary(current_analysis, trend_analysis, recommendations)

    return json.dumps({
        "current_analysis": current_analysis,
        "trend_analysis": trend_analysis,
        "recommendations": recommendations,
        "summary": summary
    }, indent=2)

def analyze_current_diabetes_state(glucose, hba1c, medication_adherence, symptoms):
    """Analyze current diabetes state"""
    analysis = {}

    # Analyze glucose
    if glucose > 0:
        if glucose < 70:
            analysis["glucose"] = {
                "status": "low",
                "description": "Your blood glucose is below the normal range, indicating hypoglycemia.",
                "risk_level": "high"
            }
        elif glucose <= 99:
            analysis["glucose"] = {
                "status": "normal",
                "description": "Your blood glucose is within the normal fasting range.",
                "risk_level": "low"
            }
        elif glucose <= 125:
            analysis["glucose"] = {
                "status": "elevated",
                "description": "Your blood glucose is elevated, indicating prediabetes.",
                "risk_level": "moderate"
            }
        else:
            analysis["glucose"] = {
                "status": "high",
                "description": "Your blood glucose is high, indicating diabetes.",
                "risk_level": "high"
            }

    # Analyze HbA1c
    if hba1c > 0:
        if hba1c < 5.7:
            analysis["hba1c"] = {
                "status": "normal",
                "description": "Your HbA1c is within the normal range.",
                "risk_level": "low"
            }
        elif hba1c <= 6.4:
            analysis["hba1c"] = {
                "status": "elevated",
                "description": "Your HbA1c is elevated, indicating prediabetes.",
                "risk_level": "moderate"
            }
        else:
            analysis["hba1c"] = {
                "status": "high",
                "description": "Your HbA1c is high, indicating diabetes.",
                "risk_level": "high"
            }

    # Analyze medication adherence
    if medication_adherence:
        if medication_adherence == "high":
            analysis["medication_adherence"] = {
                "status": "good",
                "description": "You're taking your medications as prescribed. Great job!"
            }
        elif medication_adherence == "medium":
            analysis["medication_adherence"] = {
                "status": "fair",
                "description": "You're occasionally missing doses of your medication."
            }
        else:
            analysis["medication_adherence"] = {
                "status": "poor",
                "description": "You're frequently missing doses of your medication, which can impact your diabetes control."
            }

    # Analyze symptoms
    if symptoms:
        analysis["symptoms"] = {
            "reported": symptoms,
            "concerns": []
        }

        concerning_symptoms = ["frequent urination", "excessive thirst", "unexplained weight loss",
                              "blurred vision", "slow-healing sores", "frequent infections",
                              "tingling hands/feet", "extreme fatigue"]

        for symptom in symptoms:
            if symptom.lower() in [s.lower() for s in concerning_symptoms]:
                analysis["symptoms"]["concerns"].append(symptom)

    return analysis

def analyze_diabetes_trends(historical_data, current_glucose, current_hba1c):
    """Analyze trends in diabetes data over time with enhanced support for daily/weekly tracking"""
    # Sort historical data by timestamp
    sorted_data = sorted(historical_data, key=lambda x: x.get("timestamp", ""))

    # Extract glucose and HbA1c values with timestamps
    glucose_entries = [(entry.get("condition_data", {}).get("glucose", 0),
                       entry.get("timestamp", ""),
                       entry.get("condition_data", {}).get("tracking_metadata", {}).get("tracking_frequency", "as_needed"))
                     for entry in sorted_data
                     if entry.get("condition_data", {}).get("glucose", 0) > 0]

    hba1c_entries = [(entry.get("condition_data", {}).get("hba1c", 0),
                     entry.get("timestamp", ""),
                     entry.get("condition_data", {}).get("tracking_metadata", {}).get("tracking_frequency", "as_needed"))
                   for entry in sorted_data
                   if entry.get("condition_data", {}).get("hba1c", 0) > 0]

    # Add current values if they exist
    current_timestamp = datetime.now().isoformat()
    current_frequency = "as_needed"
    try:
        current_frequency = sorted_data[-1].get("condition_data", {}).get("tracking_metadata", {}).get("tracking_frequency", "as_needed") if sorted_data else "as_needed"
    except:
        pass

    if current_glucose > 0:
        glucose_entries.append((current_glucose, current_timestamp, current_frequency))
    if current_hba1c > 0:
        hba1c_entries.append((current_hba1c, current_timestamp, current_frequency))

    # Extract just the values for basic trend analysis
    glucose_values = [entry[0] for entry in glucose_entries]
    hba1c_values = [entry[0] for entry in hba1c_entries]

    # Analyze glucose trend
    glucose_trend = {}
    if len(glucose_values) >= 2:
        glucose_change = glucose_values[-1] - glucose_values[0]
        glucose_percent_change = (glucose_change / glucose_values[0]) * 100 if glucose_values[0] > 0 else 0

        # Determine trend direction
        if glucose_percent_change <= -10:
            direction = "decreasing"
            description = "Your glucose levels have been decreasing, which is positive."
        elif glucose_percent_change >= 10:
            direction = "increasing"
            description = "Your glucose levels have been increasing, which requires attention."
        else:
            direction = "stable"
            description = "Your glucose levels have been relatively stable."

        # Create basic trend info
        glucose_trend = {
            "direction": direction,
            "description": description,
            "percent_change": round(glucose_percent_change, 1),
            "values": glucose_values,
            "timestamps": [entry[1] for entry in glucose_entries]
        }

        # Add frequency-specific analysis if we have enough data points
        if len(glucose_entries) >= 3:
            # Determine if tracking is consistent
            frequencies = [entry[2] for entry in glucose_entries]
            most_common_frequency = max(set(frequencies), key=frequencies.count)

            # Add frequency-specific insights
            if most_common_frequency == "daily":
                glucose_trend["frequency_analysis"] = analyze_daily_glucose_pattern(glucose_entries)
            elif most_common_frequency == "weekly":
                glucose_trend["frequency_analysis"] = analyze_weekly_glucose_pattern(glucose_entries)

            # Add adherence analysis
            glucose_trend["tracking_adherence"] = analyze_tracking_adherence(glucose_entries, most_common_frequency)

    # Analyze HbA1c trend
    hba1c_trend = {}
    if len(hba1c_values) >= 2:
        hba1c_change = hba1c_values[-1] - hba1c_values[0]
        hba1c_percent_change = (hba1c_change / hba1c_values[0]) * 100 if hba1c_values[0] > 0 else 0

        if hba1c_percent_change <= -5:
            hba1c_trend = {
                "direction": "decreasing",
                "description": "Your HbA1c has been decreasing, which is positive.",
                "percent_change": round(hba1c_percent_change, 1)
            }
        elif hba1c_percent_change >= 5:
            hba1c_trend = {
                "direction": "increasing",
                "description": "Your HbA1c has been increasing, which requires attention.",
                "percent_change": round(hba1c_percent_change, 1)
            }
        else:
            hba1c_trend = {
                "direction": "stable",
                "description": "Your HbA1c has been relatively stable.",
                "percent_change": round(hba1c_percent_change, 1)
            }

    # Determine tracking consistency and frequency
    tracking_frequency = "as_needed"
    if sorted_data:
        frequencies = [entry.get("condition_data", {}).get("tracking_metadata", {}).get("tracking_frequency", "as_needed")
                      for entry in sorted_data]
        if frequencies:
            tracking_frequency = max(set(frequencies), key=frequencies.count)

    # Calculate tracking period in days
    tracking_period_days = 0
    if len(sorted_data) >= 2:
        try:
            first_date = datetime.fromisoformat(sorted_data[0].get("timestamp", "").split("T")[0])
            last_date = datetime.fromisoformat(sorted_data[-1].get("timestamp", "").split("T")[0])
            tracking_period_days = (last_date - first_date).days + 1
        except:
            tracking_period_days = len(sorted_data)  # Fallback if date parsing fails

    return {
        "glucose": glucose_trend,
        "hba1c": hba1c_trend,
        "data_points": len(sorted_data) + 1,  # Include current entry
        "tracking_period": f"{len(sorted_data) + 1} readings over {tracking_period_days} days" if tracking_period_days > 0 else f"{len(sorted_data) + 1} readings",
        "tracking_frequency": tracking_frequency,
        "consistency_score": calculate_tracking_consistency(sorted_data, tracking_frequency)
    }

def analyze_daily_glucose_pattern(glucose_entries):
    """Analyze daily patterns in glucose readings"""
    # This is a simplified analysis - in a real system, this would be more sophisticated
    try:
        # Look for patterns in morning vs evening readings
        morning_readings = []
        evening_readings = []

        for glucose, timestamp, _ in glucose_entries:
            try:
                dt = datetime.fromisoformat(timestamp)
                hour = dt.hour
                if 5 <= hour < 12:  # Morning
                    morning_readings.append(glucose)
                elif 17 <= hour < 23:  # Evening
                    evening_readings.append(glucose)
            except:
                continue

        # Calculate averages if we have enough data
        morning_avg = sum(morning_readings) / len(morning_readings) if morning_readings else 0
        evening_avg = sum(evening_readings) / len(evening_readings) if evening_readings else 0

        # Generate insights
        insights = []
        if morning_avg > 0 and evening_avg > 0:
            diff = morning_avg - evening_avg
            if abs(diff) > 20:  # Significant difference
                if diff > 0:
                    insights.append(f"Your morning glucose readings (avg: {round(morning_avg)} mg/dL) tend to be higher than evening readings (avg: {round(evening_avg)} mg/dL).")
                    insights.append("This could indicate the dawn phenomenon, where glucose rises in early morning.")
                else:
                    insights.append(f"Your evening glucose readings (avg: {round(evening_avg)} mg/dL) tend to be higher than morning readings (avg: {round(morning_avg)} mg/dL).")
                    insights.append("This could be related to meal timing or medication scheduling.")
            else:
                insights.append(f"Your glucose levels are relatively consistent throughout the day (morning avg: {round(morning_avg)} mg/dL, evening avg: {round(evening_avg)} mg/dL).")

        # Check for variability
        all_readings = [g for g, _, _ in glucose_entries]
        if all_readings:
            max_val = max(all_readings)
            min_val = min(all_readings)
            variability = max_val - min_val

            if variability > 50:
                insights.append(f"Your glucose shows significant daily variability (range: {round(min_val)}-{round(max_val)} mg/dL).")
                insights.append("Consider reviewing meal timing, carbohydrate intake, and medication schedule with your healthcare provider.")
            elif variability > 30:
                insights.append(f"Your glucose shows moderate daily variability (range: {round(min_val)}-{round(max_val)} mg/dL).")
            else:
                insights.append(f"Your glucose levels are relatively stable throughout the day (range: {round(min_val)}-{round(max_val)} mg/dL).")

        return {
            "morning_average": round(morning_avg) if morning_avg > 0 else None,
            "evening_average": round(evening_avg) if evening_avg > 0 else None,
            "insights": insights
        }
    except Exception as e:
        return {"error": f"Could not analyze daily patterns: {str(e)}"}

def analyze_weekly_glucose_pattern(glucose_entries):
    """Analyze weekly patterns in glucose readings"""
    try:
        # Group readings by day of week
        day_readings = {i: [] for i in range(7)}  # 0=Monday, 6=Sunday

        for glucose, timestamp, _ in glucose_entries:
            try:
                dt = datetime.fromisoformat(timestamp)
                day_of_week = dt.weekday()
                day_readings[day_of_week].append(glucose)
            except:
                continue

        # Calculate averages for days with data
        day_averages = {}
        day_names = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]

        for day_num, readings in day_readings.items():
            if readings:
                day_averages[day_names[day_num]] = sum(readings) / len(readings)

        # Find highest and lowest days
        if day_averages:
            highest_day = max(day_averages.items(), key=lambda x: x[1])
            lowest_day = min(day_averages.items(), key=lambda x: x[1])

            insights = []
            if highest_day[1] - lowest_day[1] > 20:  # Significant difference
                insights.append(f"Your glucose tends to be highest on {highest_day[0]}s (avg: {round(highest_day[1])} mg/dL) and lowest on {lowest_day[0]}s (avg: {round(lowest_day[1])} mg/dL).")

                # Check if weekend vs weekday pattern
                weekday_readings = []
                weekend_readings = []

                for day, avg in day_averages.items():
                    if day in ["Saturday", "Sunday"]:
                        weekend_readings.append(avg)
                    else:
                        weekday_readings.append(avg)

                weekday_avg = sum(weekday_readings) / len(weekday_readings) if weekday_readings else 0
                weekend_avg = sum(weekend_readings) / len(weekend_readings) if weekend_readings else 0

                if abs(weekday_avg - weekend_avg) > 15:
                    if weekend_avg > weekday_avg:
                        insights.append(f"Your weekend glucose readings tend to be higher than weekdays. This could be related to changes in routine, diet, or activity levels.")
                    else:
                        insights.append(f"Your weekday glucose readings tend to be higher than weekends. This could be related to work stress or different eating patterns.")
            else:
                insights.append("Your glucose levels are relatively consistent throughout the week.")

            return {
                "day_averages": {day: round(avg) for day, avg in day_averages.items()},
                "highest_day": highest_day[0],
                "lowest_day": lowest_day[0],
                "insights": insights
            }
        else:
            return {"message": "Not enough data to analyze weekly patterns."}
    except Exception as e:
        return {"error": f"Could not analyze weekly patterns: {str(e)}"}

def analyze_tracking_adherence(entries, frequency):
    """Analyze how consistently the user is tracking their condition"""
    try:
        if not entries or len(entries) < 2:
            return {"adherence": "insufficient data", "score": 0}

        # Sort entries by timestamp
        sorted_entries = sorted(entries, key=lambda x: x[1])

        # Calculate expected tracking frequency in days
        expected_interval = 1  # daily
        if frequency == "weekly":
            expected_interval = 7
        elif frequency == "monthly":
            expected_interval = 30
        elif frequency == "as_needed":
            return {"adherence": "as needed", "score": 100}  # Can't measure adherence for as-needed tracking

        # Calculate actual intervals
        intervals = []
        for i in range(1, len(sorted_entries)):
            try:
                current_date = datetime.fromisoformat(sorted_entries[i][1].split("T")[0])
                previous_date = datetime.fromisoformat(sorted_entries[i-1][1].split("T")[0])
                interval_days = (current_date - previous_date).days
                intervals.append(interval_days)
            except:
                continue

        if not intervals:
            return {"adherence": "insufficient data", "score": 0}

        # Calculate adherence score
        deviations = [abs(interval - expected_interval) for interval in intervals]
        avg_deviation = sum(deviations) / len(deviations)

        # Convert to a 0-100 score (lower deviation = higher score)
        max_allowed_deviation = expected_interval * 2  # Allow up to 2x the expected interval
        adherence_score = max(0, 100 - (avg_deviation / max_allowed_deviation * 100))

        # Determine adherence level
        if adherence_score >= 90:
            adherence_level = "excellent"
            message = "You're doing an excellent job tracking your condition consistently."
        elif adherence_score >= 75:
            adherence_level = "good"
            message = "You're doing a good job tracking your condition, with only minor inconsistencies."
        elif adherence_score >= 50:
            adherence_level = "fair"
            message = f"Your tracking could be more consistent. Try to track every {expected_interval} day(s) for better insights."
        else:
            adherence_level = "poor"
            message = f"Your tracking has been inconsistent. Setting reminders might help you track every {expected_interval} day(s)."

        return {
            "adherence": adherence_level,
            "score": round(adherence_score),
            "message": message
        }
    except Exception as e:
        return {"adherence": "error", "score": 0, "error": str(e)}

def calculate_tracking_consistency(historical_data, frequency):
    """Calculate a consistency score for condition tracking"""
    try:
        if not historical_data or len(historical_data) < 2:
            return 0

        # Expected interval in days
        expected_interval = 1  # daily
        if frequency == "weekly":
            expected_interval = 7
        elif frequency == "monthly":
            expected_interval = 30
        elif frequency == "as_needed":
            return 100  # Can't measure consistency for as-needed tracking

        # Calculate actual intervals
        timestamps = []
        for entry in historical_data:
            try:
                timestamp = entry.get("timestamp", "")
                if timestamp:
                    date = datetime.fromisoformat(timestamp.split("T")[0])
                    timestamps.append(date)
            except:
                continue

        if len(timestamps) < 2:
            return 0

        # Sort timestamps
        timestamps.sort()

        # Calculate intervals
        intervals = [(timestamps[i] - timestamps[i-1]).days for i in range(1, len(timestamps))]

        # Calculate consistency score
        deviations = [abs(interval - expected_interval) for interval in intervals]
        avg_deviation = sum(deviations) / len(deviations)

        # Convert to a 0-100 score (lower deviation = higher score)
        max_allowed_deviation = expected_interval * 2
        consistency_score = max(0, 100 - (avg_deviation / max_allowed_deviation * 100))

        return round(consistency_score)
    except Exception as e:
        return 0

def generate_diabetes_recommendations(glucose, hba1c, trend_analysis, symptoms):
    """Generate personalized recommendations for diabetes management with continuous care focus"""
    recommendations = []
    tracking_recommendations = []
    continuous_care_recommendations = []

    # Glucose-based recommendations
    if glucose > 0:
        if glucose < 70:
            recommendations.append("Consume 15-20 grams of fast-acting carbohydrates to raise your blood sugar.")
            recommendations.append("Check your blood sugar again after 15 minutes.")
            recommendations.append("Contact your healthcare provider if hypoglycemia persists.")
            tracking_recommendations.append("Track your glucose more frequently over the next 24 hours.")
        elif glucose > 125:
            recommendations.append("Monitor your carbohydrate intake and follow your meal plan.")
            recommendations.append("Stay hydrated by drinking plenty of water.")
            recommendations.append("Engage in regular physical activity as recommended by your healthcare provider.")
            tracking_recommendations.append("Consider tracking your glucose before and after meals to identify patterns.")

    # HbA1c-based recommendations
    if hba1c > 0:
        if hba1c > 6.4:
            recommendations.append("Follow your medication regimen as prescribed by your healthcare provider.")
            recommendations.append("Monitor your blood glucose regularly.")
            recommendations.append("Schedule regular check-ups with your healthcare provider.")
            continuous_care_recommendations.append("Consider daily glucose monitoring to better manage your diabetes.")
        elif hba1c > 5.7:
            continuous_care_recommendations.append("Weekly glucose monitoring can help prevent progression to diabetes.")

    # Trend-based recommendations
    if trend_analysis:
        # Tracking frequency recommendations
        tracking_frequency = trend_analysis.get("tracking_frequency", "as_needed")
        consistency_score = trend_analysis.get("consistency_score", 0)

        # Add tracking adherence recommendations
        if "glucose" in trend_analysis and "tracking_adherence" in trend_analysis["glucose"]:
            adherence = trend_analysis["glucose"]["tracking_adherence"]
            if adherence.get("adherence") == "poor":
                tracking_recommendations.append(adherence.get("message", "Try to be more consistent with your tracking."))
                tracking_recommendations.append("Set reminders on your phone to help you track regularly.")
            elif adherence.get("adherence") == "fair":
                tracking_recommendations.append(adherence.get("message", "Your tracking could be more consistent."))

        # Add frequency-specific recommendations
        if tracking_frequency == "daily":
            if consistency_score < 70:
                tracking_recommendations.append("Daily tracking is most effective when done consistently. Try setting a reminder at the same time each day.")

            # Add daily pattern recommendations if available
            if "glucose" in trend_analysis and "frequency_analysis" in trend_analysis["glucose"]:
                analysis = trend_analysis["glucose"]["frequency_analysis"]
                if "insights" in analysis and analysis["insights"]:
                    for insight in analysis["insights"]:
                        if "higher" in insight and "morning" in insight:
                            continuous_care_recommendations.append("Consider adjusting your evening medication or having a small protein snack before bed to address morning highs.")
                        elif "higher" in insight and "evening" in insight:
                            continuous_care_recommendations.append("Review your meal timing and composition, especially for lunch and dinner.")

        elif tracking_frequency == "weekly":
            if consistency_score < 70:
                tracking_recommendations.append("Pick a specific day each week for tracking to establish a routine.")

            # Add weekly pattern recommendations if available
            if "glucose" in trend_analysis and "frequency_analysis" in trend_analysis["glucose"]:
                analysis = trend_analysis["glucose"]["frequency_analysis"]
                if "insights" in analysis and analysis["insights"]:
                    for insight in analysis["insights"]:
                        if "weekend" in insight and "higher" in insight:
                            continuous_care_recommendations.append("Try to maintain your weekday routine on weekends, especially regarding meal times and physical activity.")

        # Add trend-based recommendations
        if "glucose" in trend_analysis:
            if trend_analysis["glucose"].get("direction") == "increasing":
                recommendations.append("Review your diet and consider reducing carbohydrate intake.")
                recommendations.append("Increase physical activity if approved by your healthcare provider.")
                continuous_care_recommendations.append("Consider increasing your tracking frequency to identify what's causing the upward trend.")
            elif trend_analysis["glucose"].get("direction") == "decreasing":
                continuous_care_recommendations.append("Continue with your current management approach as it appears to be working well.")
            elif trend_analysis["glucose"].get("direction") == "stable" and glucose > 125:
                continuous_care_recommendations.append("While your glucose is stable, it remains elevated. Consider discussing medication adjustments with your provider.")

    # Symptom-based recommendations
    if symptoms:
        concerning_symptoms = ["frequent urination", "excessive thirst", "unexplained weight loss",
                              "blurred vision", "slow-healing sores", "frequent infections",
                              "tingling hands/feet", "extreme fatigue"]

        has_concerning_symptoms = any(symptom.lower() in [s.lower() for s in concerning_symptoms] for symptom in symptoms)

        if has_concerning_symptoms:
            recommendations.append("Schedule an appointment with your healthcare provider to discuss your symptoms.")
            tracking_recommendations.append("Track when these symptoms occur and their severity to share with your healthcare provider.")

    # General recommendations
    recommendations.append("Maintain a balanced diet rich in vegetables, lean proteins, and whole grains.")
    recommendations.append("Stay physically active with at least 150 minutes of moderate exercise per week.")
    recommendations.append("Monitor your blood glucose regularly and keep a log of your readings.")
    recommendations.append("Take medications as prescribed and don't skip doses.")

    # Add tracking recommendations
    if tracking_recommendations:
        recommendations.append("\nFor better tracking:")
        recommendations.extend(tracking_recommendations)

    # Add continuous care recommendations
    if continuous_care_recommendations:
        recommendations.append("\nFor continuous care:")
        recommendations.extend(continuous_care_recommendations)

    return recommendations

def create_diabetes_summary(current_analysis, trend_analysis, recommendations):
    """Create a doctor-like summary of diabetes tracking analysis with continuous care focus"""
    summary = "Based on your diabetes tracking data, here's what I'm seeing:\n\n"

    # Add current state summary
    if "glucose" in current_analysis:
        summary += f"Your blood glucose is {current_analysis['glucose']['status']}. {current_analysis['glucose']['description']}\n\n"

    if "hba1c" in current_analysis:
        summary += f"Your HbA1c is {current_analysis['hba1c']['status']}. {current_analysis['hba1c']['description']}\n\n"

    # Add trend summary
    if trend_analysis:
        # Add tracking frequency and consistency information
        tracking_frequency = trend_analysis.get("tracking_frequency", "as_needed")
        consistency_score = trend_analysis.get("consistency_score", 0)
        tracking_period = trend_analysis.get("tracking_period", "")

        if tracking_period:
            summary += f"You've been tracking your diabetes for {tracking_period}. "

        if tracking_frequency != "as_needed":
            if consistency_score >= 90:
                summary += f"You're tracking {tracking_frequency} with excellent consistency (score: {consistency_score}/100).\n\n"
            elif consistency_score >= 75:
                summary += f"You're tracking {tracking_frequency} with good consistency (score: {consistency_score}/100).\n\n"
            elif consistency_score >= 50:
                summary += f"You're tracking {tracking_frequency}, but with only fair consistency (score: {consistency_score}/100).\n\n"
            else:
                summary += f"Your {tracking_frequency} tracking has been inconsistent (score: {consistency_score}/100). More regular tracking will provide better insights.\n\n"

        # Add glucose trend information
        if "glucose" in trend_analysis and trend_analysis["glucose"]:
            summary += f"Over time, your glucose levels have been {trend_analysis['glucose']['direction']}. {trend_analysis['glucose']['description']}\n\n"

            # Add frequency-specific insights if available
            if "frequency_analysis" in trend_analysis["glucose"] and trend_analysis["glucose"]["frequency_analysis"]:
                analysis = trend_analysis["glucose"]["frequency_analysis"]

                # Add daily pattern insights
                if "insights" in analysis and analysis["insights"]:
                    summary += "Pattern analysis: "
                    summary += " ".join(analysis["insights"][:2]) + "\n\n"  # Limit to first 2 insights

            # Add tracking adherence information if available
            if "tracking_adherence" in trend_analysis["glucose"]:
                adherence = trend_analysis["glucose"]["tracking_adherence"]
                if "message" in adherence and adherence["adherence"] != "as needed" and adherence["adherence"] != "excellent":
                    summary += f"Tracking feedback: {adherence['message']}\n\n"

        # Add HbA1c trend information
        if "hba1c" in trend_analysis and trend_analysis["hba1c"]:
            summary += f"Your HbA1c has been {trend_analysis['hba1c']['direction']}. {trend_analysis['hba1c']['description']}\n\n"

    # Add symptom summary
    if "symptoms" in current_analysis and current_analysis["symptoms"]["concerns"]:
        summary += "I notice you're experiencing some concerning symptoms that we should address: "
        summary += ", ".join(current_analysis["symptoms"]["concerns"]) + ".\n\n"

    # Add recommendation summary
    summary += "Here are my recommendations:\n"

    # Filter out the section headers (they start with newline)
    main_recommendations = [rec for rec in recommendations if not rec.startswith("\n")]

    # Get tracking and continuous care sections
    tracking_section_index = -1
    continuous_care_section_index = -1

    for i, rec in enumerate(recommendations):
        if rec == "\nFor better tracking:":
            tracking_section_index = i
        elif rec == "\nFor continuous care:":
            continuous_care_section_index = i

    # Add main recommendations (limited to top 5)
    end_index = min(5, tracking_section_index if tracking_section_index > 0 else len(main_recommendations))
    for i, recommendation in enumerate(main_recommendations[:end_index], 1):
        summary += f"{i}. {recommendation}\n"

    # Add tracking recommendations section if it exists
    if tracking_section_index > 0:
        summary += "\nFor better tracking:\n"
        start_idx = tracking_section_index + 1
        end_idx = continuous_care_section_index if continuous_care_section_index > 0 else len(recommendations)
        for i, recommendation in enumerate(recommendations[start_idx:end_idx], 1):
            if not recommendation.startswith("\n"):  # Skip any nested section headers
                summary += f"• {recommendation}\n"

    # Add continuous care recommendations section if it exists
    if continuous_care_section_index > 0:
        summary += "\nFor continuous care:\n"
        for i, recommendation in enumerate(recommendations[continuous_care_section_index+1:], 1):
            if not recommendation.startswith("\n"):  # Skip any nested section headers
                summary += f"• {recommendation}\n"

    return summary

def analyze_hypertension_data(condition_data, historical_data):
    """Analyze hypertension tracking data and provide recommendations"""
    # Extract current readings
    systolic = condition_data.get("systolic", 0)
    diastolic = condition_data.get("diastolic", 0)
    heart_rate = condition_data.get("heart_rate", 0)
    medication_adherence = condition_data.get("medication_adherence", "")
    symptoms = condition_data.get("symptoms", [])

    # Analyze current state
    current_analysis = analyze_current_hypertension_state(systolic, diastolic, heart_rate, medication_adherence, symptoms)

    # Analyze trends if historical data exists
    trend_analysis = {}
    if historical_data:
        trend_analysis = analyze_hypertension_trends(historical_data, systolic, diastolic)

    # Generate recommendations
    recommendations = generate_hypertension_recommendations(systolic, diastolic, trend_analysis, symptoms)

    # Create doctor-like summary
    summary = create_hypertension_summary(current_analysis, trend_analysis, recommendations)

    return json.dumps({
        "current_analysis": current_analysis,
        "trend_analysis": trend_analysis,
        "recommendations": recommendations,
        "summary": summary
    }, indent=2)

def analyze_current_hypertension_state(systolic, diastolic, heart_rate, medication_adherence, symptoms):
    """Analyze current hypertension state"""
    analysis = {}

    # Analyze systolic blood pressure
    if systolic > 0:
        if systolic < 120:
            analysis["systolic"] = {
                "status": "normal",
                "description": "Your systolic blood pressure is within the normal range.",
                "risk_level": "low",
                "value": systolic
            }
        elif systolic <= 129:
            analysis["systolic"] = {
                "status": "elevated",
                "description": "Your systolic blood pressure is elevated.",
                "risk_level": "low to moderate",
                "value": systolic
            }
        elif systolic <= 139:
            analysis["systolic"] = {
                "status": "stage 1 hypertension",
                "description": "Your systolic blood pressure indicates stage 1 hypertension.",
                "risk_level": "moderate",
                "value": systolic
            }
        elif systolic <= 180:
            analysis["systolic"] = {
                "status": "stage 2 hypertension",
                "description": "Your systolic blood pressure indicates stage 2 hypertension.",
                "risk_level": "high",
                "value": systolic
            }
        else:
            analysis["systolic"] = {
                "status": "hypertensive crisis",
                "description": "Your systolic blood pressure is dangerously high, indicating a hypertensive crisis.",
                "risk_level": "very high",
                "value": systolic
            }

    # Analyze diastolic blood pressure
    if diastolic > 0:
        if diastolic < 80:
            analysis["diastolic"] = {
                "status": "normal",
                "description": "Your diastolic blood pressure is within the normal range.",
                "risk_level": "low",
                "value": diastolic
            }
        elif diastolic <= 89:
            analysis["diastolic"] = {
                "status": "stage 1 hypertension",
                "description": "Your diastolic blood pressure indicates stage 1 hypertension.",
                "risk_level": "moderate",
                "value": diastolic
            }
        elif diastolic <= 120:
            analysis["diastolic"] = {
                "status": "stage 2 hypertension",
                "description": "Your diastolic blood pressure indicates stage 2 hypertension.",
                "risk_level": "high",
                "value": diastolic
            }
        else:
            analysis["diastolic"] = {
                "status": "hypertensive crisis",
                "description": "Your diastolic blood pressure is dangerously high, indicating a hypertensive crisis.",
                "risk_level": "very high",
                "value": diastolic
            }

    # Analyze heart rate
    if heart_rate > 0:
        if heart_rate < 60:
            analysis["heart_rate"] = {
                "status": "low",
                "description": "Your heart rate is below the normal range (bradycardia).",
                "risk_level": "moderate",
                "value": heart_rate
            }
        elif heart_rate <= 100:
            analysis["heart_rate"] = {
                "status": "normal",
                "description": "Your heart rate is within the normal range.",
                "risk_level": "low",
                "value": heart_rate
            }
        else:
            analysis["heart_rate"] = {
                "status": "high",
                "description": "Your heart rate is above the normal range (tachycardia).",
                "risk_level": "moderate",
                "value": heart_rate
            }

    # Analyze medication adherence
    if medication_adherence:
        if medication_adherence == "high":
            analysis["medication_adherence"] = {
                "status": "good",
                "description": "You're taking your medications as prescribed. Great job!"
            }
        elif medication_adherence == "medium":
            analysis["medication_adherence"] = {
                "status": "fair",
                "description": "You're occasionally missing doses of your medication."
            }
        else:
            analysis["medication_adherence"] = {
                "status": "poor",
                "description": "You're frequently missing doses of your medication, which can impact your blood pressure control."
            }

    # Analyze symptoms
    if symptoms:
        analysis["symptoms"] = {
            "reported": symptoms,
            "concerns": []
        }

        concerning_symptoms = ["headache", "dizziness", "blurred vision", "chest pain",
                              "shortness of breath", "nosebleeds", "fatigue", "confusion",
                              "irregular heartbeat", "blood in urine"]

        for symptom in symptoms:
            if symptom.lower() in [s.lower() for s in concerning_symptoms]:
                analysis["symptoms"]["concerns"].append(symptom)

    return analysis

def analyze_hypertension_trends(historical_data, current_systolic, current_diastolic):
    """Analyze trends in hypertension data over time"""
    # Sort historical data by timestamp
    sorted_data = sorted(historical_data, key=lambda x: x.get("timestamp", ""))

    # Extract systolic and diastolic values
    systolic_values = [entry.get("condition_data", {}).get("systolic", 0) for entry in sorted_data
                     if entry.get("condition_data", {}).get("systolic", 0) > 0]
    diastolic_values = [entry.get("condition_data", {}).get("diastolic", 0) for entry in sorted_data
                       if entry.get("condition_data", {}).get("diastolic", 0) > 0]

    # Add current values if they exist
    if current_systolic > 0:
        systolic_values.append(current_systolic)
    if current_diastolic > 0:
        diastolic_values.append(current_diastolic)

    # Analyze systolic trend
    systolic_trend = {}
    if len(systolic_values) >= 2:
        systolic_change = systolic_values[-1] - systolic_values[0]
        systolic_percent_change = (systolic_change / systolic_values[0]) * 100 if systolic_values[0] > 0 else 0

        if systolic_percent_change <= -5:
            systolic_trend = {
                "direction": "decreasing",
                "description": "Your systolic blood pressure has been decreasing, which is positive.",
                "percent_change": round(systolic_percent_change, 1)
            }
        elif systolic_percent_change >= 5:
            systolic_trend = {
                "direction": "increasing",
                "description": "Your systolic blood pressure has been increasing, which requires attention.",
                "percent_change": round(systolic_percent_change, 1)
            }
        else:
            systolic_trend = {
                "direction": "stable",
                "description": "Your systolic blood pressure has been relatively stable.",
                "percent_change": round(systolic_percent_change, 1)
            }

    # Analyze diastolic trend
    diastolic_trend = {}
    if len(diastolic_values) >= 2:
        diastolic_change = diastolic_values[-1] - diastolic_values[0]
        diastolic_percent_change = (diastolic_change / diastolic_values[0]) * 100 if diastolic_values[0] > 0 else 0

        if diastolic_percent_change <= -5:
            diastolic_trend = {
                "direction": "decreasing",
                "description": "Your diastolic blood pressure has been decreasing, which is positive.",
                "percent_change": round(diastolic_percent_change, 1)
            }
        elif diastolic_percent_change >= 5:
            diastolic_trend = {
                "direction": "increasing",
                "description": "Your diastolic blood pressure has been increasing, which requires attention.",
                "percent_change": round(diastolic_percent_change, 1)
            }
        else:
            diastolic_trend = {
                "direction": "stable",
                "description": "Your diastolic blood pressure has been relatively stable.",
                "percent_change": round(diastolic_percent_change, 1)
            }

    return {
        "systolic": systolic_trend,
        "diastolic": diastolic_trend,
        "data_points": len(sorted_data) + 1,  # Include current entry
        "tracking_period": f"{len(sorted_data) + 1} readings"
    }

def generate_hypertension_recommendations(systolic, diastolic, trend_analysis, symptoms):
    """Generate personalized recommendations for hypertension management"""
    recommendations = []

    # Blood pressure-based recommendations
    if systolic > 0 and diastolic > 0:
        # Hypertensive crisis
        if systolic > 180 or diastolic > 120:
            recommendations.append("Seek immediate medical attention for severely high blood pressure.")
            recommendations.append("Contact your healthcare provider or go to the emergency room.")
        # Stage 2 hypertension
        elif (systolic >= 140 or diastolic >= 90):
            recommendations.append("Follow your medication regimen exactly as prescribed by your healthcare provider.")
            recommendations.append("Monitor your blood pressure daily and keep a log of your readings.")
            recommendations.append("Schedule a follow-up appointment with your healthcare provider.")
        # Stage 1 hypertension or elevated
        elif (systolic >= 120 or diastolic >= 80):
            recommendations.append("Reduce sodium intake to less than 1,500 mg per day.")
            recommendations.append("Engage in regular aerobic exercise for at least 30 minutes most days of the week.")
            recommendations.append("Monitor your blood pressure regularly and keep a log of your readings.")

    # Trend-based recommendations
    if trend_analysis:
        if "systolic" in trend_analysis and trend_analysis["systolic"].get("direction") == "increasing":
            recommendations.append("Review your diet and consider adopting the DASH diet (rich in fruits, vegetables, and low-fat dairy).")
            recommendations.append("Reduce stress through meditation, deep breathing exercises, or yoga.")

        if "diastolic" in trend_analysis and trend_analysis["diastolic"].get("direction") == "increasing":
            recommendations.append("Limit alcohol consumption to moderate levels (one drink per day for women, two for men).")
            recommendations.append("Ensure you're getting 7-8 hours of quality sleep each night.")

    # Symptom-based recommendations
    if symptoms:
        concerning_symptoms = ["headache", "dizziness", "blurred vision", "chest pain",
                              "shortness of breath", "nosebleeds", "fatigue", "confusion",
                              "irregular heartbeat", "blood in urine"]

        has_concerning_symptoms = any(symptom.lower() in [s.lower() for s in concerning_symptoms] for symptom in symptoms)

        if has_concerning_symptoms:
            recommendations.append("Schedule an appointment with your healthcare provider to discuss your symptoms.")

    # General recommendations
    recommendations.append("Maintain a heart-healthy diet rich in fruits, vegetables, whole grains, and lean proteins.")
    recommendations.append("Limit sodium intake to less than 2,300 mg per day (about 1 teaspoon of salt).")
    recommendations.append("Engage in regular physical activity for at least 150 minutes per week.")
    recommendations.append("Maintain a healthy weight or work toward weight loss if overweight.")
    recommendations.append("Limit alcohol consumption and avoid tobacco products.")
    recommendations.append("Practice stress-reduction techniques such as meditation, deep breathing, or yoga.")

    return recommendations

def create_hypertension_summary(current_analysis, trend_analysis, recommendations):
    """Create a doctor-like summary of hypertension tracking analysis"""
    summary = "Based on your hypertension tracking data, here's what I'm seeing:\n\n"

    # Add current state summary
    if "systolic" in current_analysis and "diastolic" in current_analysis:
        summary += f"Your blood pressure is {current_analysis['systolic']['status']} at {current_analysis['systolic']['value']}/{current_analysis['diastolic']['value']} mmHg. "
        summary += f"{current_analysis['systolic']['description']} {current_analysis['diastolic']['description']}\n\n"
    elif "systolic" in current_analysis:
        summary += f"Your systolic blood pressure is {current_analysis['systolic']['status']} at {current_analysis['systolic']['value']} mmHg. "
        summary += f"{current_analysis['systolic']['description']}\n\n"
    elif "diastolic" in current_analysis:
        summary += f"Your diastolic blood pressure is {current_analysis['diastolic']['status']} at {current_analysis['diastolic']['value']} mmHg. "
        summary += f"{current_analysis['diastolic']['description']}\n\n"

    if "heart_rate" in current_analysis:
        summary += f"Your heart rate is {current_analysis['heart_rate']['status']} at {current_analysis['heart_rate']['value']} bpm. "
        summary += f"{current_analysis['heart_rate']['description']}\n\n"

    # Add trend summary
    if trend_analysis and "systolic" in trend_analysis and trend_analysis["systolic"]:
        summary += f"Over time, your systolic blood pressure has been {trend_analysis['systolic']['direction']}. "
        summary += f"{trend_analysis['systolic']['description']}\n\n"

    if trend_analysis and "diastolic" in trend_analysis and trend_analysis["diastolic"]:
        summary += f"Your diastolic blood pressure has been {trend_analysis['diastolic']['direction']}. "
        summary += f"{trend_analysis['diastolic']['description']}\n\n"

    # Add symptom summary
    if "symptoms" in current_analysis and current_analysis["symptoms"]["concerns"]:
        summary += "I notice you're experiencing some concerning symptoms that we should address: "
        summary += ", ".join(current_analysis["symptoms"]["concerns"]) + ".\n\n"

    # Add recommendation summary
    summary += "Here are my recommendations:\n"
    for i, recommendation in enumerate(recommendations[:5], 1):  # Limit to top 5 recommendations
        summary += f"{i}. {recommendation}\n"

    return summary

def analyze_asthma_data(condition_data, historical_data):
    """Analyze asthma tracking data and provide recommendations"""
    # This is a placeholder for asthma analysis
    # Similar structure to diabetes analysis would be implemented
    return json.dumps({
        "message": "Asthma tracking analysis will be implemented in a future update."
    })

def analyze_heart_disease_data(condition_data, historical_data):
    """Analyze heart disease tracking data and provide recommendations"""
    # This is a placeholder for heart disease analysis
    # Similar structure to diabetes analysis would be implemented
    return json.dumps({
        "message": "Heart disease tracking analysis will be implemented in a future update."
    })

def analyze_kidney_disease_data(condition_data, historical_data):
    """Analyze kidney disease tracking data and provide recommendations"""
    # This is a placeholder for kidney disease analysis
    # Similar structure to diabetes analysis would be implemented
    return json.dumps({
        "message": "Kidney disease tracking analysis will be implemented in a future update."
    })

# Create a Tool instance for use with LangChain
chronic_tracker_tool = Tool(
    name="ChronicTracker",
    func=chronic_tracker_tool,
    description="Tracks chronic conditions over time, analyzes trends, and provides personalized feedback and continuous care recommendations."
)
