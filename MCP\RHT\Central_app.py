import json
import os
import subprocess

# File to store user data
USER_DB = "users.json"

# Load existing users
def load_users():
    if not os.path.exists(USER_DB):
        with open(USER_DB, "w") as f:
            json.dump({}, f)
    with open(USER_DB, "r") as f:
        return json.load(f)

# Save users to file
def save_users(users):
    with open(USER_DB, "w") as f:
        json.dump(users, f, indent=4)

# Register a new user
def register_user(username, password):
    users = load_users()
    if username in users:
        return {"success": False, "message": "Username already exists."}
    users[username] = {"password": password, "recommended_app": None}
    save_users(users)
    return {"success": True, "message": "Registration successful."}

# Authenticate user
def login_user(username, password):
    users = load_users()
    if username in users and users[username]["password"] == password:
        return {"success": True, "recommended_app": users[username]["recommended_app"]}
    return {"success": False, "message": "Invalid credentials."}

# Recommend app based on questionnaire answers
def recommend_app(username, q1, q2, q3, q4, q5):
    users = load_users()
    if username not in users:
        return {"success": False, "message": "User not found."}

    # Determine app recommendation
    if q5 == "Yes":
        app = "Postpartum Tracking App"
    elif q1 == "Yes" or q4 == "Yes":
        app = "Pregnancy Tracking & Diagnosis App"
    elif q2 == "Yes" or q3 == "Yes":
        app = "Menstrual Cycle Tracking App"
    else:
        app = "Menstrual Cycle Tracking App"

    users[username]["recommended_app"] = app
    save_users(users)
    return {"success": True, "recommended_app": app}

# Launch an external app based on choice
def launch_app(app_name):
    script_map = {
        "Menstrual Cycle Tracking App": "Reptoductive_health_tracker.py",
        "Pregnancy Tracking & Diagnosis App": "pregnancy_monitoring_tracker.py",
        "Postpartum Tracking App": "postpartum_tracker.py"
    }
    script = script_map.get(app_name)
    if script:
        subprocess.Popen(["streamlit", "run", script])
        return {"success": True, "message": f"Launched {app_name}"}
    return {"success": False, "message": "Invalid app name"}





# ******* STREAMLIT VERSION COMMENTED OUT *********


# import streamlit as st
# import json
# import os
# import subprocess

# # File to store user data
# USER_DB = "users.json"

# # Load existing users
# if not os.path.exists(USER_DB):
#     with open(USER_DB, "w") as f:
#         json.dump({}, f)

# def load_users():
#     with open(USER_DB, "r") as f:
#         return json.load(f)

# def save_users(users):
#     with open(USER_DB, "w") as f:
#         json.dump(users, f, indent=4)

# # Initialize session state
# if "authenticated" not in st.session_state:
#     st.session_state.authenticated = False
#     st.session_state.username = ""
#     st.session_state.recommended_app = None

# # Registration function
# def register():
#     with st.form("register_form"):
#         st.subheader("Register")
#         username = st.text_input("Choose a username")
#         password = st.text_input("Choose a password", type="password")
#         submit = st.form_submit_button("Register")
        
#         if submit:
#             users = load_users()
#             if username in users:
#                 st.error("Username already exists. Try another.")
#             else:
#                 users[username] = {"password": password, "recommended_app": None}
#                 save_users(users)
#                 st.success("Registration successful! Please log in.")
#                 st.rerun()

# # Login function
# def login():
#     with st.form("login_form"):
#         st.subheader("Login")
#         username = st.text_input("Username")
#         password = st.text_input("Password", type="password")
#         submit = st.form_submit_button("Login")
        
#         if submit:
#             users = load_users()
#             if username in users and users[username]["password"] == password:
#                 st.session_state.authenticated = True
#                 st.session_state.username = username
#                 st.session_state.recommended_app = users[username]["recommended_app"]
#                 st.rerun()
#             else:
#                 st.error("Invalid credentials. Try again.")

# # Main app logic
# if not st.session_state.authenticated:
#     st.title("Welcome!")
#     option = st.radio("Select an option", ["Login", "Register"])
#     if option == "Login":
#         login()
#     else:
#         register()
# else:
#     st.title(f"Welcome, {st.session_state.username} 👋")
#     users = load_users()
    
#     if users[st.session_state.username]["recommended_app"] is None:
#         with st.form("questionnaire"):
#             st.subheader("Answer a few questions to find the best app for you")
#             q1 = st.radio("Are you currently pregnant?", ["Yes", "No"])
#             q2 = st.radio("Are you trying to conceive?", ["Yes", "No"])
#             q3 = st.radio("Do you want to track your menstrual cycle?", ["Yes", "No"])
#             q4 = st.radio("Are you looking for pregnancy-related health tips and diagnosis?", ["Yes", "No"])
#             q5 = st.radio("Are you a breastfeeding mother?", ["Yes", "No"])  # New question
#             submit = st.form_submit_button("Submit")

#             if submit:
#                 if q5 == "Yes":
#                     st.session_state.recommended_app = "Postpartum Tracking App"
#                 elif q1 == "Yes" or q4 == "Yes":
#                     st.session_state.recommended_app = "Pregnancy Tracking & Diagnosis App"
#                 elif q3 == "Yes" or q2 == "Yes":
#                     st.session_state.recommended_app = "Menstrual Cycle Tracking App"
#                 else:
#                     st.session_state.recommended_app = "Menstrual Cycle Tracking App"
                
#                 users[st.session_state.username]["recommended_app"] = st.session_state.recommended_app
#                 save_users(users)
#                 st.rerun()
    
#     # Show app selection
#     st.success(f"**Recommended App:** {st.session_state.recommended_app}")
#     st.subheader("Choose an app to proceed:")
#     col1, col2, col3 = st.columns(3)

#     with col1:
#         if st.button("Menstrual Cycle Tracking App"):
#             st.write("🚀 Launching Menstrual Cycle Tracking App...")
#             subprocess.Popen(["streamlit", "run", "Reptoductive_health_tracker.py"])
    
#     with col2:
#         if st.button("Pregnancy Tracking & Diagnosis App"):
#             st.write("🚀 Launching Pregnancy Tracking & Diagnosis App...")
#             subprocess.Popen(["streamlit", "run", "pregnancy_monitoring_tracker.py"])
    
#     with col3:
#         if st.button("Postpartum Tracking App"):
#             st.write("🚀 Launching Postpartum Tracking App...")
#             subprocess.Popen(["streamlit", "run", "postpartum_tracker.py"])
