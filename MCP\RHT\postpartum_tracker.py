import pandas as pd
from datetime import date, timedelta, datetime
import json
import os

# Helper Functions
def detect_anomalies(entry):
    anomalies = []
    mother = entry['mother']
    baby = entry['baby']

    if mother['mood'] in ['sad', 'anxious'] and mother['sleep_hours'] < 4:
        anomalies.append("Mother is low on mood with less than 4 hours of sleep...")

    if mother['pain_level'] >= 6 and entry['days_since_delivery'] > 14:
        anomalies.append("High pain level after 2 weeks postpartum")

    if baby['feeding_frequency'] < 6:
        anomalies.append("Baby feeding frequency is below recommended level")

    if not baby['urinates']:
        anomalies.append("Baby has not passed urine today")

    if 'redness' in mother['wound_notes'].lower():
        anomalies.append("Wound note indicates redness (possible infection)")

    if mother['mood_log'].count("sad") + mother['mood_log'].count("anxious") >= 3:
        anomalies.append("Consistent low mood signs - possible postpartum depression")

    return anomalies

def get_post_op_treatment(delivery_type):
    if delivery_type == "Vaginal":
        return [
            "Keep the perineal area clean and dry.",
            "Use warm sitz baths to relieve discomfort.",
            "Use pain relievers or ice packs as needed.",
            "Monitor for unusual vaginal discharge, odor, or pain.",
            "Kegel exercises may help improve pelvic floor strength."
        ]
    elif delivery_type == "Cesarean":
        return [
            "Keep the incision clean and dry.",
            "Avoid lifting anything heavier than the baby.",
            "Take pain medication as prescribed.",
            "Watch for fever, redness, or discharge around the incision.",
            "Rest and avoid strenuous activity until cleared by a doctor."
        ]
    return []

def get_baby_vaccination_due(weeks_since_delivery):
    schedule = {
        6: "First DTP, IPV, Hib, and HepB vaccine due",
        10: "Second DTP, IPV, Hib, and HepB vaccine due",
        14: "Third DTP, IPV, Hib, and HepB vaccine due",
        24: "First MMR, Varicella vaccine due",
        36: "Second MMR, Varicella vaccine due"
    }
    return schedule.get(weeks_since_delivery, None)

def breastfeeding_nutrition_recommendations(weeks_since_delivery):
    if weeks_since_delivery < 6:
        return "Exclusive breastfeeding is highly recommended for the first 6 months."
    elif weeks_since_delivery <= 12:
        return "Continue breastfeeding on demand. Introduce vitamin D supplements if needed."
    elif weeks_since_delivery <= 24:
        return "Introduce solid foods at 6 months. Continue breastfeeding as primary nutrition."
    else:
        return "Ensure baby gets balanced diet with breast milk and solids."

def baby_development_recommendations(weeks_since_delivery):
    if weeks_since_delivery < 4:
        return "Expect erratic sleep patterns. Baby adjusting to life outside womb."
    elif weeks_since_delivery <= 8:
        return "Baby more alert, smiling. Start tummy time."
    elif weeks_since_delivery <= 16:
        return "May roll over or babble. Use stimulating toys."
    elif weeks_since_delivery <= 24:
        return "Reaching for objects, explore textures. Encourage motor skills."
    else:
        return "May start crawling or standing. Encourage safe exploration."
    
def track_postpartum_cycle(breastfeeding_duration):
    delay = breastfeeding_duration * 0.5
    return f"Possible ovulation delay: {delay:.1f} months"


# ------------------- Main Program -------------------

def get_input(prompt, cast_func=str):
    while True:
        try:
            return cast_func(input(prompt))
        except ValueError:
            print("Invalid input. Please try again.")

def main():
    print("Welcome to the Postpartum Recovery Tracker")

    delivery_date_str = input("Enter date of delivery (YYYY-MM-DD): ")
    delivery_date = datetime.strptime(delivery_date_str, "%Y-%m-%d").date()
    today = date.today()
    days_since_delivery = (today - delivery_date).days
    weeks_since_delivery = days_since_delivery // 7

    type_of_delivery = input("Delivery Type (Vaginal/Cesarean): ")
    feeding_style = input("Feeding Style (Exclusive Breastfeeding/Mixed/Formula Only/None): ")

    breastfeeding_duration = get_input("Breastfeeding Duration (in months): ", float)
    ovulation_info = track_postpartum_cycle(breastfeeding_duration)
    print(f"\n{ovulation_info}")


    # Mother Diary
    print("\n--- Mother Diary ---")
    sleep_hours = get_input("Sleep Hours (last 24h): ", int)
    mood = input("Mood Today (happy/neutral/sad/anxious): ")
    pain_level = get_input("Pain Level (0-10): ", int)
    stress_level = input("Stress Level (Low/Moderate/High): ")
    emotional_state = input("Emotional State (Stable/Overwhelmed/Irritable/Tearful): ")
    wound_notes = input("Wound Condition Notes: ")

    wound_data = {'wound_notes': wound_notes}
    if type_of_delivery.lower() == "vaginal":
        wound_data['perineal_healing'] = input("Perineal swelling or discomfort? (No/Mild/Severe): ")
        wound_data['discharge'] = input("Unusual vaginal discharge? (No/Yes): ")
        wound_data['stinging_urine'] = input("Painful urination? (No/Yes): ")
    else:
        wound_data['incision_appearance'] = input("Incision issues? (No/Mild/Severe): ")
        wound_data['fever_present'] = input("Fever present? (No/Yes): ")

    post_op_med = input("Have you taken post-op medication today? (Yes/No): ")
    wound_data['post_op_medication'] = post_op_med

    body_changes = input("Observed body changes (comma-separated): ").split(",")

    # Baby Diary
    print("\n--- Baby Diary ---")
    baby_sleep = get_input("Baby Sleep Hours: ", int)
    feeding_log = input("Last Feeding: ")
    feeding_frequency = get_input("Feeding Frequency (last 24h): ", int)
    stool_color = input("Stool Color (Yellow/Brown/Green/Black): ")
    urinates = input("Did baby pass urine today? (Yes/No): ").strip().lower() == 'yes'
    expression_notes = input("Baby's Expressions/Behaviors: ")

    breastfeeding_notes = input("Breastfeeding Notes: ")
    baby_development_notes = input("Baby Development Notes: ")

    breastfeeding_advice = breastfeeding_nutrition_recommendations(weeks_since_delivery)
    baby_development_advice = baby_development_recommendations(weeks_since_delivery)
    next_baby_vaccination = get_baby_vaccination_due(weeks_since_delivery)

    print(f"\nBreastfeeding Advice: {breastfeeding_advice}")
    print(f"Development Advice: {baby_development_advice}")
    if next_baby_vaccination:
        print(f"Vaccination Reminder: {next_baby_vaccination}")

    entry = {
        'date': str(today),
        'days_since_delivery': days_since_delivery,
        'type_of_delivery': type_of_delivery,
        'mother': {
            'sleep_hours': sleep_hours,
            'mood': mood,
            'mood_log': [mood],
            'stress_level': stress_level,
            'emotional_state': emotional_state,
            'pain_level': pain_level,
            'feeding_style': feeding_style,
            'wound_notes': wound_notes,
            'wound_data': wound_data,
            'body_changes': body_changes,
        },
        'baby': {
            'sleep_hours': baby_sleep,
            'feeding_log': feeding_log,
            'feeding_frequency': feeding_frequency,
            'stool_color': stool_color,
            'urinates': urinates,
            'expression_notes': expression_notes,
            'breastfeeding_notes': breastfeeding_notes,
            'baby_development_notes': baby_development_notes,
        }
    }

    entry['flags'] = detect_anomalies(entry)

    # Save to logs
    if not os.path.exists("logs"):
        os.makedirs("logs")

    with open(f"logs/{today}.json", 'w') as f:
        json.dump(entry, f, indent=4)

    print("\n✅ Log saved successfully!")
    print("\nFlags:")
    for flag in entry['flags']:
        print("•", flag)

if __name__ == "__main__":
    main()




# ********* STREAMLIT VERSION COMMENTED OUT ********


# import pandas as pd
# import streamlit as st
# from datetime import date, timedelta
# import json
# import os

# # Initialize session state
# if 'logs' not in st.session_state:
#     st.session_state.logs = []

# # Helper Functions
# def detect_anomalies(entry):
#     anomalies = []
#     mother = entry['mother']
#     baby = entry['baby']

#     if mother['mood'] in ['sad', 'anxious'] and mother['sleep_hours'] < 4:
#         anomalies.append("Mother is low on mood with less than 4 hours of sleep, ensure to get more sleep and try to engage in activities that will lift your mood and if it persists, kindly book a session with a therapist")

#     if mother['pain_level'] >= 6 and entry['days_since_delivery'] > 14:
#         anomalies.append("High pain level after 2 weeks postpartum")

#     if baby['feeding_frequency'] < 6:
#         anomalies.append("Baby feeding frequency is below recommended level")

#     if not baby['urinates']:
#         anomalies.append("Baby has not passed urine today")

#     if 'redness' in mother['wound_notes'].lower():
#         anomalies.append("Wound note indicates redness (possible infection)")

#     if mother['mood_log'].count("sad") + mother['mood_log'].count("anxious") >= 3:
#         anomalies.append("Consistent low mood signs - possible postpartum depression")

#     return anomalies

# def get_post_op_treatment(delivery_type):
#     if delivery_type == "Vaginal":
#         return [
#             "Keep the perineal area clean and dry.",
#             "Use warm sitz baths to relieve discomfort.",
#             "Use pain relievers or ice packs as needed.",
#             "Monitor for unusual vaginal discharge, odor, or pain.",
#             "Kegel exercises may help improve pelvic floor strength."
#         ]
#     elif delivery_type == "Cesarean":
#         return [
#             "Keep the incision clean and dry.",
#             "Avoid lifting anything heavier than the baby.",
#             "Take pain medication as prescribed.",
#             "Watch for fever, redness, or discharge around the incision.",
#             "Rest and avoid strenuous activity until cleared by a doctor."
#         ]
#     return []

# def get_baby_vaccination_due(weeks_since_delivery):
#     if weeks_since_delivery == 6:
#         return "First DTP, IPV, Hib, and HepB vaccine due"
#     elif weeks_since_delivery == 10:
#         return "Second DTP, IPV, Hib, and HepB vaccine due"
#     elif weeks_since_delivery == 14:
#         return "Third DTP, IPV, Hib, and HepB vaccine due"
#     elif weeks_since_delivery == 24:
#         return "First MMR, Varicella vaccine due"
#     elif weeks_since_delivery == 36:
#         return "Second MMR, Varicella vaccine due"
#     else:
#         return None

# def breastfeeding_nutrition_recommendations(weeks_since_delivery):
#     if weeks_since_delivery < 6:
#         return "Exclusive breastfeeding is highly recommended for the first 6 months. Aim for frequent feedings (8-12 times a day)."
#     elif weeks_since_delivery <= 12:
#         return "Continue breastfeeding on demand. You may introduce vitamin D supplements if recommended by your pediatrician."
#     elif weeks_since_delivery <= 24:
#         return "Breastfeeding continues, and you may start introducing solid foods at around 6 months. Continue breastfeeding as a primary source of nutrition."
#     else:
#         return "Continue breastfeeding and ensure your baby is eating a balanced diet with solids and breast milk."

# def baby_development_recommendations(weeks_since_delivery):
#     if weeks_since_delivery < 4:
#         return "Your baby is likely adjusting to life outside the womb. Expect some erratic sleep patterns, but they'll soon become more consistent."
#     elif weeks_since_delivery <= 8:
#         return "Your baby should be becoming more alert and starting to smile. Tummy time is essential for developing neck strength."
#     elif weeks_since_delivery <= 16:
#         return "At this stage, your baby may begin to roll over and possibly start babbling. Continue tummy time and engage your baby with stimulating toys."
#     elif weeks_since_delivery <= 24:
#         return "Your baby may be reaching for objects and showing interest in different textures. Continue fostering motor skills and provide more interactive playtime."
#     else:
#         return "Your baby may be starting to crawl or stand with support. Encourage these milestones with safe spaces to explore."

# # Sidebar: Mother Info
# st.sidebar.header("Mother Details")
# delivery_date = st.sidebar.date_input("Date of Delivery", value=date.today())
# type_of_delivery = st.sidebar.selectbox("Type of Delivery", ["Vaginal", "Cesarean"])
# feeding_style = st.sidebar.radio("Feeding Style", ["Exclusive Breastfeeding", "Mixed", "Formula Only", "None"])

# # Main Display
# today = date.today()
# days_since_delivery = (today - delivery_date).days
# weeks_since_delivery = days_since_delivery // 7
# st.title("👩‍👧 Postpartum Recovery Tracker")
# st.subheader(f"🗓️ Day {days_since_delivery} Postpartum")

# with st.form("log_form"):
#     st.markdown("## 👩 Mother Diary")
#     sleep_hours = st.slider("Sleep Hours (last 24h)", 0, 12, 6)
#     mood = st.multiselect("Mood Today", ["happy", "neutral", "sad", "anxious"])
#     pain_level = st.slider("Pain Level", 0, 10, 2)

#     st.markdown("### Mental Health Check")
#     stress_level = st.selectbox("Stress Level", ["Low", "Moderate", "High"])
#     emotional_state = st.selectbox("How are you feeling emotionally?", ["Stable", "Overwhelmed", "Irritable", "Tearful"])

#     st.markdown("### 🩺 Wound and Recovery")
#     wound_notes = st.text_area("Wound Condition Notes", placeholder="e.g. Mild swelling, redness, discharge")
    
#     wound_data = {'wound_notes': wound_notes}
#     if type_of_delivery == "Vaginal":
#         perineal_healing = st.radio("Perineal swelling or discomfort?", ["No", "Mild", "Severe"])
#         discharge = st.radio("Unusual vaginal discharge?", ["No", "Yes"])
#         stinging_urine = st.radio("Painful urination?", ["No", "Yes"])
#         wound_data.update({
#             'perineal_healing': perineal_healing,
#             'discharge': discharge,
#             'stinging_urine': stinging_urine
#         })
#     else:
#         incision_appearance = st.radio("Incision issues (red/swollen/leaking)?", ["No", "Mild", "Severe"])
#         fever_present = st.radio("Fever present?", ["No", "Yes"])
#         wound_data.update({
#             'incision_appearance': incision_appearance,
#             'fever_present': fever_present
#         })

#     post_op_med = st.radio("Have you taken post-op medication today?", ["Yes", "No"])
#     wound_data['post_op_medication'] = post_op_med

#     st.markdown("### 🧍 Body Changes")
#     body_changes = st.multiselect(
#         "Select observed changes:",
#         ["Swelling", "Hair loss", "Breast tenderness", "Fatigue", "Mood swings", "Abdominal cramps", "Back pain", "None"]
#     )

#     # Calculate next baby vaccination based on weeks since delivery
#     next_baby_vaccination = get_baby_vaccination_due(weeks_since_delivery)

#     st.markdown("## 👶 Baby Diary")
#     baby_sleep = st.slider("Baby Sleep Hours", 0, 20, 10)
#     feeding_log = st.text_input("Last Feeding (e.g. 20 mins left breast)")
#     feeding_frequency = st.slider("Feedings in past 24h", 0, 12, 8)
#     stool_color = st.selectbox("Stool Color", ["Yellow", "Brown", "Green", "Black"])
#     urinates = st.checkbox("Did baby pass urine today?")
#     expression_notes = st.text_area("Baby’s Expressions / Behaviors", placeholder="Crying, smiling, quiet, etc.")
    
#     # New sections for breastfeeding, nutritional needs, and baby development
#     st.markdown("### 🍼 Breastfeeding & Nutritional Needs")
#     breastfeeding_notes = st.text_area("Breastfeeding and Nutritional Needs Notes", placeholder="e.g. Frequency, duration, issues")
#     breastfeeding_advice = breastfeeding_nutrition_recommendations(weeks_since_delivery)
#     st.markdown(f"**💡 Advice:** {breastfeeding_advice}")
  
#     st.markdown("### 👶 Baby Development & Health")
#     baby_development_notes = st.text_area("Baby Development and Health Notes", placeholder="e.g. Milestones, growth patterns")
#     baby_development_advice = baby_development_recommendations(weeks_since_delivery)
#     st.markdown(f"**💡 Advice:** {baby_development_advice}")

#     # Display next vaccination notification
#     if next_baby_vaccination:
#         st.markdown(f"**📅 Next Baby Vaccination Due**: {next_baby_vaccination}")
#     else:
#         st.markdown("**📅 No upcoming vaccinations for the baby yet.**")
#     submitted = st.form_submit_button("Save Today's Entry")

#     if submitted:
#         entry = {
#             'date': str(today),
#             'days_since_delivery': days_since_delivery,
#             'type_of_delivery': type_of_delivery,
#             'mother': {
#                 'sleep_hours': sleep_hours,
#                 'mood': mood,
#                 'mood_log': [mood],
#                 'stress_level': stress_level,
#                 'emotional_state': emotional_state,
#                 'pain_level': pain_level,
#                 'feeding_style': feeding_style,
#                 'wound_notes': wound_notes,
#                 'wound_data': wound_data,
#                 'body_changes': body_changes,
#             },
#             'baby': {
#                 'sleep_hours': baby_sleep,
#                 'feeding_log': feeding_log,
#                 'feeding_frequency': feeding_frequency,
#                 'stool_color': stool_color,
#                 'urinates': urinates,
#                 'expression_notes': expression_notes,
#                 'breastfeeding_notes': breastfeeding_notes,
#                 'baby_development_notes': baby_development_notes,
#             }
#         }

#         entry['flags'] = detect_anomalies(entry)
#         st.session_state.logs.append(entry)
        
#         # Save logs to JSON
#         if not os.path.exists("logs"):
#             os.makedirs("logs")
        
#         with open(f"logs/{today}.json", 'w') as f:
#             json.dump(entry, f, indent=4)
        
#         st.success("✅ Log saved successfully!")

# # Function to estimate ovulation delay based on breastfeeding
# def track_postpartum_cycle(breastfeeding_duration):
#     delay = breastfeeding_duration * 0.5
#     return f"Possible ovulation delay: {delay:.1f} months"


# # 🔄 Ovulation Resumption Tracker
# st.markdown("### 🩺 Ovulation Resumption Tracker")
# breastfeeding_duration = st.number_input("Breastfeeding Duration (months)", min_value=0.0, step=0.1, format="%.1f")
# if st.button("Track Ovulation Resumption"):
#     st.info(track_postpartum_cycle(breastfeeding_duration))

# # View Logs
# st.markdown("---")
# st.header("📊 Recovery Insights")

# if st.session_state.logs:
#     for log in reversed(st.session_state.logs):
#         with st.expander(f"📅 {log['date']} - Day {log['days_since_delivery']}"):
#             st.write("### Mother Overview")
#             st.dataframe(pd.DataFrame([log['mother']]))  # Displaying mother data in tabular format

#             st.write("### Baby Overview")
#             st.dataframe(pd.DataFrame([log['baby']]))  # Displaying baby data in tabular format

#             if log['flags']:
#                 st.warning("⚠️ Issues Detected:")
#                 for flag in log['flags']:
#                     st.markdown(f"- {flag}")
#             else:
#                 st.success("✅ No issues detected for this entry.")

#             st.markdown("### 📌 Post-operative Care Advice")
#             care_recommendations = get_post_op_treatment(log['type_of_delivery'])
#             for tip in care_recommendations:
#                 st.markdown(f"- {tip}")
# else:
#     st.info("Start tracking by submitting your first diary entry above.")




