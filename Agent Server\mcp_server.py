from fastapi import FastAPI
import uvicorn
import ollama
import faiss
import pickle
import numpy as np
import json
from pydantic import BaseModel
from typing import Dict, List
from langchain_ollama import OllamaEmbeddings
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import asyncio
import traceback
from contextlib import asynccontextmanager

# Import necessary modules for the agent
from langchain_core.runnables import <PERSON>nableLambda
from langgraph.graph import StateGraph, END
from langchain_core.prompts import ChatPromptTemplate
from langchain_ollama import ChatOllama
from pydantic import BaseModel

# Import tools
from tools.tools_health_score import HealthScoreAnalysisTool
from tools.tools_vector import vector_search_tool
from tools.tools_monitor_vital_signs import vital_sign_monitoring_tool
from tools.tools_health_consult import automated_health_consultation_tool
from tools.tools_health_data_json import get_default_health_data
from tools.tools_kidney_function import kidney_function_analysis_tool

# Define lifespan context manager
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup: Load FAISS index and metadata
    print("🌱 Server starting...")

    # Load FAISS index and metadata only during the startup event
    for model_name, paths in VECTOR_STORE_PATHS.items():
        try:
            vector_indexes[model_name] = faiss.read_index(paths["index"])
            print(f"✅ FAISS index loaded for {model_name}")
        except Exception as e:
            print(f"❌ Error loading FAISS index for {model_name}: {e}")

        try:
            with open(paths["metadata"], "rb") as f:
                vector_docs[model_name] = pickle.load(f)
            print(f"✅ Metadata loaded for {model_name}")
        except Exception as e:
            print(f"❌ Error loading metadata for {model_name}: {e}")

        try:
            embedding_models[model_name] = OllamaEmbeddings(model="qwen2.5:1.5b")
            print(f"✅ Embedding model loaded for {model_name}")
        except Exception as e:
            print(f"❌ Error loading embedding model {model_name}: {e}")

    yield

    # Shutdown: Clean up resources
    print("🔴 Server shutting down...")

# Initialize FastAPI app
app = FastAPI(lifespan=lifespan)

# === MODEL CONSTANTS ===
QWEN_MODEL = "qwen2.5:1.5b"
DEEPSEEK_MODEL = "deepseek-r1:1.5b"

# === VECTOR STORE PATHS ===
VECTOR_STORE_PATHS = {
    QWEN_MODEL: {
        "index": "Vector_Store/index.faiss",
        "metadata": "Vector_Store/index.pkl"
    },
    DEEPSEEK_MODEL: {
        "index": "Vector_Store/index.faiss",
        "metadata": "Vector_Store/index.pkl"
    }
}

# Initialize vector store components
vector_indexes = {}
vector_docs = {}
embedding_models = {}

# === CHAT HISTORY CONFIGURATION ===
chat_histories: Dict[str, List[Dict[str, str]]] = {}
MAX_HISTORY_LENGTH = 10
TOP_K = 1  # Number of relevant documents to fetch

# === STATE MODEL ===
class GraphState(BaseModel):
    question: str
    llm_output: str = ""
    final_response: str = ""
    tools_used: list[str] = []
    health_data_collected: bool = False
    health_data: dict = {}
    kidney_data_collected: bool = False
    kidney_data: dict = {}
    kidney_analysis_result: dict = {}
    mode: str = "default"  # mode could be "score", "consult", "monitor", etc.

# === REQUEST MODELS ===
class ChatRequest(BaseModel):
    user_id: str
    query: str
    model: str  # ✅ Add model selection from user input
    mode: str = "default"  # mode could be "score", "consult", "monitor", etc.

class VitalSignsRequest(BaseModel):
    user_id: str
    vital_signs: dict

class HealthScoreRequest(BaseModel):
    user_id: str
    health_data: dict

class KidneyFunctionRequest(BaseModel):
    user_id: str
    kidney_data: dict

# === ASYNC FUNCTION TO RETRIEVE CONTEXT ===
async def retrieve_context(query: str, model_name: str, top_k: int = TOP_K):
    if model_name not in vector_indexes or model_name not in vector_docs:
        print(f"❌ Model '{model_name}' not found in vector index or documents.")
        return ""

    try:
        embedder = embedding_models[model_name]
        query_embedding = embedder.embed_query(query)

        if not query_embedding or not isinstance(query_embedding, list):
            raise ValueError("Embedding returned is empty or invalid.")

        query_embedding = np.array([query_embedding]).astype("float32")

        index = vector_indexes[model_name]
        documents = vector_docs[model_name]

        distances, indices = index.search(query_embedding, top_k)

        relevant_docs = [
            documents[idx].get("text", "") if isinstance(documents[idx], dict) else str(documents[idx])
            for idx in indices[0] if idx < len(documents)
        ]

        print(f"✅ Retrieved {len(relevant_docs)} relevant docs for model {model_name}")
        return " ".join(relevant_docs)

    except Exception as e:
        print(f"❌ Error during context retrieval for model {model_name}: {e}")
        traceback.print_exc()
        return ""

# === DEFAULT HEALTH DATA ===
DEFAULT_HEALTH_DATA = {
    "Glucose": None,
    "SpO2": None,
    "ECG (Heart Rate)": None,
    "Blood Pressure (Systolic)": None,
    "Blood Pressure (Diastolic)": None,
    "Weight (BMI)": None,
    "Temperature": None,
    "Malaria": "Unknown",
    "Widal Test": "Unknown",
    "Hepatitis B": "Unknown",
    "Voluntary Serology": "Unknown",
    "Perfusion_index": None,
    "Waist Circumference": None,
    "Fev": None
}

# === INITIALIZE TOOLS ===
health_score_tool = HealthScoreAnalysisTool()

# === AGENT NODES ===
def run_llm(state: GraphState) -> GraphState:
    """Process the user query with the LLM."""
    # Check if the question is about kidney function recommendations and we have data
    if state.kidney_data_collected and any(keyword in state.question.lower() for keyword in ["kidney", "renal", "recommendation", "advice", "what should i do"]):
        # Generate personalized recommendations based on kidney function test results
        abnormal_params = []
        for analysis_item in state.kidney_analysis_result.get("analysis", []):
            if "High" in analysis_item or "Low" in analysis_item:
                param = analysis_item.split(":")[0].strip()
                abnormal_params.append(param)

        response = "Based on your kidney function test results, here are personalized recommendations:\n\n"

        if abnormal_params:
            if "eGFR" in abnormal_params:
                response += "- **For low eGFR**: Limit protein intake to reduce kidney workload. Aim for 0.8g of protein per kg of body weight daily. Reduce salt consumption to help control blood pressure. Stay well-hydrated but avoid excessive fluid intake.\n\n"
            if "Serum Creatinine" in abnormal_params:
                response += "- **For high Serum Creatinine**: Avoid creatine supplements which can increase creatinine levels. Limit red meat consumption. Stay hydrated to help your kidneys filter waste efficiently.\n\n"
            if "BUN" in abnormal_params or "Serum Urea" in abnormal_params:
                response += "- **For elevated BUN/Urea**: Reduce protein intake, especially animal proteins. Ensure adequate hydration to help flush out urea.\n\n"
            if "Serum Sodium" in abnormal_params:
                response += "- **For abnormal Sodium levels**: Consult with a doctor about appropriate fluid and salt intake. Sodium imbalances can indicate kidney dysfunction.\n\n"
            if "Serum Potassium" in abnormal_params:
                response += "- **For abnormal Potassium levels**: This can be serious and requires medical attention. Discuss with your doctor about dietary adjustments and possible medication review.\n\n"
            if "ACR" in abnormal_params:
                response += "- **For elevated ACR (albumin-to-creatinine ratio)**: This indicates protein in urine. Control blood pressure and blood sugar levels, and reduce salt intake.\n\n"
        else:
            response += "Your kidney function parameters appear to be within normal ranges. To maintain kidney health:\n\n"
            response += "- Stay well-hydrated\n"
            response += "- Maintain a balanced diet low in sodium\n"
            response += "- Exercise regularly\n"
            response += "- Avoid smoking and limit alcohol consumption\n"
            response += "- Control blood pressure and blood sugar levels\n\n"

        return GraphState(
            question=state.question,
            llm_output=response,
            tools_used=state.tools_used,
            health_data_collected=state.health_data_collected,
            kidney_data_collected=state.kidney_data_collected,
            kidney_data=state.kidney_data,
            kidney_analysis_result=state.kidney_analysis_result,
            mode=state.mode
        )

    # Normal LLM processing for other queries
    prompt = ChatPromptTemplate.from_messages([
        ("system",
         "You are Dr. Deuce, a certified and authorized medical assistant. "
         "You assist users by analyzing their health data, monitoring vitals, and providing health consultations. "
         "Engage users in a friendly, conversational tone. ALWAYS ask relevant follow-up questions at the end of your responses. "
         "Be empathetic and personable. Address the user directly using 'you' and 'your'. "
         "Provide personalized advice based on the user's specific situation. "
         "Make your responses feel like a real conversation with a caring doctor. "
         "Always respond in English and end with a question to keep the conversation going."),
        ("human", "{question}")
    ])

    llm = ChatOllama(model="qwen2.5:1.5b")
    chain = prompt | llm
    result = chain.invoke({"question": state.question})

    return GraphState(
        question=state.question,
        llm_output=result.content,
        tools_used=state.tools_used,
        health_data_collected=state.health_data_collected,
        kidney_data_collected=state.kidney_data_collected,
        kidney_data=state.kidney_data,
        kidney_analysis_result=state.kidney_analysis_result,
        mode=state.mode
    )

# Helper functions
def detect_abnormal_patterns(vitals_data):
    alerts = []
    if vitals_data.get("Glucose") and vitals_data["Glucose"] > 140:
        alerts.append("High glucose levels detected. Consider consulting a doctor.")
    if vitals_data.get("SpO2") and vitals_data["SpO2"] < 95:
        alerts.append("Low SpO2 levels detected. Ensure proper ventilation.")
    if vitals_data.get("ECG (Heart Rate)") and vitals_data["ECG (Heart Rate)"] > 100:
        alerts.append("High heart rate detected. Practice stress management.")
    if vitals_data.get("Temperature") and vitals_data["Temperature"] > 37.5:
        alerts.append("Fever detected. Stay hydrated and consult a doctor if it persists.")
    return "\n".join(alerts) if alerts else "No abnormal patterns detected."

def recommend_lifestyle_changes(vitals_data):
    recommendations = []
    if vitals_data.get("Glucose") and vitals_data["Glucose"] > 140:
        recommendations.append("Consider reducing sugar intake and exercising regularly.")
    if vitals_data.get("SpO2") and vitals_data["SpO2"] < 95:
        recommendations.append("Ensure proper ventilation and consider breathing exercises.")
    if vitals_data.get("ECG (Heart Rate)") and vitals_data["ECG (Heart Rate)"] > 100:
        recommendations.append("Practice stress management techniques like meditation.")
    if vitals_data.get("Temperature") and vitals_data["Temperature"] > 37.5:
        recommendations.append("Stay hydrated and consult a doctor if fever persists.")
    return "\n".join(recommendations) if recommendations else "No specific recommendations at this time."

def run_tool_logic(state: GraphState) -> GraphState:
    """Run the appropriate tool based on the user query."""
    llm_output = state.llm_output or ""
    llm_output_lower = llm_output.lower()

    # Clear LLM output after capturing
    state.llm_output = ""

    # Health Score Analysis
    if "health score" in llm_output_lower or state.mode == "score":
        try:
            # Use default health data or data from the request
            user_health_data = state.health_data if state.health_data_collected else DEFAULT_HEALTH_DATA.copy()

            report = health_score_tool.generate_report(user_health_data)

            total_score = report.get('Total Score', 0)
            status = report.get('Health Status', 'Unknown')

            vitals_needing_improvement = report.get('Vitals Needing Improvement', 'N/A')
            improvement_tips = report.get('Improvement Tips', 'N/A')

            if str(status).lower() == "excellent":
                summary = f"Amazing! Your current health score is {total_score}%. You're in excellent health — that's really impressive! Your test results show that you're taking great care of yourself."
                follow_up = "What specific health habits have been working well for you that you'd like to maintain?"
            elif str(status).lower() == "good":
                summary = f"Good news! Your current health score is {total_score}%. You're doing well overall — your body is in good condition. Let's talk about how to maintain and even improve this positive trend."
                follow_up = "Are there any specific areas of your health that you're particularly concerned about or would like to improve further?"
            elif str(status).lower() == "fair":
                summary = f"Your health score is {total_score}%, which indicates a fair condition. You're on the right track, but I see some specific areas where we can make meaningful improvements together."
                follow_up = "Would you like to focus on addressing these specific areas one by one? We can start with what feels most manageable for you."
            elif str(status).lower() == "poor":
                summary = f"I see your health score is currently {total_score}%. While this indicates some health challenges, please don't be discouraged. I'm here to help you make positive changes step by step."
                follow_up = "How have you been feeling lately? Any symptoms or concerns that have been bothering you that we should address first?"
            else:
                summary = f"Based on your health data, your score is {total_score}%. Let's look at the specific details to understand your health status better."
                follow_up = "Is there any part of your health assessment that surprised you or that you'd like to discuss further?"

            # Create personalized recommendations based on the vitals
            personalized_recommendations = ""
            if "Glucose" in vitals_needing_improvement:
                personalized_recommendations += "\n• I notice your glucose levels need attention. Small changes like reducing sugary drinks and adding more fiber to your diet can make a big difference. Even a 10-minute walk after meals can help regulate your blood sugar."
            if "SpO2" in vitals_needing_improvement:
                personalized_recommendations += "\n• Your oxygen saturation levels could be improved. Simple breathing exercises for 5 minutes daily can help, and ensuring your sleeping position allows for optimal breathing is important for you."
            if "ECG" in vitals_needing_improvement or "Heart Rate" in vitals_needing_improvement:
                personalized_recommendations += "\n• Your heart rate patterns suggest we should focus on heart health. For your specific situation, gentle cardio exercises like walking or swimming would be beneficial, starting with just 15 minutes daily."
            if "Blood Pressure" in vitals_needing_improvement:
                personalized_recommendations += "\n• Your blood pressure readings indicate this is an area for improvement. Based on your profile, reducing sodium intake and practicing specific relaxation techniques like deep breathing for 5 minutes twice daily could be particularly effective for you."

            state.llm_output += (
                f"\n\n{summary}"
                f"\n\n📊 Health Score Analysis:\n"
                f"Total Score: {total_score}\n"
                f"Health Status: {status}\n"
                f"Areas Needing Attention: {vitals_needing_improvement}\n"
            )

            if personalized_recommendations:
                state.llm_output += f"\n💡 Personalized Recommendations for You: {personalized_recommendations}\n"
            else:
                state.llm_output += f"\n💡 General Recommendations: {improvement_tips}\n"

            state.llm_output += f"\n{follow_up}"

            state.tools_used.append("health_score_tool")
            state.health_data_collected = True

        except Exception as e:
            state.llm_output += f"\n\n⚠️ Error processing health data: {e}"

    # Kidney Function Test
    elif any(keyword in llm_output_lower for keyword in ["kidney", "kidney function", "kidney test", "renal"]) or state.mode == "kidney":
        try:
            # Use kidney data from the request
            if state.kidney_data and not state.kidney_data_collected:
                kidney_data = state.kidney_data

                # Run kidney function analysis
                result = kidney_function_analysis_tool(kidney_data)

                # Store kidney data and analysis results in state for personalized recommendations
                state.kidney_data = kidney_data
                state.kidney_data_collected = True
                state.kidney_analysis_result = result

                # Format the analysis for display
                formatted_analysis = "\n".join(result.get("analysis", []))

                # Get overall health assessment
                overall_health = result.get('overall_health', 'Unknown')

                # Create a personalized introduction based on overall health
                if "normal" in overall_health.lower() or "healthy" in overall_health.lower():
                    intro = "I've analyzed your kidney function test results, and I'm pleased to tell you that your kidneys appear to be functioning well! This is great news for your overall health."
                elif "mild" in overall_health.lower() or "slight" in overall_health.lower():
                    intro = "Thank you for sharing your kidney function test results. I've analyzed them carefully, and I can see some mild changes in your kidney function. The good news is that with some targeted lifestyle adjustments, we can work on improving these values."
                elif "moderate" in overall_health.lower():
                    intro = "I've carefully reviewed your kidney function test results. There are some moderate changes that we should address together. While this requires attention, many people successfully manage similar kidney function levels with proper care."
                elif "severe" in overall_health.lower() or "significant" in overall_health.lower():
                    intro = "After analyzing your kidney function test results, I can see that there are significant changes that need our immediate attention. I want to assure you that there are many ways we can help support your kidney health moving forward."
                else:
                    intro = "Thank you for sharing your kidney function test results. I've analyzed them carefully to give you a personalized assessment of your kidney health."

                # Build the response
                state.llm_output += f"\n\n{intro}\n\n"
                state.llm_output += f"🔹 Detailed Analysis:\n\n{formatted_analysis}\n\n"
                state.llm_output += f"🔹 Summary:\n{overall_health}\n\n"

                # Add confidence level
                confidence_level = result.get('confidence_level', 'Unknown')
                missing_params = result.get('missing_parameters', [])
                if missing_params:
                    state.llm_output += f"🔹 Assessment Confidence: {confidence_level}\n"
                    state.llm_output += f"Note: For an even more complete picture of your kidney health, additional tests like {', '.join(missing_params)} would be helpful in our next assessment.\n\n"
                else:
                    state.llm_output += f"🔹 Assessment Confidence: {confidence_level} (We have a complete dataset for a thorough analysis)\n\n"

                # Add personalized recommendations based on abnormal values
                abnormal_params = []
                for analysis_item in result.get("analysis", []):
                    if "High" in analysis_item or "Low" in analysis_item:
                        param = analysis_item.split(":")[0].strip()
                        abnormal_params.append(param)

                if abnormal_params:
                    state.llm_output += f"\n💡 Your Personalized Kidney Health Plan:\n"
                    if "eGFR" in abnormal_params:
                        state.llm_output += "• Looking at your eGFR value, I recommend adjusting your protein intake to about 0.8g per kg of your body weight daily. For someone with your profile, this means focusing on plant proteins like lentils and beans more than animal proteins. Also, try to reduce your salt intake to help manage blood pressure, which directly impacts kidney function.\n\n"
                    if "Serum Creatinine" in abnormal_params:
                        state.llm_output += "• Your Serum Creatinine level suggests we should make some dietary adjustments. Specifically for you, I'd recommend avoiding creatine supplements completely and limiting red meat to once or twice a week. Staying well-hydrated is particularly important with your values - aim for about 2-3 liters of water daily, spread throughout the day.\n\n"
                    if "BUN" in abnormal_params or "Serum Urea" in abnormal_params:
                        state.llm_output += "• With your elevated BUN/Urea levels, I recommend reducing your protein intake, particularly animal proteins. For your specific situation, focusing on quality proteins rather than quantity would be beneficial. Also, ensure you're drinking enough water throughout the day to help your kidneys filter waste more efficiently.\n\n"
                    if "Serum Sodium" in abnormal_params:
                        state.llm_output += "• Your Sodium levels indicate we should pay attention to your fluid and salt balance. Based on your specific values, I recommend discussing with your doctor about the right amount of fluid intake for you. In the meantime, try using herbs and spices instead of salt to flavor your food.\n\n"
                    if "Serum Potassium" in abnormal_params:
                        state.llm_output += "• Your Potassium level requires attention. This is important to address, as potassium directly affects heart function. For your specific situation, I recommend discussing with your doctor about dietary adjustments and possible medication review. In the meantime, be cautious with high-potassium foods like bananas, oranges, and potatoes.\n\n"
                    if "ACR" in abnormal_params:
                        state.llm_output += "• Your ACR (albumin-to-creatinine ratio) indicates some protein in your urine. For your specific case, controlling blood pressure and blood sugar levels is crucial. I recommend monitoring your blood pressure regularly and reducing salt intake to less than 5g per day.\n\n"

                    # Add a follow-up question
                    state.llm_output += "How do these recommendations sound? Would you like more specific guidance on any particular aspect of your kidney health plan?"

                state.tools_used.append("kidney_function_tool")

            else:
                state.llm_output += "\n\n⚠️ No kidney function data provided. Please provide kidney function test results for analysis."

        except Exception as e:
            state.llm_output += f"\n\n⚠️ Error analyzing kidney function: {str(e)}"

    # Vitals Monitoring
    elif "vital signs" in llm_output_lower or "monitor" in llm_output_lower or state.mode == "monitor":
        try:
            # Use health data from the request
            if state.health_data and state.health_data_collected:
                user_vitals_data = state.health_data

                health_data_json = json.dumps({"data": user_vitals_data})
                result = vital_sign_monitoring_tool.invoke(health_data_json)

                # Create a personalized introduction based on vitals
                intro = "I've carefully analyzed your vital signs, and I'd like to share what I'm seeing."

                # Check for specific vital sign issues to personalize the intro
                if user_vitals_data.get("Glucose") and user_vitals_data["Glucose"] > 140:
                    intro = "Thank you for sharing your vital signs with me. I notice your glucose levels are a bit elevated, which is something we should discuss."
                elif user_vitals_data.get("SpO2") and user_vitals_data["SpO2"] < 95:
                    intro = "I've reviewed your vital signs, and I notice your oxygen saturation (SpO2) is lower than ideal. Let's talk about what this means for you."
                elif user_vitals_data.get("ECG (Heart Rate)") and user_vitals_data["ECG (Heart Rate)"] > 100:
                    intro = "After looking at your vital signs, I see your heart rate is elevated. I'd like to discuss what might be causing this and how we can address it."
                elif user_vitals_data.get("Temperature") and user_vitals_data["Temperature"] > 37.5:
                    intro = "I see you're running a slight fever based on your vital signs. Let's talk about what might be causing this and how you're feeling overall."

                state.llm_output += f"\n\n{intro}\n\n"

                if isinstance(result, dict):
                    state.llm_output += f"🩺 Vital Signs Assessment:\n{result.get('message', '')}\n"
                else:
                    state.llm_output += f"🩺 Vital Signs Assessment:\n{result}\n"

                # Add personalized alerts
                alerts = detect_abnormal_patterns(user_vitals_data)
                if alerts:
                    state.llm_output += f"\n⚠️ Important Findings for You:\n{alerts}\n"

                # Add personalized recommendations
                state.llm_output += "\n💡 Your Personalized Health Plan:\n"

                if user_vitals_data.get("Glucose") and user_vitals_data["Glucose"] > 140:
                    state.llm_output += "• For your elevated glucose levels: Try replacing one sugary drink each day with water or unsweetened tea. Even this small change can make a difference. Also, a short 10-minute walk after meals can help your body process glucose more effectively.\n\n"

                if user_vitals_data.get("SpO2") and user_vitals_data["SpO2"] < 95:
                    state.llm_output += "• For your oxygen levels: I recommend practicing deep breathing exercises for 5 minutes, three times daily. Sit upright, breathe in slowly through your nose for 4 counts, hold for 2, and exhale through your mouth for 6 counts. This can help improve your oxygen saturation.\n\n"

                if user_vitals_data.get("ECG (Heart Rate)") and user_vitals_data["ECG (Heart Rate)"] > 100:
                    state.llm_output += "• For your elevated heart rate: Try a simple relaxation technique called 'box breathing' - breathe in for 4 seconds, hold for 4 seconds, exhale for 4 seconds, and hold for 4 seconds before repeating. Doing this for just 2 minutes when you feel stressed can help lower your heart rate.\n\n"

                if user_vitals_data.get("Temperature") and user_vitals_data["Temperature"] > 37.5:
                    state.llm_output += "• For your elevated temperature: Make sure you're staying well-hydrated with at least 8 glasses of water daily. Rest is also important - try to get at least 7-8 hours of sleep tonight to help your body recover.\n\n"

                # If no specific recommendations were added, add general ones
                if not (user_vitals_data.get("Glucose") and user_vitals_data["Glucose"] > 140 or
                        user_vitals_data.get("SpO2") and user_vitals_data["SpO2"] < 95 or
                        user_vitals_data.get("ECG (Heart Rate)") and user_vitals_data["ECG (Heart Rate)"] > 100 or
                        user_vitals_data.get("Temperature") and user_vitals_data["Temperature"] > 37.5):
                    recommendations = recommend_lifestyle_changes(user_vitals_data)
                    state.llm_output += f"{recommendations}\n\n"

                # Add consultation suggestion if needed
                if isinstance(result, dict) and result.get("suggest_consultation"):
                    state.llm_output += "\n📞 Based on your vital signs, I would recommend speaking with a healthcare professional. Would you like me to help you book an appointment? It would be good to have this checked within the next few days.\n\n"

                # Add a follow-up question
                state.llm_output += "How have you been feeling lately compared to normal? Have you noticed any changes in your energy levels or other symptoms?"

                state.tools_used.append("vital_monitor_tool")

            else:
                state.llm_output += "\n\n⚠️ No vital signs data provided. Please provide vital signs data for monitoring."

        except Exception as e:
            state.llm_output += f"\n\n⚠️ Error monitoring vital signs: {e}"

    # Health Consultation
    elif any(keyword in llm_output_lower for keyword in ["consult", "consultation", "doctor", "doctor advice", "see a doctor"]) or state.mode == "consult":
        try:
            # Ensure the health data is being passed correctly to the tool
            if state.health_data_collected and state.health_data:
                # Convert health data to JSON format
                health_data_json = json.dumps({"data": state.health_data})

                # Invoke the consultation tool
                consultation_result = automated_health_consultation_tool.invoke(health_data_json)

                # Process the result from the tool
                result_json = json.loads(consultation_result)
                if 'Medical_Advice' in result_json:
                    # Create a personalized introduction
                    if result_json.get("Doctor_Visit_Recommended", False):
                        intro = "Thank you for sharing your health information with me. After carefully reviewing your data, I've identified some areas that would benefit from a professional medical evaluation."
                    else:
                        intro = "I've thoroughly reviewed your health information, and I'm pleased to share my assessment with you. There are some specific insights I'd like to highlight that are particularly relevant to your situation."

                    # Extract medical advice and personalize it
                    medical_advice = result_json['Medical_Advice']

                    # Add personalized recommendations based on the medical advice
                    personalized_advice = "Based on your specific health profile, here's what I recommend:\n\n"

                    if "blood pressure" in medical_advice.lower() or "hypertension" in medical_advice.lower():
                        personalized_advice += "• For your blood pressure readings: Try the DASH diet approach, which includes reducing sodium to about 1,500mg daily and increasing foods rich in potassium, calcium, and magnesium. For someone with your profile, even a 5-10 minute daily walk can make a meaningful difference.\n\n"

                    if "glucose" in medical_advice.lower() or "diabetes" in medical_advice.lower() or "sugar" in medical_advice.lower():
                        personalized_advice += "• Regarding your glucose levels: Consider eating smaller meals more frequently throughout the day rather than three large meals. This can help maintain more stable blood sugar levels. Also, try to pair carbohydrates with proteins or healthy fats to slow glucose absorption.\n\n"

                    if "cholesterol" in medical_advice.lower() or "lipid" in medical_advice.lower():
                        personalized_advice += "• For your cholesterol profile: Adding just 2 tablespoons of ground flaxseed to your daily diet can help improve your lipid profile. Replacing red meat with fatty fish like salmon twice a week can also make a significant difference for someone with your numbers.\n\n"

                    if "weight" in medical_advice.lower() or "bmi" in medical_advice.lower() or "obesity" in medical_advice.lower():
                        personalized_advice += "• Regarding weight management: Rather than focusing on restrictive dieting, I recommend starting with mindful eating practices - taking time to enjoy your food without distractions like TV or phones. This simple change has shown to reduce overall calorie intake by 10-15% for many people.\n\n"

                    # Build the complete response
                    state.llm_output += f"\n\n{intro}\n\n"
                    state.llm_output += f"🩺 Health Assessment:\n{medical_advice}\n\n"
                    state.llm_output += f"{personalized_advice}"

                    if result_json.get("Doctor_Visit_Recommended", False):
                        consultation_link = "https://your-appointment-booking-system.com/consultation"  # Placeholder link
                        state.llm_output += f"\n📞 Based on your health profile, I recommend scheduling a check-up with a healthcare provider. You can book an appointment here: {consultation_link}\n\n"
                        state.llm_output += "How soon would you be able to schedule this appointment? Is there anything specific you'd like me to help you prepare for your doctor's visit?"
                    else:
                        state.llm_output += "\nHow do these recommendations sound? Is there any specific area of your health you'd like more detailed guidance on?"
                else:
                    state.llm_output += "\n\n⚠️ I apologize, but I encountered an issue while processing your health data. Could you please confirm that all the information you provided is correct? Would you like to try again?"

                # Mark the consultation tool as used
                state.tools_used.append("health_consult_tool")

            else:
                state.llm_output += "\n\n⚠️ No health data provided. Please provide health data for consultation."

        except Exception as e:
            state.llm_output += f"\n\n⚠️ Dr. Deuce: An error occurred while processing your consultation request: {e}"

    # Extra Information (Vector Search)
    elif "vector" in llm_output_lower or any(keyword in state.question.lower() for keyword in ["what is", "tell me about", "explain", "information", "learn", "know"]):
        try:
            result = vector_search_tool.invoke({"query": state.question})

            # Create a conversational introduction based on the query
            intro = "I'd be happy to share some information about that with you."

            if "diabetes" in state.question.lower():
                intro = "Great question about diabetes. This is an important health topic that affects millions of people worldwide."
            elif "blood pressure" in state.question.lower() or "hypertension" in state.question.lower():
                intro = "I'm glad you're asking about blood pressure. It's one of the most important vital signs and a key indicator of your cardiovascular health."
            elif "kidney" in state.question.lower() or "renal" in state.question.lower():
                intro = "Thank you for your question about kidney health. The kidneys play a crucial role in your overall wellbeing."
            elif "diet" in state.question.lower() or "nutrition" in state.question.lower() or "food" in state.question.lower():
                intro = "I'm happy to discuss nutrition with you. What we eat has a profound impact on our health and wellbeing."
            elif "exercise" in state.question.lower() or "physical activity" in state.question.lower() or "workout" in state.question.lower():
                intro = "Exercise is a fantastic topic to discuss. Regular physical activity is one of the most important things you can do for your health."

            # Format the response in a conversational way
            state.llm_output += f"\n\n{intro}\n\n"
            state.llm_output += f"{result}\n\n"
            state.llm_output += "Does this information help with what you were looking for? Is there anything specific about this topic you'd like me to explain further?"

            state.tools_used.append("vector_search_tool")
        except Exception as e:
            state.llm_output += f"\n\n⚠️ I apologize, but I couldn't find the information you're looking for. Could you try rephrasing your question or asking about something else?"

    return state

def parse_output(state: GraphState) -> GraphState:
    """Parse the output from the tools and prepare the final response."""
    return GraphState(
        question=state.question,
        llm_output=state.llm_output,
        final_response=state.llm_output,
        tools_used=state.tools_used,
        health_data_collected=state.health_data_collected,
        health_data=state.health_data,
        kidney_data_collected=state.kidney_data_collected,
        kidney_data=state.kidney_data,
        kidney_analysis_result=state.kidney_analysis_result,
        mode=state.mode
    )

# === FUNCTION TO CALL LANGGRAPH AGENT ===
async def generate_agent_response(user_id: str, query: str, model_name: str, mode: str = "default"):
    try:
        # Create the agent graph
        graph = StateGraph(GraphState)
        graph.add_node("llm", RunnableLambda(run_llm))
        graph.add_node("tool_logic", RunnableLambda(run_tool_logic))
        graph.add_node("parse", RunnableLambda(parse_output))

        graph.set_entry_point("llm")
        graph.add_edge("llm", "tool_logic")
        graph.add_edge("tool_logic", "parse")
        graph.add_edge("parse", END)

        agent_app = graph.compile()

        # Prepare initial state
        initial_state = GraphState(question=query, mode=mode)

        # Run the agent
        final_state = await asyncio.to_thread(agent_app.invoke, initial_state)

        # Return response
        return {
            "response": final_state.get("final_response", ""),
            "tools_used": final_state.get("tools_used", []),
            "health_data": final_state.get("health_data", {}),
            "chat_mode": mode
        }

    except Exception as e:
        traceback.print_exc()
        return {"error": f"Failed to run health agent: {e}"}

# === API ENDPOINTS ===

# Status endpoint
@app.get("/status")
async def get_status():
    return {"status": "online", "models": [QWEN_MODEL, DEEPSEEK_MODEL]}

# Default health data endpoint
@app.get("/default-health-data")
async def get_default_health_data():
    return DEFAULT_HEALTH_DATA

# Vital signs endpoint
@app.post("/vital-signs")
async def process_vital_signs(request: VitalSignsRequest):
    user_id = request.user_id
    vital_signs = request.vital_signs

    # Log the incoming data for debugging
    print(f"Received vital signs data from user {user_id}: {json.dumps(vital_signs, indent=2)}")

    # Process vital signs
    try:
        # Convert vital signs to the format expected by the monitoring tool
        # Handle different possible formats of the vital signs data
        formatted_data = {}

        # Map the keys from the client to the keys expected by the monitoring tool
        key_mapping = {
            "Glucose": ["Glucose"],
            "SpO2": ["SpO2"],
            "ECG (Heart Rate)": ["ECG (Heart Rate)", "Heart_Rate"],
            "Blood Pressure (Systolic)": ["Blood Pressure (Systolic)", "Blood_Pressure_Systolic"],
            "Blood Pressure (Diastolic)": ["Blood Pressure (Diastolic)", "Blood_Pressure_Diastolic"],
            "Temperature": ["Temperature"]
        }

        # Try to find the values using different possible keys
        for target_key, possible_keys in key_mapping.items():
            for key in possible_keys:
                if key in vital_signs and vital_signs[key] is not None:
                    formatted_data[target_key] = vital_signs[key]
                    break

        # Print the formatted data for debugging
        print(f"Formatted data: {json.dumps(formatted_data, indent=2)}")

        # Invoke the vital sign monitoring tool
        health_data_json = json.dumps({"data": formatted_data})
        result = vital_sign_monitoring_tool.invoke(health_data_json)

        # Generate alerts and recommendations
        alerts = detect_abnormal_patterns(formatted_data)
        recommendations = recommend_lifestyle_changes(formatted_data)

        # Store the vital signs data in the user's chat history for the agent to use
        if user_id not in chat_histories:
            chat_histories[user_id] = []

        # Add a system message with the vital signs data
        vital_signs_message = {"role": "system", "content": f"User's vital signs: {json.dumps(formatted_data)}"}
        chat_histories[user_id].append(vital_signs_message)

        # Add a message with the analysis
        analysis = result if isinstance(result, str) else result.get("message", "")
        analysis_message = {"role": "assistant", "content": f"Vital Signs Analysis:\n{analysis}"}
        chat_histories[user_id].append(analysis_message)

        return {
            "analysis": analysis,
            "alerts": alerts,
            "recommendations": recommendations
        }
    except Exception as e:
        return {"error": f"Error processing vital signs: {str(e)}"}

# Health score endpoint
@app.post("/health-score")
async def process_health_score(request: HealthScoreRequest):
    user_id = request.user_id
    health_data = request.health_data

    # Log the incoming data for debugging
    print(f"Received health score data from user {user_id}: {json.dumps(health_data, indent=2)}")

    # Process health score
    try:
        # Generate health score report
        report = health_score_tool.generate_report(health_data)

        total_score = report.get('Total Score', 0)
        status = report.get('Health Status', 'Unknown')
        vitals_needing_improvement = report.get('Vitals Needing Improvement', 'N/A')
        improvement_tips = report.get('Improvement Tips', 'N/A')

        # Create personalized recommendations
        personalized_recommendations = ""
        if "Glucose" in vitals_needing_improvement:
            personalized_recommendations += "• I notice your glucose levels need attention. Small changes like reducing sugary drinks and adding more fiber to your diet can make a big difference. Even a 10-minute walk after meals can help regulate your blood sugar.\n\n"
        if "SpO2" in vitals_needing_improvement:
            personalized_recommendations += "• Your oxygen saturation levels could be improved. Simple breathing exercises for 5 minutes daily can help, and ensuring your sleeping position allows for optimal breathing is important for you.\n\n"
        if "ECG" in vitals_needing_improvement or "Heart Rate" in vitals_needing_improvement:
            personalized_recommendations += "• Your heart rate patterns suggest we should focus on heart health. For your specific situation, gentle cardio exercises like walking or swimming would be beneficial, starting with just 15 minutes daily.\n\n"
        if "Blood Pressure" in vitals_needing_improvement:
            personalized_recommendations += "• Your blood pressure readings indicate this is an area for improvement. Based on your profile, reducing sodium intake and practicing specific relaxation techniques like deep breathing for 5 minutes twice daily could be particularly effective for you.\n\n"

        # Format the analysis
        analysis = f"Your health score is {total_score}%. Your health status is {status}.\n\n"
        analysis += f"Areas needing attention: {vitals_needing_improvement}\n\n"
        analysis += f"Personalized recommendations:\n{personalized_recommendations if personalized_recommendations else improvement_tips}"

        # Store the health data in the user's chat history for the agent to use
        if user_id not in chat_histories:
            chat_histories[user_id] = []

        # Add a system message with the health data
        health_data_message = {"role": "system", "content": f"User's health data: {json.dumps(health_data)}"}
        chat_histories[user_id].append(health_data_message)

        # Add a message with the analysis
        analysis_message = {"role": "assistant", "content": f"Health Score Analysis:\n{analysis}"}
        chat_histories[user_id].append(analysis_message)

        return {
            "analysis": analysis,
            "score": total_score,
            "status": status,
            "areas_needing_improvement": vitals_needing_improvement,
            "recommendations": personalized_recommendations if personalized_recommendations else improvement_tips
        }
    except Exception as e:
        return {"error": f"Error processing health score: {str(e)}"}

# Kidney function endpoint
@app.post("/kidney-function")
async def process_kidney_function(request: KidneyFunctionRequest):
    user_id = request.user_id
    kidney_data = request.kidney_data

    # Log the incoming data for debugging
    print(f"Received kidney function data from user {user_id}: {json.dumps(kidney_data, indent=2)}")

    # Process kidney function
    try:
        # Run kidney function analysis
        result = kidney_function_analysis_tool(kidney_data)

        # Format the analysis (for debugging purposes)
        # print("\n".join(result.get("analysis", [])))

        # Add personalized recommendations based on abnormal values
        abnormal_params = []
        for analysis_item in result.get("analysis", []):
            if "High" in analysis_item or "Low" in analysis_item:
                param = analysis_item.split(":")[0].strip()
                abnormal_params.append(param)

        personalized_recommendations = ""
        if abnormal_params:
            if "eGFR" in abnormal_params:
                personalized_recommendations += "• Looking at your eGFR value, I recommend adjusting your protein intake to about 0.8g per kg of your body weight daily. For someone with your profile, this means focusing on plant proteins like lentils and beans more than animal proteins. Also, try to reduce your salt intake to help manage blood pressure, which directly impacts kidney function.\n\n"
            if "Serum Creatinine" in abnormal_params:
                personalized_recommendations += "• Your Serum Creatinine level suggests we should make some dietary adjustments. Specifically for you, I'd recommend avoiding creatine supplements completely and limiting red meat to once or twice a week. Staying well-hydrated is particularly important with your values - aim for about 2-3 liters of water daily, spread throughout the day.\n\n"
            if "BUN" in abnormal_params or "Serum Urea" in abnormal_params:
                personalized_recommendations += "• With your elevated BUN/Urea levels, I recommend reducing your protein intake, particularly animal proteins. For your specific situation, focusing on quality proteins rather than quantity would be beneficial. Also, ensure you're drinking enough water throughout the day to help your kidneys filter waste more efficiently.\n\n"
            if "Serum Sodium" in abnormal_params:
                personalized_recommendations += "• Your Sodium levels indicate we should pay attention to your fluid and salt balance. Based on your specific values, I recommend discussing with your doctor about the right amount of fluid intake for you. In the meantime, try using herbs and spices instead of salt to flavor your food.\n\n"
            if "Serum Potassium" in abnormal_params:
                personalized_recommendations += "• Your Potassium level requires attention. This is important to address, as potassium directly affects heart function. For your specific situation, I recommend discussing with your doctor about dietary adjustments and possible medication review. In the meantime, be cautious with high-potassium foods like bananas, oranges, and potatoes.\n\n"
            if "ACR" in abnormal_params:
                personalized_recommendations += "• Your ACR (albumin-to-creatinine ratio) indicates some protein in your urine. For your specific case, controlling blood pressure and blood sugar levels is crucial. I recommend monitoring your blood pressure regularly and reducing salt intake to less than 5g per day.\n\n"
        else:
            personalized_recommendations += "Your kidney function parameters appear to be within normal ranges. To maintain kidney health:\n\n"
            personalized_recommendations += "• Stay well-hydrated\n"
            personalized_recommendations += "• Maintain a balanced diet low in sodium\n"
            personalized_recommendations += "• Exercise regularly\n"
            personalized_recommendations += "• Avoid smoking and limit alcohol consumption\n"
            personalized_recommendations += "• Control blood pressure and blood sugar levels\n\n"

        # Add the personalized recommendations to the result
        result["personalized_recommendations"] = personalized_recommendations

        # Store the kidney data in the user's chat history for the agent to use
        if user_id not in chat_histories:
            chat_histories[user_id] = []

        # Add a system message with the kidney data
        kidney_data_message = {"role": "system", "content": f"User's kidney function data: {json.dumps(kidney_data)}"}
        chat_histories[user_id].append(kidney_data_message)

        # Add a message with the analysis
        analysis_message = {"role": "assistant", "content": f"Kidney Function Analysis:\n{result.get('analysis', [])}\n\nFindings: {result.get('overall_health', 'Unknown')}\n\nPersonalized Recommendations:\n{personalized_recommendations}"}
        chat_histories[user_id].append(analysis_message)

        return result
    except Exception as e:
        return {"error": f"Error analyzing kidney function: {str(e)}"}

# === QUERY HANDLER ===
@app.post("/query")
async def get_response(chat: ChatRequest):
    user_id, query, selected_model, mode = chat.user_id, chat.query, chat.model, chat.mode

    if selected_model not in [QWEN_MODEL, DEEPSEEK_MODEL]:
        return {"error": "Invalid model selection. Choose either QWEN or DeepSeek."}

    # Prepare system roles based on the selected model
    system_roles = {
        QWEN_MODEL: "You are Dr. Deuce, a real medical assistant who directly books appointments. You give concise and accurate healthcare recommendations.",
        DEEPSEEK_MODEL: "You are Dr. Deuce, a real medical assistant who directly books appointments. You give concise and accurate healthcare recommendations."
    }

    context = system_roles[selected_model]
    relevant_info = await retrieve_context(query, selected_model, TOP_K)
    full_context = f"{context} {relevant_info}"

    # Ensure user exists in chat histories
    if user_id not in chat_histories:
        chat_histories[user_id] = [{"role": "system", "content": full_context}]
    else:
        # Update system message with current context
        if chat_histories[user_id] and chat_histories[user_id][0]["role"] == "system":
            chat_histories[user_id][0]["content"] = full_context
        else:
            chat_histories[user_id].insert(0, {"role": "system", "content": full_context})

    chat_histories[user_id].append({"role": "user", "content": query})

    if len(chat_histories[user_id]) > MAX_HISTORY_LENGTH:
        # Keep the system message and trim the rest
        system_message = chat_histories[user_id][0] if chat_histories[user_id] and chat_histories[user_id][0]["role"] == "system" else None
        chat_histories[user_id] = chat_histories[user_id][-(MAX_HISTORY_LENGTH-1):]
        if system_message:
            chat_histories[user_id].insert(0, system_message)

    # Call LangGraph agent for health-related response
    agent_response = await generate_agent_response(
        user_id=user_id, query=query, model_name=selected_model, mode=mode
    )

    # Append the assistant response to chat history
    chat_histories[user_id].append({"role": "assistant", "content": agent_response["response"]})

    return {
        "response": agent_response["response"],
        "tools_used": agent_response["tools_used"],
        "health_data": agent_response["health_data"],
        "chat_history": chat_histories[user_id]
    }

# === RUN SERVER ===
if __name__ == "__main__":
    # Start the FastAPI server
    uvicorn.run(app, host="0.0.0.0", port=8000)
