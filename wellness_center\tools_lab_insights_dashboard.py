"""
Lab Insights Dashboard Tool

A tool for generating comprehensive lab insights and analytics for healthcare administrators.
Provides analytics on test requests, turnaround times, disease indicators, and geographic distribution.
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import Counter, defaultdict
from langchain.tools import Tool

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LabInsightsDashboardTool:
    """
    Lab Insights Dashboard Tool for monitoring lab usage and health trends
    
    This tool provides comprehensive analytics for healthcare facilities including:
    - Test volume analysis
    - Turnaround time monitoring
    - Disease trend detection
    - Geographic distribution analysis
    - AI-generated insights and recommendations
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.logger.info("🔬 Lab Insights Dashboard Tool initialized")
        
    def generate_dashboard(self, user_health_data: Dict, admin_id: str,
                          date_range_days: int = 30,
                          test_type_filter: Optional[str] = None,
                          user_group_filter: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate comprehensive Lab Insights Dashboard

        Args:
            user_health_data: Dictionary containing all user health data
            admin_id: ID of the admin requesting the dashboard
            date_range_days: Number of days to analyze (default: 30)
            test_type_filter: Optional filter for specific test types
            user_group_filter: Optional filter for specific user groups

        Returns:
            Dictionary containing complete dashboard analytics
        """
        try:
            self.logger.info(f"🔬 Generating Lab Insights Dashboard for admin: {admin_id}")
            self.logger.info(f"📊 Analysis parameters - Days: {date_range_days}, Test filter: {test_type_filter}")
            
            cutoff_date = datetime.now() - timedelta(days=date_range_days)
            
            # Initialize analytics containers
            test_requests = Counter()
            test_turnaround_times = defaultdict(list)
            disease_indicators = Counter()
            geographic_distribution = Counter()
            daily_test_counts = defaultdict(int)
            
            # Process user health data
            total_users_analyzed = 0
            for user_key, user_data in user_health_data.items():
                user_id = user_key if isinstance(user_key, str) else f"{user_key[0]}_{user_key[1]}"
                location = self._extract_location(user_id)
                
                # Process different types of health data
                processed = self._process_health_records(
                    user_data, cutoff_date, test_requests, 
                    test_turnaround_times, disease_indicators,
                    geographic_distribution, location, 
                    test_type_filter, user_group_filter, daily_test_counts
                )
                
                if processed:
                    total_users_analyzed += 1
            
            # Generate comprehensive analytics
            dashboard_data = self._compile_dashboard_data(
                test_requests, test_turnaround_times, disease_indicators,
                geographic_distribution, daily_test_counts, total_users_analyzed,
                date_range_days, test_type_filter, user_group_filter, admin_id
            )
            
            self.logger.info(f"✅ Dashboard generated successfully for {total_users_analyzed} users")
            return dashboard_data

        except Exception as e:
            error_msg = f"Error generating lab insights dashboard: {str(e)}"
            self.logger.error(error_msg)
            return {"error": error_msg, "success": False}
    
    def _process_health_records(self, user_data: Any, cutoff_date: datetime,
                               test_requests: Counter, test_turnaround_times: defaultdict,
                               disease_indicators: Counter, geographic_distribution: Counter,
                               location: str, test_type_filter: Optional[str],
                               user_group_filter: Optional[str], daily_test_counts: defaultdict) -> bool:
        """Process individual user health records"""
        
        processed_any = False
        
        # Handle different data structures
        if isinstance(user_data, dict):
            # Process different types of stored data
            for data_type, data_content in user_data.items():
                if data_type in ['health_score', 'test_results', 'kidney_function', 
                               'lipid_profile', 'lung_capacity', 'liver_function']:
                    if self._process_test_data(data_type, data_content, cutoff_date,
                                             test_requests, test_turnaround_times,
                                             disease_indicators, geographic_distribution,
                                             location, test_type_filter, daily_test_counts):
                        processed_any = True
        
        elif isinstance(user_data, list):
            # Process list of health records (vital signs tracking)
            for record in user_data:
                if isinstance(record, dict) and 'timestamp' in record:
                    try:
                        record_date = datetime.fromisoformat(record['timestamp'].replace('Z', '+00:00').replace('+00:00', ''))
                        if record_date >= cutoff_date:
                            self._process_vital_signs_record(record, test_requests, 
                                                           disease_indicators, 
                                                           geographic_distribution, location,
                                                           daily_test_counts, record_date)
                            processed_any = True
                    except Exception as e:
                        self.logger.warning(f"Error processing vital signs record: {str(e)}")
        
        return processed_any
    
    def _process_test_data(self, test_type: str, data_content: Dict, cutoff_date: datetime,
                          test_requests: Counter, test_turnaround_times: defaultdict,
                          disease_indicators: Counter, geographic_distribution: Counter,
                          location: str, test_type_filter: Optional[str],
                          daily_test_counts: defaultdict) -> bool:
        """Process specific test data"""
        
        if not isinstance(data_content, dict) or 'timestamp' not in data_content:
            return False
            
        try:
            test_date = datetime.fromisoformat(data_content['timestamp'].replace('Z', '+00:00').replace('+00:00', ''))
            if test_date < cutoff_date:
                return False
                
            # Apply test type filter
            if test_type_filter and test_type != test_type_filter:
                return False
                
            # Count test request
            test_requests[test_type] += 1
            geographic_distribution[location] += 1
            
            # Track daily counts
            date_key = test_date.strftime('%Y-%m-%d')
            daily_test_counts[date_key] += 1
            
            # Simulate turnaround time
            turnaround_hours = self._simulate_turnaround_time(test_type)
            test_turnaround_times[test_type].append(turnaround_hours)
            
            # Analyze for disease indicators
            if 'data' in data_content:
                self._extract_disease_indicators(data_content['data'], disease_indicators)
            
            return True
                
        except Exception as e:
            self.logger.warning(f"Error processing test data for {test_type}: {str(e)}")
            return False
    
    def _process_vital_signs_record(self, record: Dict, test_requests: Counter,
                                   disease_indicators: Counter, 
                                   geographic_distribution: Counter, location: str,
                                   daily_test_counts: defaultdict, record_date: datetime) -> None:
        """Process vital signs records"""
        test_requests['vital_signs_monitoring'] += 1
        geographic_distribution[location] += 1
        
        # Track daily counts
        date_key = record_date.strftime('%Y-%m-%d')
        daily_test_counts[date_key] += 1
        
        # Check for concerning vital signs patterns
        if 'Blood Pressure (Systolic)' in record and record['Blood Pressure (Systolic)']:
            try:
                if float(record['Blood Pressure (Systolic)']) > 140:
                    disease_indicators['hypertension_risk'] += 1
            except (ValueError, TypeError):
                pass
                
        if 'Glucose' in record and record['Glucose']:
            try:
                if float(record['Glucose']) > 126:
                    disease_indicators['diabetes_risk'] += 1
            except (ValueError, TypeError):
                pass
    
    def _extract_disease_indicators(self, test_data: Dict, disease_indicators: Counter) -> None:
        """Extract disease indicators from test data"""
        
        # Check for specific test results
        positive_tests = {
            'Malaria': 'malaria_positive',
            'Hepatitis B': 'hepatitis_b_positive', 
            'Widal Test': 'typhoid_positive'
        }
        
        for test_name, indicator_name in positive_tests.items():
            if test_name in test_data and test_data[test_name] == 'Positive':
                disease_indicators[indicator_name] += 1
        
        # Check for abnormal values
        try:
            if 'Glucose' in test_data and isinstance(test_data['Glucose'], (int, float)):
                if test_data['Glucose'] > 126:
                    disease_indicators['diabetes_risk'] += 1
                    
            if 'Blood Pressure (Systolic)' in test_data and isinstance(test_data['Blood Pressure (Systolic)'], (int, float)):
                if test_data['Blood Pressure (Systolic)'] > 140:
                    disease_indicators['hypertension_risk'] += 1
        except (ValueError, TypeError):
            pass
    
    def _extract_location(self, user_id: str) -> str:
        """Extract location from user ID"""
        user_id_lower = user_id.lower()
        location_mapping = {
            'lagos': 'Lagos',
            'abuja': 'Abuja', 
            'kano': 'Kano',
            'ibadan': 'Ibadan',
            'port': 'Port Harcourt',
            'kaduna': 'Kaduna'
        }
        
        for key, location in location_mapping.items():
            if key in user_id_lower:
                return location
        return 'Unknown'
    
    def _simulate_turnaround_time(self, test_type: str) -> float:
        """Simulate realistic turnaround times"""
        turnaround_map = {
            'health_score': 0.5,
            'vital_signs_monitoring': 0.25,
            'test_results': 24.0,
            'kidney_function': 4.0,
            'lipid_profile': 6.0,
            'lung_capacity': 1.0,
            'liver_function': 8.0,
        }
        return turnaround_map.get(test_type, 2.0)

    def _compile_dashboard_data(self, test_requests: Counter, test_turnaround_times: defaultdict,
                               disease_indicators: Counter, geographic_distribution: Counter,
                               daily_test_counts: defaultdict, total_users_analyzed: int,
                               date_range_days: int, test_type_filter: Optional[str],
                               user_group_filter: Optional[str], admin_id: str) -> Dict[str, Any]:
        """Compile all analytics into dashboard data"""

        # Calculate metrics
        total_requests = sum(test_requests.values())
        avg_turnaround = self._calculate_average_turnaround(test_turnaround_times)
        top_tests = self._get_top_requested_tests(test_requests, 10)
        disease_trends = self._analyze_disease_trends(disease_indicators)
        geo_distribution = self._analyze_geographic_distribution(geographic_distribution)
        insights = self._generate_insights(test_requests, disease_indicators, avg_turnaround, total_requests)

        # Compile dashboard
        dashboard_data = {
            "success": True,
            "dashboard_generated": datetime.now().isoformat(),
            "requested_by": admin_id,
            "analysis_period_days": date_range_days,
            "filters_applied": {
                "test_type": test_type_filter,
                "user_group": user_group_filter
            },
            "summary_statistics": {
                "total_test_requests": total_requests,
                "unique_tests_requested": len(test_requests),
                "active_users_analyzed": total_users_analyzed,
                "average_turnaround_hours": avg_turnaround,
                "daily_average_tests": round(total_requests / max(date_range_days, 1), 2)
            },
            "top_requested_tests": top_tests,
            "disease_trend_indicators": disease_trends,
            "geographic_distribution": geo_distribution,
            "insights_and_recommendations": insights,
            "wellness_center_metadata": {
                "version": "1.0.0",
                "generated_by": "Wellness Center Lab Insights Dashboard Tool",
                "data_quality_score": self._calculate_data_quality_score(total_requests, total_users_analyzed)
            }
        }

        return dashboard_data

    def _get_top_requested_tests(self, test_requests: Counter, limit: int = 10) -> List[Dict]:
        """Get top requested tests with detailed metrics"""
        total_requests = sum(test_requests.values())
        if total_requests == 0:
            return []

        return [
            {
                "test_name": test_name,
                "request_count": count,
                "percentage_of_total": round((count / total_requests) * 100, 2),
                "rank": i + 1
            }
            for i, (test_name, count) in enumerate(test_requests.most_common(limit))
        ]

    def _calculate_average_turnaround(self, test_turnaround_times: defaultdict) -> float:
        """Calculate overall average turnaround time"""
        all_times = []
        for times_list in test_turnaround_times.values():
            all_times.extend(times_list)
        return round(sum(all_times) / len(all_times), 2) if all_times else 0.0

    def _analyze_disease_trends(self, disease_indicators: Counter) -> List[Dict]:
        """Analyze disease trend indicators with risk assessment"""
        total_indicators = sum(disease_indicators.values())
        if total_indicators == 0:
            return []

        return [
            {
                "condition": condition,
                "case_count": count,
                "percentage": round((count / total_indicators) * 100, 2),
                "trend_status": "High Alert" if count > 10 else "Monitoring" if count > 5 else "Normal",
                "risk_level": "High" if count > 10 else "Medium" if count > 5 else "Low"
            }
            for condition, count in disease_indicators.most_common()
        ]

    def _analyze_geographic_distribution(self, geographic_distribution: Counter) -> List[Dict]:
        """Analyze geographic distribution with insights"""
        total_tests = sum(geographic_distribution.values())
        if total_tests == 0:
            return []

        return [
            {
                "location": location,
                "test_count": count,
                "percentage_of_total": round((count / total_tests) * 100, 2),
                "status": "High Volume" if count > total_tests * 0.4 else "Moderate" if count > total_tests * 0.2 else "Low Volume"
            }
            for location, count in geographic_distribution.most_common()
        ]

    def _generate_insights(self, test_requests: Counter, disease_indicators: Counter,
                          avg_turnaround: float, total_requests: int) -> List[str]:
        """Generate actionable insights and recommendations"""
        insights = []

        # Volume insights
        if total_requests > 100:
            insights.append(f"🔥 High test volume detected ({total_requests} requests) - consider scaling lab capacity")
        elif total_requests < 10:
            insights.append("📉 Low test volume - may indicate underutilization or data collection issues")

        # Efficiency insights
        if avg_turnaround > 12:
            insights.append(f"⏰ Average turnaround time ({avg_turnaround}h) exceeds 12 hours - review lab efficiency")
        elif avg_turnaround < 2:
            insights.append(f"⚡ Excellent turnaround times ({avg_turnaround}h) - lab operating efficiently")

        # Disease trend insights
        total_disease_indicators = sum(disease_indicators.values())
        if total_disease_indicators > 20:
            insights.append("🚨 Elevated disease indicators detected - consider public health interventions")

        # Test pattern insights
        if test_requests:
            most_common_test = test_requests.most_common(1)[0]
            insights.append(f"📊 Most requested test: {most_common_test[0]} ({most_common_test[1]} requests)")

            if len(test_requests) == 1:
                insights.append("⚠️ Limited test diversity - consider expanding test offerings")

        return insights

    def _calculate_data_quality_score(self, total_requests: int, total_users: int) -> float:
        """Calculate a data quality score based on available metrics"""
        if total_users == 0:
            return 0.0

        # Simple quality score based on data completeness
        requests_per_user = total_requests / total_users

        if requests_per_user >= 3:
            return 95.0
        elif requests_per_user >= 2:
            return 85.0
        elif requests_per_user >= 1:
            return 75.0
        else:
            return 60.0

# Create dashboard tool instance
lab_insights_dashboard_tool = LabInsightsDashboardTool()

def generate_lab_insights_dashboard(query: str) -> str:
    """
    Generate lab insights dashboard based on query parameters

    Expected query format: JSON string with parameters:
    {
        "user_health_data": {...},
        "admin_id": "admin_001",
        "date_range_days": 30,
        "test_type_filter": null,
        "user_group_filter": null
    }

    Args:
        query: JSON string containing dashboard parameters

    Returns:
        JSON string containing dashboard analytics
    """
    try:
        # Parse the query parameters
        params = json.loads(query)

        user_health_data = params.get("user_health_data", {})
        admin_id = params.get("admin_id", "unknown_admin")
        date_range_days = params.get("date_range_days", 30)
        test_type_filter = params.get("test_type_filter")
        user_group_filter = params.get("user_group_filter")

        # Generate dashboard
        result = lab_insights_dashboard_tool.generate_dashboard(
            user_health_data=user_health_data,
            admin_id=admin_id,
            date_range_days=date_range_days,
            test_type_filter=test_type_filter,
            user_group_filter=user_group_filter
        )

        return result

    except json.JSONDecodeError as e:
        error_result = {
            "error": f"Invalid JSON query format: {str(e)}",
            "success": False
        }
        return json.dumps(error_result)
    except Exception as e:
        error_result = {
            "error": f"Error generating lab insights dashboard: {str(e)}",
            "success": False
        }
        return json.dumps(error_result)

# Create LangChain Tool wrapper
lab_insights_dashboard_langchain_tool = Tool(
    name="LabInsightsDashboard",
    func=generate_lab_insights_dashboard,
    description="""
    Generate comprehensive lab insights dashboard for healthcare administrators.

    Input should be a JSON string with the following structure:
    {
        "user_health_data": {...},  // Dictionary containing all user health data
        "admin_id": "admin_001",    // ID of the admin requesting the dashboard
        "date_range_days": 30,      // Number of days to analyze (default: 30)
        "test_type_filter": null,   // Optional filter for specific test types
        "user_group_filter": null   // Optional filter for specific user groups
    }

    Returns comprehensive analytics including:
    - Top 10 most requested tests
    - Average turnaround time
    - Disease trend indicators
    - Geographic test distribution
    - AI-generated insights and recommendations
    """
)
