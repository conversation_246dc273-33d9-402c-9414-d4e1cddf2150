"""
Lab Insights Dashboard Tool

A centralized dashboard for admins or doctors to monitor lab usage and health trends.
Provides analytics on test requests, turnaround times, disease indicators, and geographic distribution.
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from collections import Counter, defaultdict
from dataclasses import dataclass
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TestAnalytics:
    """Data class for test analytics"""
    test_name: str
    request_count: int
    avg_turnaround_hours: float
    trend_percentage: float  # Positive for increase, negative for decrease

@dataclass
class GeographicData:
    """Data class for geographic test distribution"""
    location: str
    test_count: int
    most_common_test: str

class LabInsightsDashboard:
    """Lab Insights Dashboard for monitoring lab usage and health trends"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def analyze_lab_usage(self, user_health_data: Dict, date_range_days: int = 30, 
                         test_type_filter: Optional[str] = None,
                         user_group_filter: Optional[str] = None) -> Dict[str, Any]:
        """
        Analyze lab usage patterns and generate insights dashboard
        
        Args:
            user_health_data: Dictionary containing all user health data
            date_range_days: Number of days to analyze (default: 30)
            test_type_filter: Optional filter for specific test types
            user_group_filter: Optional filter for specific user groups
            
        Returns:
            Dictionary containing dashboard analytics
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=date_range_days)
            
            # Initialize analytics containers
            test_requests = Counter()
            test_turnaround_times = defaultdict(list)
            disease_indicators = Counter()
            geographic_distribution = Counter()
            user_demographics = defaultdict(int)
            
            # Process user health data
            for user_key, user_data in user_health_data.items():
                user_id = user_key if isinstance(user_key, str) else f"{user_key[0]}_{user_key[1]}"
                
                # Extract location from user_id or default to "Unknown"
                location = self._extract_location(user_id)
                
                # Process different types of health data
                self._process_health_records(user_data, cutoff_date, test_requests, 
                                           test_turnaround_times, disease_indicators,
                                           geographic_distribution, location, 
                                           test_type_filter, user_group_filter)
            
            # Generate analytics
            top_tests = self._get_top_requested_tests(test_requests, 10)
            avg_turnaround = self._calculate_average_turnaround(test_turnaround_times)
            disease_trends = self._analyze_disease_trends(disease_indicators)
            geo_distribution = self._analyze_geographic_distribution(geographic_distribution, test_requests)
            
            # Create dashboard data
            dashboard_data = {
                "dashboard_generated": datetime.now().isoformat(),
                "analysis_period_days": date_range_days,
                "filters_applied": {
                    "test_type": test_type_filter,
                    "user_group": user_group_filter
                },
                "summary_statistics": {
                    "total_test_requests": sum(test_requests.values()),
                    "unique_tests_requested": len(test_requests),
                    "active_users": len(user_health_data),
                    "average_turnaround_hours": avg_turnaround
                },
                "top_requested_tests": top_tests,
                "disease_trend_indicators": disease_trends,
                "geographic_distribution": geo_distribution,
                "insights_and_recommendations": self._generate_insights(
                    test_requests, disease_indicators, avg_turnaround
                )
            }
            
            self.logger.info(f"Lab insights dashboard generated for {date_range_days} days")
            return dashboard_data
            
        except Exception as e:
            error_msg = f"Error generating lab insights dashboard: {str(e)}"
            self.logger.error(error_msg)
            return {"error": error_msg}
    
    def _process_health_records(self, user_data: Any, cutoff_date: datetime,
                               test_requests: Counter, test_turnaround_times: defaultdict,
                               disease_indicators: Counter, geographic_distribution: Counter,
                               location: str, test_type_filter: Optional[str],
                               user_group_filter: Optional[str]) -> None:
        """Process individual user health records"""
        
        # Handle different data structures
        if isinstance(user_data, dict):
            # Process different types of stored data
            for data_type, data_content in user_data.items():
                if data_type in ['health_score', 'test_results', 'kidney_function', 
                               'lipid_profile', 'lung_capacity', 'liver_function']:
                    self._process_test_data(data_type, data_content, cutoff_date,
                                          test_requests, test_turnaround_times,
                                          disease_indicators, geographic_distribution,
                                          location, test_type_filter)
        
        elif isinstance(user_data, list):
            # Process list of health records (vital signs tracking)
            for record in user_data:
                if isinstance(record, dict) and 'timestamp' in record:
                    record_date = datetime.fromisoformat(record['timestamp'].replace('Z', '+00:00').replace('+00:00', ''))
                    if record_date >= cutoff_date:
                        self._process_vital_signs_record(record, test_requests, 
                                                       disease_indicators, 
                                                       geographic_distribution, location)
    
    def _process_test_data(self, test_type: str, data_content: Dict, cutoff_date: datetime,
                          test_requests: Counter, test_turnaround_times: defaultdict,
                          disease_indicators: Counter, geographic_distribution: Counter,
                          location: str, test_type_filter: Optional[str]) -> None:
        """Process specific test data"""
        
        if not isinstance(data_content, dict) or 'timestamp' not in data_content:
            return
            
        try:
            test_date = datetime.fromisoformat(data_content['timestamp'].replace('Z', '+00:00').replace('+00:00', ''))
            if test_date < cutoff_date:
                return
                
            # Apply test type filter
            if test_type_filter and test_type != test_type_filter:
                return
                
            # Count test request
            test_requests[test_type] += 1
            geographic_distribution[location] += 1
            
            # Simulate turnaround time (in real implementation, this would be actual data)
            turnaround_hours = self._simulate_turnaround_time(test_type)
            test_turnaround_times[test_type].append(turnaround_hours)
            
            # Analyze for disease indicators
            if 'data' in data_content:
                self._extract_disease_indicators(data_content['data'], disease_indicators)
                
        except Exception as e:
            self.logger.warning(f"Error processing test data for {test_type}: {str(e)}")
    
    def _process_vital_signs_record(self, record: Dict, test_requests: Counter,
                                   disease_indicators: Counter, 
                                   geographic_distribution: Counter, location: str) -> None:
        """Process vital signs records"""
        test_requests['vital_signs_monitoring'] += 1
        geographic_distribution[location] += 1
        
        # Check for concerning vital signs patterns
        if 'Blood Pressure (Systolic)' in record and record['Blood Pressure (Systolic)']:
            if float(record['Blood Pressure (Systolic)']) > 140:
                disease_indicators['hypertension_risk'] += 1
                
        if 'Glucose' in record and record['Glucose']:
            if float(record['Glucose']) > 126:
                disease_indicators['diabetes_risk'] += 1
    
    def _extract_disease_indicators(self, test_data: Dict, disease_indicators: Counter) -> None:
        """Extract disease indicators from test data"""
        
        # Check for specific test results that indicate disease trends
        if 'Malaria' in test_data and test_data['Malaria'] == 'Positive':
            disease_indicators['malaria_positive'] += 1
            
        if 'Hepatitis B' in test_data and test_data['Hepatitis B'] == 'Positive':
            disease_indicators['hepatitis_b_positive'] += 1
            
        if 'Widal Test' in test_data and test_data['Widal Test'] == 'Positive':
            disease_indicators['typhoid_positive'] += 1
            
        # Check for abnormal values
        if 'Glucose' in test_data and isinstance(test_data['Glucose'], (int, float)):
            if test_data['Glucose'] > 126:
                disease_indicators['diabetes_risk'] += 1
                
        if 'Blood Pressure (Systolic)' in test_data and isinstance(test_data['Blood Pressure (Systolic)'], (int, float)):
            if test_data['Blood Pressure (Systolic)'] > 140:
                disease_indicators['hypertension_risk'] += 1
    
    def _extract_location(self, user_id: str) -> str:
        """Extract location from user ID or return default"""
        # In a real implementation, this would map user IDs to actual locations
        # For now, we'll simulate based on user ID patterns
        if 'lagos' in user_id.lower():
            return 'Lagos'
        elif 'abuja' in user_id.lower():
            return 'Abuja'
        elif 'kano' in user_id.lower():
            return 'Kano'
        else:
            return 'Unknown'
    
    def _simulate_turnaround_time(self, test_type: str) -> float:
        """Simulate turnaround time for different test types"""
        # In real implementation, this would be actual recorded turnaround times
        turnaround_map = {
            'health_score': 0.5,  # 30 minutes
            'vital_signs_monitoring': 0.25,  # 15 minutes
            'test_results': 24.0,  # 24 hours
            'kidney_function': 4.0,  # 4 hours
            'lipid_profile': 6.0,  # 6 hours
            'lung_capacity': 1.0,  # 1 hour
            'liver_function': 8.0,  # 8 hours
        }
        return turnaround_map.get(test_type, 2.0)  # Default 2 hours
    
    def _get_top_requested_tests(self, test_requests: Counter, limit: int = 10) -> List[Dict]:
        """Get top requested tests"""
        return [
            {
                "test_name": test_name,
                "request_count": count,
                "percentage_of_total": round((count / sum(test_requests.values())) * 100, 2)
            }
            for test_name, count in test_requests.most_common(limit)
        ]
    
    def _calculate_average_turnaround(self, test_turnaround_times: defaultdict) -> float:
        """Calculate overall average turnaround time"""
        all_times = []
        for times_list in test_turnaround_times.values():
            all_times.extend(times_list)
        return round(sum(all_times) / len(all_times), 2) if all_times else 0.0
    
    def _analyze_disease_trends(self, disease_indicators: Counter) -> List[Dict]:
        """Analyze disease trend indicators"""
        total_indicators = sum(disease_indicators.values())
        if total_indicators == 0:
            return []
            
        return [
            {
                "condition": condition,
                "case_count": count,
                "percentage": round((count / total_indicators) * 100, 2),
                "trend_status": "Monitoring" if count > 5 else "Normal"
            }
            for condition, count in disease_indicators.most_common()
        ]
    
    def _analyze_geographic_distribution(self, geographic_distribution: Counter, 
                                       test_requests: Counter) -> List[Dict]:
        """Analyze geographic distribution of tests"""
        return [
            {
                "location": location,
                "test_count": count,
                "percentage_of_total": round((count / sum(geographic_distribution.values())) * 100, 2)
            }
            for location, count in geographic_distribution.most_common()
        ]
    
    def _generate_insights(self, test_requests: Counter, disease_indicators: Counter,
                          avg_turnaround: float) -> List[str]:
        """Generate insights and recommendations"""
        insights = []
        
        # Test volume insights
        if sum(test_requests.values()) > 100:
            insights.append("High test volume detected - consider scaling lab capacity")
        
        # Turnaround time insights
        if avg_turnaround > 12:
            insights.append("Average turnaround time exceeds 12 hours - review lab efficiency")
        elif avg_turnaround < 2:
            insights.append("Excellent turnaround times - lab operating efficiently")
        
        # Disease trend insights
        total_disease_indicators = sum(disease_indicators.values())
        if total_disease_indicators > 20:
            insights.append("Elevated disease indicators - consider public health interventions")
        
        # Most common test insights
        if test_requests:
            most_common_test = test_requests.most_common(1)[0]
            insights.append(f"Most requested test: {most_common_test[0]} ({most_common_test[1]} requests)")
        
        return insights

# Create tool instance
lab_insights_dashboard_tool = LabInsightsDashboard()

def generate_lab_insights_dashboard(user_health_data: Dict, date_range_days: int = 30,
                                   test_type_filter: Optional[str] = None,
                                   user_group_filter: Optional[str] = None) -> str:
    """
    Generate lab insights dashboard with analytics
    
    Args:
        user_health_data: Dictionary containing all user health data
        date_range_days: Number of days to analyze
        test_type_filter: Optional filter for specific test types
        user_group_filter: Optional filter for specific user groups
        
    Returns:
        JSON string containing dashboard analytics
    """
    result = lab_insights_dashboard_tool.analyze_lab_usage(
        user_health_data, date_range_days, test_type_filter, user_group_filter
    )
    return json.dumps(result, indent=2)
