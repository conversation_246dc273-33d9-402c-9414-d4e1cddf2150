import streamlit as st
import joblib
import pandas as pd
import numpy as np

# --- Burnout/Stress Assessment Section ---

# Likert scale for responses
likert_scale = {
    1: "Never",
    2: "Rarely",
    3: "Sometimes",
    4: "Often",
    5: "Always"
}

# Question set
questions_by_category = {
    "work": [
        "I feel overwhelmed by my job responsibilities.",
        "I struggle to complete tasks due to fatigue or mental exhaustion.",
        "I get fewer than 6 hours of sleep on most workdays.",
        "I rarely take breaks or rest during the workday.",
        "I feel emotionally detached from my work.",
        "I feel recognized and valued at my workplace.",
        "I work beyond 9 hours a day on a regular basis.",
        "I experience physical symptoms such as headaches, fatigue, or insomnia due to work.",
        "I feel like I have a healthy work-life balance.",
        "I enjoy going to work or feel a sense of purpose in my job."
    ],
    "school": [
        "I often feel anxious about deadlines and academic performance.",
        "I struggle to get 7–8 hours of sleep on school nights.",
        "I study or attend schoolwork for more than 8 hours daily.",
        "I feel unable to cope with academic pressure.",
        "I rarely take breaks or engage in non-academic hobbies.",
        "I feel emotionally supported by teachers or school counselors.",
        "I compare myself negatively to other students.",
        "I have trouble focusing and retaining what I study.",
        "I feel burnout from continuous academic demands.",
        "I believe I am managing school and personal life well."
    ],
    "relationship": [
        "I often feel emotionally drained by my relationships.",
        "I find myself avoiding conversations with people close to me.",
        "I feel like my needs are not being acknowledged or understood.",
        "I frequently have conflicts or unresolved tension with loved ones.",
        "I feel pressure to constantly give more than I receive.",
        "I receive emotional support from those close to me.",
        "I often feel lonely even when I am with others.",
        "I feel stressed by trying to maintain harmony in my relationships.",
        "I find joy and peace in my close connections.",
        "I have space to express myself honestly and without judgment."
    ],
    "medical": [
        "I frequently feel tired, even after resting.",
        "My medical condition affects my mood or productivity.",
        "I worry about my health status or future frequently.",
        "I find it hard to manage medication or treatment schedules.",
        "I experience sleep difficulties due to my health issues.",
        "I feel emotionally supported by my healthcare providers.",
        "My health limits my ability to participate in daily activities.",
        "I feel frustrated or helpless about my health condition.",
        "I avoid seeking help even when my symptoms worsen.",
        "I feel in control of my health and wellness decisions."
    ]
}

def interpret_score(score: int, max_score: int) -> str:
    percentage = (score / max_score) * 100
    if percentage <= 50:
        return "🟢 Low stress/burnout"
    elif 51 <= percentage <= 70:
        return "🟡 Moderate stress/burnout"
    else:
        return "🔴 High stress/burnout – consider seeking support"

# Streamlit App
st.title("🧠 Stress and Burnout Assessment")

# Show Likert scale legend in the sidebar
st.sidebar.header("Likert Scale Reference")
for num, label in likert_scale.items():
    st.sidebar.write(f"{num}: {label}")

# Initialize session state for category_results
if "category_results" not in st.session_state:
    st.session_state.category_results = []
if "show_mental_health" not in st.session_state:
    st.session_state.show_mental_health = False
if "avg_percentage" not in st.session_state:
    st.session_state.avg_percentage = 0

# Allow user to select multiple categories
categories = st.multiselect(
    "Choose one or more categories to assess:",
    list(questions_by_category.keys())
)

if categories:
    for category in categories:
        st.subheader(f"Assessment for: {category.capitalize()}")
        responses = []
        questions = questions_by_category[category]

        for i, question in enumerate(questions, 1):
            response = st.slider(
                f"{category.capitalize()} Q{i}: {question}",
                min_value=1,
                max_value=5,
                value=3,
                key=f"{category}_{i}"
            )
            st.caption(f"Selected: {likert_scale[response]}")
            responses.append(response)

        if st.button(f"Submit {category.capitalize()} Assessment", key=f"submit_{category}"):
            total_score = sum(responses)
            max_score = len(questions) * 5
            percentage = round((total_score / max_score) * 100, 2)
            result = interpret_score(total_score, max_score)

            st.success(f"✅ {category.capitalize()} Assessment Complete!")
            st.write(f"**Total Score:** {total_score}")
            st.write(f"**Maximum Score:** {max_score}")
            st.write(f"**Percentage:** {percentage}%")
            st.write(f"**Interpretation:** {result}")

            # Store results for averaging if needed
            st.session_state.category_results.append({
                "category": category,
                "total_score": total_score,
                "max_score": max_score,
                "percentage": percentage,
                "interpretation": result
            })
            st.session_state.show_mental_health = True

    # Display average if more than one category was assessed
    if st.button("Show Overall Average (for all submitted categories)"):
        if st.session_state.category_results:
            avg_percentage = round(
                sum(r["percentage"] for r in st.session_state.category_results) / len(st.session_state.category_results), 2
            )
            st.session_state.avg_percentage = avg_percentage
            st.info(f"**Average Percentage Across {len(st.session_state.category_results)} Categories:** {avg_percentage}%")
            # Optional: Show average interpretation
            if avg_percentage <= 50:
                avg_result = "🟢 Low stress/burnout"
            elif 51 <= avg_percentage <= 70:
                avg_result = "🟡 Moderate stress/burnout"
            else:
                avg_result = "🔴 High stress/burnout – consider seeking support"
                st.session_state.show_mental_health = True
            st.write(f"**Average Interpretation:** {avg_result}")
        else:
            st.warning("Please submit at least one category assessment before viewing the average.")

# --- Mental Health Screening Section (PHQ-9 & GAD-7) ---
if st.session_state.get("show_mental_health", False): 
    st.markdown("---")
    st.header("Depression and Anxiety Screening (PHQ-9 & GAD-7)")
    st.markdown("""
    Please consider taking this additional screening for depression and anxiety.
    """)

    # Load trained model
    try:
        model = joblib.load('mental_health_risk_predictor.pkl')
    except Exception as e:
        st.error(f"Could not load mental health model: {e}")
        st.stop()

    # Sidebar with information
    with st.sidebar:
        st.header("About")
        st.markdown("""
        - PHQ-9 assesses depression severity
        - GAD-7 assesses anxiety severity
        - Scores are combined to assess mental health risk
        - All responses are anonymous
        """)

    # Country selection
    country = st.selectbox("Select your country", ["Argentina", "Australia", "Austria", "Bangladesh", "Belgium", "Brazil", "Canada", "China", "Côte d'Ivoire", "Czech Republic", 
                                                   "Denmark", "Egypt", "Ethopia", "Finland", "France", "Gambia", "Germany", "Ghana", "Greece", "Hungary", "India", "Ireland", 
                                                   "Israel", "Italy", "Kenya", "Malawi", "Malaysia", "Mauritius", "Mexico", "Netherlands", "New Zealand", "Nigeria", "Norway", 
                                                   "Pakistan", "Poland", "Portugal", "Romania", "Russia", "Rwanda", "Seychelles", "Singapore", "South Africa", "South Korea", 
                                                   "Spain", "Sri Lanka", "Sweden", "Switzerland", "Tanzania", "Thailand", "Turkey", "Uganda", "Ukraine", "United Arab Emirates", 
                                                   "United Kingdom", "United States"])
    # Dynamic crisis resources based on country
    if country == "Argentina":
        st.markdown("[Crisis Resources (Argentina)](https://www.asistenciaalsuicida.org.ar)")
    elif country == "Australia":
        st.markdown("[Crisis Resources (Australia)](https://www.lifeline.org.au)")
    elif country == "Austria":
        st.markdown("[Crisis Resources (Austria)](https://www.telefonseelsorge.at)")
    elif country == "Bangladesh":
        st.markdown("[Crisis Resources (Bangladesh)](https://www.shuni.org)")
    elif country == "Belgium":
        st.markdown("[Crisis Resources (Belgium)](https://www.zelfmoord1813.be)")
    elif country == "Brazil":
        st.markdown("[Crisis Resources (Brazil)](https://www.cvv.org.br)")
    elif country == "Canada":
        st.markdown("[Crisis Resources (Canada)](https://www.crisisservicescanada.ca)")
    elif country == "China":
        st.markdown("[Crisis Resources (China)](https://www.lifeline-shanghai.com)")
    elif country == "Côte d'Ivoire":
        st.markdown("[Crisis Resources (Côte d'Ivoire)](https://borgenproject.org/mental-health-in-cote-divoire/)")
    elif country == "Czech Republic":
        st.markdown("[Crisis Resources (Czech Republic)](https://www.csspraha.cz)")
    elif country == "Denmark":
        st.markdown("[Crisis Resources (Denmark)](https://www.livslinien.dk)")
    elif country == "Egypt":
        st.markdown("[Crisis Resources (Egypt)](https://help.unhcr.org/egypt/en/health-services/mental-health/)")
    elif country == "Ethopia":
        st.markdown("[Crisis Resources (Ethopia)](https://en.peseschkian-stiftung.de/mental-health-project-in-ethiopia)")
    elif country == "Finland":
        st.markdown("[Crisis Resources (Finland)](https://www.mieli.fi)")
    elif country == "France":
        st.markdown("[Crisis Resources (France)](https://www.expatica.com/fr/healthcare/healthcare-services/mental-healthcare-france-317551/)")
    elif country == "Gambia":
        st.markdown("[Crisis Resources (Gambia)](https://www.gm-nhrc.org/download-file/8b99abcf-d649-11ee-a991-02a8a26af761)")
    elif country == "Germany":
        st.markdown("[Crisis Resources (Germany)](https://www.deutsche-depressionshilfe.de)")
    elif country == "Ghana":
        st.markdown("[Crisis Resources (Ghana)](https://mha-ghana.com)")
    elif country == "Greece":
        st.markdown("[Crisis Resources (Greece)](https://www.psyhelp.gr)")
    elif country == "Hungary":
        st.markdown("[Crisis Resources (Hungary)](https://www.sos505.hu)")
    elif country == "India":
        st.markdown("[Crisis Resources (India)](https://www.vandrevalafoundation.com)")
    elif country == "Ireland":
        st.markdown("[Crisis Resources (Ireland)](https://www.pieta.ie)")
    elif country == "Israel":
        st.markdown("[Crisis Resources (Israel)](https://www.eran.org.il)")
    elif country == "Italy":
        st.markdown("[Crisis Resources (Italy)](https://www.telefonoamico.it)")
    elif country == "Kenya":
        st.markdown("[Crisis Resources (Kenya)](https://www.mtrh.go.ke/?page_id=288)")
    elif country == "Malawi":
        st.markdown("[Crisis Resources (Malawi)](https://mhlec.com/resources/)")
    elif country == "Malaysia":
        st.markdown("[Crisis Resources (Malaysia)](https://www.befrienders.org.my)")
    elif country == "Mauritius":
        st.markdown("[Crisis Resources (Mauritius)](https://www.mauritiusmentalhealth.org)")
    elif country == "Mexico":
        st.markdown("[Crisis Resources (Mexico)](https://www.saptel.org.mx)")
    elif country == "Netherlands":
        st.markdown("[Crisis Resources (Netherlands)](https://www.113.nl)")
    elif country == "New Zealand":
        st.markdown("[Crisis Resources (New Zealand)](https://www.lifeline.org.nz)")
    elif country == "Nigeria":
        st.markdown("[Crisis Resources (Nigeria)](https://www.nigerianmentalhealth.org)")
    elif country == "Norway":
        st.markdown("[Crisis Resources (Norway)](https://www.mentalhelse.no)")
    elif country == "Pakistan":
        st.markdown("[Crisis Resources (Pakistan)](https://www.umang.com.pk)")
    elif country == "Poland":
        st.markdown("[Crisis Resources (Poland)](https://www.116123.pl)")
    elif country == "Portugal":
        st.markdown("[Crisis Resources (Portugal)](https://www.dhi.health.nsw.gov.au/transcultural-mental-health-centre-tmhc/resources/in-your-language/portuguese)")
    elif country == "Romania":
        st.markdown("[Crisis Resources (Romania)](https://mentalhealthforromania.org/en/)")
    elif country == "Russia":
        st.markdown("[Crisis Resources (Russia)](https://www.psychiatr.ru)")
    elif country == "Rwanda":
        st.markdown("[Crisis Resources (Rwanda)](https://www.pih.org/programs/mental-health)")
    elif country == "Seychelles":
        st.markdown("[Crisis Resources (Seychelles)](https://progress.guide/atlas/africa/seychelles/)")
    elif country == "Singapore":
        st.markdown("[Crisis Resources (Singapore)](https://www.sos.org.sg)")
    elif country == "South Africa":
        st.markdown("[Crisis Resources (South Africa)](https://www.safmh.org)")
    elif country == "South Korea":
        st.markdown("[Crisis Resources (South Korea)](https://www.mentalhealthkorea.org)")
    elif country == "Spain":
        st.markdown("[Crisis Resources (Spain)](https://www.telefonodelaesperanza.org)")
    elif country == "Sri Lanka":
        st.markdown("[Crisis Resources (Sri Lanka)](https://www.sumithrayo.org)")
    elif country == "Sweden":
        st.markdown("[Crisis Resources (Sweden)](https://www.mind.se)")
    elif country == "Switzerland":
        st.markdown("[Crisis Resources (Switzerland)](https://www.143.ch)")
    elif country == "Tanzania":
        st.markdown("[Crisis Resources (Tanzania)](https://ticc.org/social-programs/mental-health)")
    elif country == "Thailand":
        st.markdown("[Crisis Resources (Thailand)](https://www.samaritansthai.com)")
    elif country == "Turkey":
        st.markdown("[Crisis Resources (Turkey)](https://www.ruhsal.org)")
    elif country == "Uganda":
        st.markdown("[Crisis Resources (Uganda)](https://www.globalhand.org/en/browse/partnering/3/all/organisation/50801)")
    elif country == "Ukraine":
        st.markdown("[Crisis Resources (Ukraine)](https://mentalhealth.org.ua)")
    elif country == "United Arab Emirates":
        st.markdown("[Crisis Resources (United Arab Emirates)](https://www.mohap.gov.ae)")
    elif country == "United Kingdom":
        st.markdown("[Crisis Resources (United Kingdom)](https://www.samaritans.org)")              
    else:
        st.markdown("[Crisis Resources (US)](https://www.mentalhealth.gov/get-help/immediate-help)")

    # Demographic information
    st.header("Basic Information")
    col1, col2, col3 = st.columns(3)
    with col1:
        age = st.number_input("Age", min_value=12, max_value=120, value=25)
    with col2:
        gender = st.selectbox("Gender", ["Male", "Female", "Other/Prefer not to say"])
    with col3:
        stress_event = st.selectbox("Recent stressful life event?", ["No", "Yes"])

    # PHQ-9 Questions
    st.header("PHQ-9 Depression Screening")
    phq_questions = [
        "Little interest or pleasure in doing things",
        "Feeling down, depressed, or hopeless",
        "Trouble falling/staying asleep, or sleeping too much",
        "Feeling tired or having little energy",
        "Poor appetite or overeating",
        "Feeling bad about yourself - or that you're a failure",
        "Trouble concentrating on things",
        "Moving/speaking slowly or being fidgety/restless",
        "Thoughts of self-harm or suicide"
    ]

    phq_responses = {}
    cols = st.columns(2)
    for i, question in enumerate(phq_questions):
        with cols[i%2]:
            phq_responses[f'phq_q{i+1}'] = st.selectbox(
                f"{i+1}. {question}",
                options=[0, 1, 2, 3],
                format_func=lambda x: [
                    "Not at all", "Several days", 
                    "More than half the days", "Nearly every day"
                ][x]
            )

    # GAD-7 Questions
    st.header("GAD-7 Anxiety Screening")
    gad_questions = [
        "Feeling nervous, anxious, or on edge",
        "Not being able to stop worrying",
        "Worrying too much about different things",
        "Trouble relaxing",
        "Being so restless that it's hard to sit still",
        "Becoming easily annoyed or irritable",
        "Feeling afraid as if something awful might happen"
    ]

    gad_responses = {}
    cols = st.columns(2)
    for i, question in enumerate(gad_questions):
        with cols[i%2]:
            gad_responses[f'gad_q{i+1}'] = st.selectbox(
                f"{i+1}. {question}",
                options=[0, 1, 2, 3],
                format_func=lambda x: [
                    "Not at all", "Several days", 
                    "More than half the days", "Nearly every day"
                ][x]
            )

    # Create input dataframe
    input_data = {
        'age': age,
        'gender': gender,
        'recent_stress_event': 1 if stress_event == "Yes" else 0,
        **phq_responses,
        **gad_responses
    }

    df = pd.DataFrame([input_data])

    # Add missing columns (if any from original training data)
    expected_columns = model.named_steps['preprocessor'].transformers_[1][1].get_feature_names_out().tolist() + \
                       ['age', 'recent_stress_event'] + \
                       [f'phq_q{i+1}' for i in range(9)] + \
                       [f'gad_q{i+1}' for i in range(7)]

    for col in expected_columns:
        if col not in df.columns:
            df[col] = 0

    # Prediction and results
    if st.button("Get Assessment"):
        try:
            # Make prediction
            proba = model.predict_proba(df)[0][1]
            prediction = model.predict(df)[0]
            
            # Display results
            st.subheader("Assessment Results")
            
            # Risk indicator
            if prediction == 1:
                st.error("Our screening suggests you may benefit from professional support")
                st.markdown("**Please consider reaching out to a mental health professional**")
            else:
                st.success("Our screening suggests lower risk of mental health concerns")
                st.markdown("**Remember:** Regular check-ins on mental health are important for everyone")

            # Probability gauge
            st.metric("Risk Probability", f"{proba*100:.1f}%")
            
            # PHQ-9 Score
            phq_total = sum(phq_responses.values())
            st.write(f"PHQ-9 Total Score: {phq_total}/27")
            
            # GAD-7 Score
            gad_total = sum(gad_responses.values())
            st.write(f"GAD-7 Total Score: {gad_total}/21")
            
               
            # Resources based on country
            st.subheader("Resources")
            if country == "Argentina":
                st.markdown("""
                - **Suicide Prevention Hotline**: 135 (24/7)
                - [Asistencia al Suicida](https://www.asistenciaalsuicida.org.ar)
                - **Hospital Nacional Mental Health**: 0800-345-1435
                - [Mental Health Argentina](https://www.argentina.gob.ar/salud/mental)
                """)
            elif country == "Australia":
                st.markdown("""
                - **Lifeline Australia**: 13 11 14
                - [Beyond Blue](https://www.beyondblue.org.au): 1300 22 4636
                - [Kids Helpline](https://www.kidshelpline.com.au): 1800 55 1800
                """)
            elif country == "Austria":
                st.markdown("""
                - **Crisis Hotline**: 144 or 112 
                - [Psychosocial Services Austria](https://eu-promens.eu/exchange-visit-austria-1/pages/programme)
                - **Youth Support**: 147 Rat auf Draht
                - [Mental healthcare in Austria](https://www.expatica.com/at/healthcare/healthcare-services/austria-mental-health-109300/)
                """)
            elif country == "Bangladesh":
                st.markdown("""
                - **National Helpline**: ***********
                - [Mental Health Bangladesh](https://www.dghs.gov.bd)
                - **Kaan Pete Roi**: ***********
                - [Moner Bondhu](https://www.monerbondhu.com): ***********
                """)
            elif country == "Belgium":
                st.markdown("""
                - **Zelfmoordlijn 1813**: 1813
                - [Te Gek!?](https://www.tegek.be): 9000
                - [Awel Youth Line](https://www.awel.be): 102
                """)
            elif country == "Brazil":
                st.markdown("""
                - **CVV Suicide Prevention**: 188 (24/7)
                - [Mental Health Brazil](https://www.cvv.org.br)
                - **Psychiatric Emergency**: 190
                """)
            elif country == "Canada":
                st.markdown("""
                - **Crisis Services Canada**: 1-************
                - [Kids Help Phone](https://kidshelpphone.ca): 1-************
                - [Hope for Wellness Helpline](https://www.hopeforwellness.ca): 1-************
                """)
            elif country == "China":
                st.markdown("""
                - **Beijing Suicide Research Center**: ************
                - [Mental Health China](http://www.crisis.org.cn) 
                - **Psychological Support Hotline**: 010-82951332
                - [Lifeline Shanghai](https://www.lifeline-shanghai.com): ************
                """)
            elif country == "Côte d'Ivoire":
                st.markdown("""
                - [Mental Health Authority Côte d'Ivoire](https://borgenproject.org/mental-health-in-cote-divoire/): (*************
                - [National Mental Health Programme](https://reliefweb.int/report/cote-divoire/optimizing-mental-health-care-prayer-camps-cote-divoire): <EMAIL>
                - [Find a Therapist](https://turbomedics.com) : +234 ************           
                """)
            elif country == "Czech Republic":
                st.markdown("""
                - **Crisis Linka**: 116 123
                - [Czech Psychiatric Society](https://www.psychiatrie.cz): +420 773 786 133
                - **Don't Give Up!**: 778 870 344
                - [Online Therapy CZ](https://www.terap.io)
                """)
            elif country == "Denmark":
                st.markdown("""
                - **Livslinien**: 70 201 201
                - [PsykiatriFonden](https://www.psykiatrifonden.dk): 39 25 25 25
                - **Børns Vilkår**: 116 111 (Children's Help)
                """)
            elif country == "Egypt":
                st.markdown("""
                - [Mental Health Service](https://egyptiansocietyformh.com): <EMAIL>
                - [UNHCR](https://help.unhcr.org/egypt/en/health-services/mental-health/): ********** 
                - [Find a Therapist](https://turbomedics.com) : +234 ************           
                """)
            elif country == "Ethopia":
                st.markdown("""
                - [Mental Health Service](https://mhsua.org/contact/): +251 945 565656
                - [Ethiopia Community Support And Advocacy Center](https://www.ecsac.org/mentalhealth): (571) 351-6117
                - [Find a Therapist](https://turbomedics.com) : +234 ************           
                """)
            elif country == "Finland":
                st.markdown("""
                - **MIELI Crisis Center**: 09 2525 0111
                - [Mental Health Finland](https://www.mieli.fi)
                - **Children and Youth**: 116 111
                - [Online Therapy Finland](https://mielipalvelut.fi/therapy-in-english-mielipalvelut/?gad_source=1&gad_campaignid=20578186544&gbraid=0AAAAADPTl64ZwpDOHfNKnLxekhgkDAYU5&gclid=Cj0KCQjw0LDBBhCnARIsAMpYlAoFpQmaqBxD-03MXfOJ8tf9dGiOrMk4gGsSIp9tRzp7L60dECPMnoQaAt9TEALw_wcB)
                """)
            elif country == "France":
                st.markdown("""
                - **SOS Amitié**: 09 72 39 40 50
                - [La Croix-Rouge Écoute](https://www.croix-rouge.fr): 0 800 858 858
                - [Fil Santé Jeunes](https://www.filsantejeunes.com): 0 800 235 236
                - [Association France Dépression](https://www.france-depression.org)
                """)
            elif country == "Gambia":
                st.markdown("""
                - [Mental Health Awareness in Ghana](https://www.my-gambia.com/mymagazine/supportive-activists-foundation-saf/#:~:text=Supportive%20Activist%27s%20Foundation%20is%20a,ill%2Dhealth%20and%20the%20needy.): +220 214 00 00
                - [Mental Health Services in Gambia](https://www.betterplace.org/en/projects/106360-capacity-building-mental-health-services-in-gambia): +49 30 568 38659
                - [Find a Therapist](https://turbomedics.com) : +234 ************           
                """)
            elif country == "Germany":
                st.markdown("""
                - **Emergency Psychological Help**: 0800 111 0 111
                - [German Depression Aid](https://www.deutsche-depressionshilfe.de)
                - [Telefonseelsorge](https://www.telefonseelsorge.de): 0800 111 0 222
                - [Psychotherapeutic Federal Chamber](https://www.bptk.de)
                """)
            elif country == "Ghana":
                st.markdown("""
                - [Mental Health Authority Ghana](https://mha-ghana.com): **********
                - [Mental Health Foundation of Ghana](https://www.mhinnovation.net/organisations/mental-health-foundation-ghana)
                - [Care and Action for Mental Health in Africa Ghana](https://www.camha.org)
                - [Find a Therapist](https://turbomedics.com) : +234 ************           
                """)
            elif country == "Greece":
                st.markdown("""
                - **Suicide Help Greece**: 1018
                - [Klimaka NGO Crisis Line](https://www.klimaka.org.gr): 1056
                - **Child Support**: 115 25 (Hellenic Pediatric Association)
                - [Greek Mental Health Society](https://www.psyhelp.gr)
                """)
            elif country == "Hungary":
                st.markdown("""
                - **SOS Mental Health**: 06 80 505 505
                - [Hungarian Psychiatric Society](https://www.europsy.net/npa-members/?id=13): 1 2006533 1 3920063
                - **Blue Line Crisis Center**: 06-80-820-111
                - [Online Therapy Hungary](https://www.therapyroute.com/therapists/hungary/1)
                """)
            elif country == "India":
                st.markdown("""
                - **Vandrevala Foundation**: 1860 2662 345
                - [iCall Psychosocial Helpline](https://icallhelpline.org): **********
                - [AASRA Crisis Line](https://www.aasra.info): 91-**********
                """)
            elif country == "Ireland":
                st.markdown("""
                - **Pieta House**: 1800 247 247
                - [Aware Depression Support](https://www.aware.ie): 1800 80 48 48
                - **Samaritans Ireland**: 116 123
                - [Turn2Me Online Therapy](https://www.turn2me.ie)
                """)
            elif country == "Israel":
                st.markdown("""
                - **ERAN Emotional First Aid**: 1201
                - [Ministry of Health](https://www.health.gov.il): *2974 from any phone
                - **SAHAR Emotional Support**: 1-800-363-363
                - [Natal Trauma Support](https://www.natal.org.il): 1-800-363-363
                """)
            elif country == "Italy":
                st.markdown("""
                - **Telefono Amico**: 02 2327 2327
                - [Samaritans Onlus](https://findahelpline.com/organizations/samaritans-onlus): 06 77208977
                - [La Voce Amica](https://www.lavoceamica.it): 02 873 873
                - [Emergency Psychological Support]: 800 833 833
                """)
            elif country == "Kenya":
                st.markdown("""
                - [Suicide Prevention](https://befrienders.org/find-support-now/befrienders-kenya/?country=ke): +254 722 178 177
                - [Mental Health Foundation Helpline](https://mental360.or.ke): +254710360360
                - [Kamili Organization](https://www.kamilimentalhealth.org): +254 (0)700 327 701
                - [Find a Therapist](https://turbomedics.com) : +234 ************           
                """)
            elif country == "Malawi":
                st.markdown("""
                - [Local mental health support](https://mhlec.com/resources/): +265 1 311 690
                - [Find a Therapist](https://turbomedics.com) : +234 ************           
                """)
            elif country == "Malaysia":
                st.markdown("""
                - **Befrienders KL**: 03-76272929
                - [Mental Health Malaysia](https://www.befrienders.org.my)
                - **Ministry of Health**: 03-29359935
                - [Talian Kasih](https://www.jkm.gov.my): 15999 (Domestic violence/abuse)
                """)
            elif country == "Mauritius":
                st.markdown("""
                - [Mauritius Mental Health Association](https://www.actogether.mu/find-an-ngo/mauritius-mental-health-association): +************
                - [Special Education Needs Authority](https://sena.govmu.org/sena/?page_id=2892): 460 3015
                - [Find a Therapist](https://turbomedics.com) : +234 ************           
                """)
            elif country == "Mexico":
                st.markdown("""
                - **SAPTEL Crisis Line**: 55 5259-8121 (24/7)
                - [Mental Health Mexico](https://www.saptel.org.mx)
                - **UNAM Psychological Support**: 55 5025-0855
                """)
            elif country == "Netherlands":
                st.markdown("""
                - **113 Suicide Prevention**: 0900 0113
                - [MIND Korrelatie](https://www.mindkorrelatie.nl): 0900 1450
                - [iPractice Online Therapy](https://www.ipractice.nl)
                - [De Luisterlijn](https://www.deluisterlijn.nl): 0900 0767
                """)
            elif country == "New Zealand":
                st.markdown("""
                - **Lifeline Aotearoa**: 0800 543 354
                - [Youthline](https://www.youthline.co.nz): 0800 376 633
                - [Depression Helpline](https://www.depression.org.nz): 0800 111 757
                """)
            elif country == "Nigeria":
                st.markdown("""
                - [Nigerian Mental Health] (https://www.nigerianmentalhealth.org): +234 ************
                - [Mentally Aware Nigeria Initiative (MANI)](https://mentallyaware.org): 08091116264
                - [Suicide Research and Prevention Initiative](https://www.surpinng.com): +234-************
                - [Find a Therapist](https://turbomedics.com) : +234 ************
                """)
            elif country == "Norway":
                st.markdown("""
                - **Mental Helse**: 116 123
                - [Kirkens SOS](https://www.kirkens-sos.no): 22 40 00 40
                - **Children's Help Line**: 116 111
                - [Online Therapy Norway](https://www.psykologportalen.no)
                """)
            elif country == "Pakistan":
                st.markdown("""
                - **Umang Helpline**: 0311-7786264
                - [Ministry of NHS](https://www.nhsrc.gov.pk): 1166
                - **Karachi Suicide Prevention**: 021-111-911-911
                """)
            elif country == "Poland":
                st.markdown("""
                - **Kryzysowy Telefon Zaufania**: 116 123
                - [ITAKA Foundation](https://www.stopdepresji.pl): 22 654 40 41
                - [Youth Support Line](https://www.liniadzieciom.pl): 116 111
                - [Mental Health Helpline]: 800 702 222
                """)
            elif country == "Portugal":
                st.markdown("""
                - **SOS Voz Amiga**: 213 544 545
                - [Portuguese Mental Health & Addictions Services](https://www.uhn.ca/MentalHealth/Clinics/Portuguese_Addiction_Services): ************
                - **Conversa Amiga**: 808 237 327
                - [APSI Suicide Prevention](https://www.apsi.org.pt): : 21 884 41 00
                """)
            elif country == "Romania":
                st.markdown("""
                - **Telefonul Alb**: 0800 0700 10
                - [ASUR Romanian Psychologists](https://www.asur.ro)
                - **Child Helpline**: 116 111
                - [Mental Health Initiative Supports](https://www.opensocietyfoundations.org/newsroom/mental-health-initiative-supports-monitoring-project-romania-advance-rights-people): +1 ************
                """)
            elif country == "Russia":
                st.markdown("""
                - **Emergency Psychological Help**: 8-800-333-44-34
                - [Mental Health Russia](https://www.psychiatr.ru)
                - [Krizisnaya Liniya](https://www.telefon-doveria.ru): 8-800-2000-122
                """)
            elif country == "Rwanda":
                st.markdown("""
                - [MENTAL HEALTH DEPARTMENT](https://www.chub.rw/clinical-service-division/mental-health): +250 789660010
                - [Mental Health Division](https://rbc.gov.rw/who-we-are/our-divisions-and-units/mental-health-division): 114
                - [Emergency Line](https://rbc.gov.rw/who-we-are/our-divisions-and-units/mental-health-division): 912
                - [Find a Therapist](https://turbomedics.com) : +234 ************           
                """)
            elif country == "Seychelles":
                st.markdown("""
                - [Suicide Prevention](https://progress.guide/atlas/africa/seychelles/): +************
                - [Mental Health Helpline](https://progress.guide/atlas/africa/seychelles/): +************
                - [Emergency Line]: 151
                - [Find a Therapist](https://turbomedics.com) : +234 ************           
                """)
            elif country == "Singapore":
                st.markdown("""
                - **Institute of Mental Health**: 6389-2222 (24h emergency)
                - [SOS Samaritans](https://www.sos.org.sg): 1-767 (24/7)
                - **Silver Ribbon SG**: 6386-1928
                - [HealthHub Mental Wellness](https://www.healthhub.sg)
                """)
            elif country == "South Africa":
                st.markdown("""
                - [Suicide Crisis Helpline](https://mha-ghana.com): 0800 567 567
                - [SA Mental Health Foundation](https://www.scan-network.org.za/ngo-listings/sa-mental-health-foundation/): **********
                - [INALA MENTAL HEALTH FOUNDATION](https://www.inala.org.za): Email: <EMAIL>
                - [Find a Therapist](https://turbomedics.com) : +234 ************           
                """)
            elif country == "South Korea":
                st.markdown("""
                - **Suicide Prevention Hotline**: 1577-0199
                - [Korea Mental Health Foundation]((https://www.mentalhealthkorea.org)
                - **Lifeline Korea**: 1588-9191
                - [Seoul Global Center](https://global.seoul.go.kr): 02-2075-4180 (Foreign language support)
                """)
            elif country == "Spain":
                st.markdown("""
                - **Teléfono de la Esperanza**: 717 003 717
                - [Cruz Roja Escucha](https://www.cruzroja.es): 900 107 917
                - [ANAR Foundation](https://www.anar.org): 900 20 20 10
                - [Confidential Suicide Hotline]: 914 590 055
                """)
            elif country == "Sri Lanka":
                st.markdown("""
                - **Sumithrayo**: ***********
                - [National Institute of Mental Health](https://www.nimh.health.gov.lk): 1926
                - **CCCline**: 1333
                - [Shanthi Maargam](https://www.shanthimaargam.org): ***********
                """)
            elif country == "Sweden":
                st.markdown("""
                - **Mind Sverige**: 901 01 (Chat available)
                - [Bris Youth Support](https://www.bris.se): 116 111
                - [Självmordslinjen](https://www.sjalvmordslinjen.se): 901 01
                - [Kry Mental Health Services](https://www.kry.se)
                """)
            elif country == "Switzerland":
                st.markdown("""
                - **Die Dargebotene Hand**: 143
                - [Pro Mente Sana](https://www.promentesana.ch): 0848 800 858
                - [SafeZone Online Counseling](https://www.safezone.ch)
                - [Children's Advice Line](https://www.147.ch): 147
                """)
            elif country == "Tanzania":
                st.markdown("""
                - [Mwanamke Initiatives Foundation](https://www.mif.or.tz/our-work/program/health-program): +255 623 057 457
                - [TAHMEF](https://www.tahmef.org): +255 692 773 854
                - [Arise International Mental Health Foundation](https://arisementalhealthfoundation.com)
                - [Find a Therapist](https://turbomedics.com) : +234 ************           
                """)
            elif country == "Thailand":
                st.markdown("""
                - **Samaritans of Thailand**: 02-713-6793 (EN/TH)
                - [Department of Mental Health](https://www.dmh.go.th): 1323
                - **Bangkok Mental Health**: 02-026-5905
                - [Sati App](https://www.sati.app) (Digital support)
                """)
            elif country == "Turkey":
                st.markdown("""
                - **Psychological Support Line**: 182
                - [Turkish Mental Health Foundation](https://www.ruhsal.org)
                - [Psikolojik Destek Hattı](https://www.psikolog.org.tr): 0850 280 1475
                """)
            elif country == "Uganda":
                st.markdown("""
                - [Mental Health in Uganda](https://mhu.ug): **********
                - [Haven Mental Health Foundation](https://www.havenmentalhealthfoundation.org): +256 751902509
                - [Find a Therapist](https://turbomedics.com) : +234 ************           
                """)
            elif country == "Ukraine":
                st.markdown("""
                - **Emergency Mental Health Hotline**: 0 800 100 102 (24/7)
                - [Ukrainian Mental Health Center](https://mentalhealth.org.ua): +38(044)503-87-33
                - **UNICEF Support Line**: 0 800 500 225
                - [Psychological First Aid Ukraine](https://www.learning.foundation/ukraine)
                - **International Red Cross Support**: +380 44 235 1515
                - [WHO Mental Health Resources](https://www.who.int/ukraine)
                """)
            elif country == "United Arab Emirates":
                st.markdown("""
                - **Dubai Health Authority**: 800342
                - [Al Amal Hospital Mental Health](https://www.mohap.gov.ae)
                """)
            elif country == "United Kingdom":
                    st.markdown("""
                    - **Samaritans**: 116 123 (24/7)
                    - [NHS Mental Health Services](https://www.nhs.uk)
                    - [Mind UK](https://www.mind.org.uk): 0300 123 3393
                    - [Shout Crisis Text Line]: Text SHOUT to 85258
                    """)
            else:
                st.markdown("""
                - [National Suicide Prevention Lifeline]((https://suicidepreventionlifeline.org): 1-************
                - [Crisis Text Line](https://www.crisistextline.org): Text HOME to 741741
                - [NAMI Helpline](https://www.nami.org): 1-************
                - [Find a Therapist](https://www.psychologytoday.com)
                """)


        except Exception as e:
            st.error(f"Error in processing: {str(e)}")

    # Footer disclaimer
    st.markdown("---")
    st.markdown("""
    **Disclaimer**: This tool is not a substitute for professional medical advice. 
    Always seek the advice of qualified health providers with any questions you may have regarding medical conditions.
    """)