# Function to analyze kidney function
def analyze_kidney_function(data):
    analysis = []
    missing_parameters = []

    # Define normal ranges, clinical thresholds, and interpretations
    parameter_info = {
        "BUN": {"range": (7, 20), "elevated": "High levels may indicate kidney dysfunction or dehydration.", "decreased": "Low levels may indicate malnutrition or liver disease."},
        "Serum Urea": {"range": (2.5, 7.1), "elevated": "Elevated levels may suggest kidney issues or high protein intake.", "decreased": "Low levels may indicate malnutrition or liver disease."},
        "Serum Creatinine": {"range": (0.6, 1.2), "elevated": "High levels may indicate impaired kidney function.", "decreased": "Low levels may indicate reduced muscle mass."},
        "eGFR": {"range": (90, float("inf")), "elevated": None, "decreased": "Lower levels indicate reduced kidney filtration capacity."},
        "BUN/Creatinine Ratio": {"range": (10, 20), "elevated": "Elevated BUN/Creatinine Ratio may indicate dehydration or reduced kidney perfusion.", "decreased": "Low BUN/Creatinine Ratio may indicate liver disease or malnutrition."},
        "Urea/Creatinine Ratio": {"range": (40, 100), "elevated": "High Urea/Creatinine Ratio may indicate dehydration or high protein intake.", "decreased": "Low Urea/Creatinine Ratio may indicate liver disease or malnutrition."},
        "Serum Sodium": {"range": (135, 145), "elevated": "High levels may indicate dehydration.", "decreased": "Low levels may indicate overhydration or kidney dysfunction."},
        "Serum Potassium": {"range": (3.5, 5.0), "elevated": "High levels may indicate kidney dysfunction or acidosis.", "decreased": "Low levels may indicate alkalosis or diuretic use."},
        "Serum Calcium": {"range": (8.8, 10.2), "elevated": "High levels may indicate hyperparathyroidism or cancer.", "decreased": "Low levels may indicate kidney disease or vitamin D deficiency."},
        "Serum Uric Acid": {"range": (3.5, 7.2), "elevated": "High levels may indicate gout or kidney dysfunction.", "decreased": "Low levels may indicate liver disease."},
        "Chloride": {"range": (96, 106), "elevated": "High levels may indicate dehydration.", "decreased": "Low levels may indicate alkalosis."},
        "Bicarbonate": {"range": (22, 29), "elevated": "High levels may indicate metabolic alkalosis.", "decreased": "Low levels may indicate metabolic acidosis."},
        "ACR": {"range": (None, 30), "elevated": "High levels indicate increased albumin excretion, a marker of kidney damage.", "decreased": None}
    }

    # Analyze each parameter
    for param, value in data.items():
        if value is None:
            missing_parameters.append(param)
        elif param in parameter_info:
            info = parameter_info[param]
            low, high = info["range"]
            if low is not None and high is not None:
                if low <= value <= high:
                    analysis.append(f"{param}: {value} → Normal")
                elif value > high:
                    analysis.append(f"{param}: {value} → High (Above Normal Range). {info['elevated']}")
                elif value < low:
                    analysis.append(f"{param}: {value} → Low (Below Normal Range). {info['decreased']}")
            elif high is not None and value > high:
                analysis.append(f"{param}: {value} → High (Above Normal Range). {info['elevated']}")
            elif low is not None and value < low:
                analysis.append(f"{param}: {value} → Low (Below Normal Range). {info['decreased']}")

    # Add eGFR stages
    if data["eGFR"]:
        egfr = data["eGFR"]
        if egfr >= 90:
            stage = "Stage 1 (Normal or High)"
        elif 60 <= egfr < 90:
            stage = "Stage 2 (Mildly Decreased)"
        elif 30 <= egfr < 60:
            stage = "Stage 3 (Moderate CKD)"
        elif 15 <= egfr < 30:
            stage = "Stage 4 (Severe CKD)"
        else:
            stage = "Stage 5 (Kidney Failure)"
        analysis.append(f"eGFR: {egfr} → {stage}")

    return analysis, missing_parameters

def get_manual_input():
    extracted_data = {
        "BUN": None,
        "Serum Urea": float(input("Serum Urea: ")),
        "Serum Creatinine": float(input("Serum Creatinine: ")),
        "eGFR": None,
        "BUN/Creatinine Ratio": None,
        "Urea/Creatinine Ratio": None,
        "Serum Sodium": float(input("Serum Sodium: ")),
        "Serum Potassium": float(input("Serum Potassium: ")),
        "Serum Calcium": float(input("Serum Calcium: ")),
        "Serum Uric Acid": float(input("Serum Uric Acid: ")),
        "Urine Albumin": float(input("Urine Albumin: ")),
        "Urine Creatinine": float(input("Urine Creatinine: ")),
        "Chloride": float(input("Chloride: ")),
        "Bicarbonate": float(input("Bicarbonate: ")),
        "ACR": None,
        "Age": int(input("Age: ")),
        "Sex": input("Sex (male/female): ").strip().capitalize()
    }

    # Allow user to enter or calculate BUN
    bun_input = input("BUN (leave blank to calculate from Serum Urea): ").strip()
    if bun_input:
        extracted_data["BUN"] = float(bun_input)
    else:
        extracted_data["BUN"] = extracted_data["Serum Urea"] / 2.14  # Example conversion factor

    # Allow user to enter or calculate ACR
    acr_input = input("ACR (leave blank to calculate from Urine Albumin and Urine Creatinine): ").strip()
    if acr_input:
        extracted_data["ACR"] = float(acr_input)
    else:
        extracted_data["ACR"] = extracted_data["Urine Albumin"] / extracted_data["Urine Creatinine"]

    # Allow user to enter or calculate BUN/Creatinine Ratio
    bun_creatinine_ratio_input = input("BUN/Creatinine Ratio (leave blank to calculate from BUN and Serum Creatinine): ").strip()
    if bun_creatinine_ratio_input:
        extracted_data["BUN/Creatinine Ratio"] = float(bun_creatinine_ratio_input)
    else:
        extracted_data["BUN/Creatinine Ratio"] = extracted_data["BUN"] / extracted_data["Serum Creatinine"]

    # Allow user to enter or calculate Urea/Creatinine Ratio
    urea_creatinine_ratio_input = input("Urea/Creatinine Ratio (leave blank to calculate from Serum Urea and Serum Creatinine): ").strip()
    if urea_creatinine_ratio_input:
        extracted_data["Urea/Creatinine Ratio"] = float(urea_creatinine_ratio_input)
    else:
        extracted_data["Urea/Creatinine Ratio"] = extracted_data["Serum Urea"] / extracted_data["Serum Creatinine"]

    # Allow user to enter or calculate eGFR
    egfr_input = input("eGFR (leave blank to calculate): ").strip()
    if egfr_input:
        extracted_data["eGFR"] = float(egfr_input)
    else:
        # Prompt for age and sex only if eGFR is blank
        age = extracted_data["Age"]
        sex = extracted_data["Sex"]
        k = 0.742 if sex == "Female" else 1.0
        extracted_data["eGFR"] = 186 * (extracted_data["Serum Creatinine"] ** -1.154) * (age ** -0.203) * k

    # Reorder calculated values to come last
    extracted_data = reorder_extracted_data(extracted_data)

    return extracted_data

def reorder_extracted_data(data):
    """
    Reorder the extracted data dictionary to ensure calculated values (ACR, eGFR, BUN/Creatinine Ratio, Urea/Creatinine Ratio, BUN)
    are listed last.
    """
    calculated_keys = ["ACR", "eGFR", "BUN/Creatinine Ratio", "Urea/Creatinine Ratio", "BUN"]
    reordered_data = {key: value for key, value in data.items() if key not in calculated_keys}
    for key in calculated_keys:
        if key in data:
            reordered_data[key] = data[key]
    return reordered_data

def main():
    choice = input("Do you want to extract results from a PDF or enter manually? (pdf/manual): ").strip().lower()
    
    if choice == "manual":
        extracted_data = get_manual_input()
        
        # Reorder extracted data to ensure calculated values come last
        extracted_data = reorder_extracted_data(extracted_data)
        
        print("\nExtracted Data:")
        for key, value in extracted_data.items():
            print(f"{key}: {value if value is not None else 'Not Provided'}")
        
        # Generate findings and missing parameters
        analysis, missing_parameters = analyze_kidney_function(extracted_data)
        
        print(f"\n🔹 Kidney Function Analysis:")
        for key in extracted_data.keys():
            for result in analysis:
                if result.startswith(key):
                    print(f"   - {result}")

        # Add preliminary assessment
        print("\nBased on the available test results, here is my preliminary assessment:")

        # Add findings (previously Kidney Health Summary)
        print("\n🔹 Findings:")
        overall_health = "Unable to determine overall kidney health due to missing data."
        if (
            extracted_data["eGFR"] and extracted_data["BUN/Creatinine Ratio"] and extracted_data["Serum Sodium"] and
            extracted_data["Serum Potassium"] and extracted_data["ACR"] and extracted_data["Bicarbonate"] and
            extracted_data["Chloride"] and extracted_data["Serum Uric Acid"]
        ):
            egfr = extracted_data["eGFR"]
            bun_creatinine_ratio = extracted_data["BUN/Creatinine Ratio"]
            sodium = extracted_data["Serum Sodium"]
            potassium = extracted_data["Serum Potassium"]
            acr = extracted_data["ACR"]
            bicarbonate = extracted_data["Bicarbonate"]
            chloride = extracted_data["Chloride"]
            uric_acid = extracted_data["Serum Uric Acid"]

            if (
                egfr >= 90 and 10 <= bun_creatinine_ratio <= 20 and 135 <= sodium <= 145 and 3.5 <= potassium <= 5.0 and
                acr < 30 and 22 <= bicarbonate <= 29 and 96 <= chloride <= 106 and 3.5 <= uric_acid <= 7.2
            ):
                overall_health = "Your kidney health is normal."
            elif (
                egfr < 90 or bun_creatinine_ratio < 10 or bun_creatinine_ratio > 20 or sodium < 135 or sodium > 145 or
                potassium < 3.5 or potassium > 5.0 or acr >= 30 or bicarbonate < 22 or bicarbonate > 29 or
                chloride < 96 or chloride > 106 or uric_acid < 3.5 or uric_acid > 7.2
            ):
                overall_health = "You may have mild to moderate kidney impairment. Consult a doctor for further evaluation."
            elif egfr < 30:
                overall_health = "You may have severe kidney disease. Immediate medical attention is recommended."
        elif extracted_data["eGFR"]:
            egfr = extracted_data["eGFR"]
            if egfr >= 90:
                overall_health = "Your kidney filtration rate is normal."
            elif egfr < 90:
                overall_health = "Your kidney filtration rate is below normal. Consult a doctor for further evaluation."
        print(overall_health)
        
        # Add confidence level
        confidence_level = "High" if not missing_parameters else "Medium" if len(missing_parameters) <= 3 else "Low"
        print(f"🔹 Confidence Level: {confidence_level} (Due to {'missing parameters: ' + ', '.join(missing_parameters) if missing_parameters else 'complete data'})")
        
        # Add additional statement about missing parameters and further tests
        if missing_parameters:
            print(f"Some parameters necessary for a more complete analysis were not provided, which may affect the accuracy of this assessment. Specifically, results for {', '.join(missing_parameters)} would help refine the findings.")
        
        # Show recommendations based on any missing value
        if missing_parameters:
            specific_tests = [f"{param} test" for param in missing_parameters]
            print("For a more comprehensive kidney function evaluation, we recommend considering the following additional tests:")
            print(f" - {', '.join(specific_tests)}")
    
    else:
        print("Invalid choice. Please enter 'pdf' or 'manual'.")

if __name__ == "__main__":
    main()