from fastapi import FastAPI
import uvicorn
import ollama
import faiss
import pickle
import numpy as np
from uuid import uuid4
from pydantic import BaseModel
from typing import Dict, List
from langchain_community.embeddings import OllamaEmbeddings
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import asyncio
import spacy

# Initialize FastAPI app
app = FastAPI()

# === MODEL CONSTANTS ===
QWEN_MODEL = "qwen2.5:1.5b"
DEEPSEEK_MODEL = "deepseek-r1:1.5b"

# === VECTOR STORE PATHS ===
VECTOR_STORE_PATHS = {
    QWEN_MODEL: {
        "index": "./embeddings/qwen/index.faiss",
        "metadata": "./embeddings/qwen/index.pkl"
    },
    DEEPSEEK_MODEL: {
        "index": "./embeddings/deepseek/index.faiss",
        "metadata": "./embeddings/deepseek/index.pkl"
    }
}

# === STORE CHAT TITLES ===
chat_titles = {}  # Dictionary to store session titles per user

# === LOAD SPACY MODEL ===
nlp = spacy.load("en_core_web_sm")

# === FUNCTION TO GENERATE CHAT TITLE USING SPACY ===
def generate_chat_title(first_query: str) -> str:
    doc = nlp(first_query.lower())

    # Curated list of specific and relevant medical keywords/phrases
    medical_keywords = [
        "hypertension", "diabetes", "symptoms", "treatment", "diagnosis", "management",
        "causes", "risk factors", "reproduction", "ovulation", "pregnancy", "infertility",
        "complications", "heart disease", "stroke", "blood pressure", "cholesterol",
        "insulin", "glucose", "fatigue", "depression", "mental health", "therapy",
        "prevention", "diet", "lifestyle changes", "medications", "monitoring", "cycle"
    ]

    # Step 1: Try to find best matching multi-word phrase in the original query
    for phrase in medical_keywords:
        if phrase in first_query.lower():
            return phrase.title()

    # Step 2: Use spaCy to extract nouns/proper nouns that are medical in context
    medical_tokens = [token.text for token in doc if token.pos_ in ("NOUN", "PROPN") and token.text.lower() in medical_keywords]
    if medical_tokens:
        return " ".join(medical_tokens[:3]).title()

    # Step 3: Fallback to slicing first few words of the question
    fallback = " ".join(first_query.strip().split()[:5])
    return fallback.title() if fallback else "Health Chat"




    
# === LOAD NLP SUMMARIZATION MODEL ===
#summarizer = pipeline("summarization")

#def generate_chat_title(first_query: str) -> str:
    #"""Generate a short and meaningful chat title from the first user query."""
    #summary = summarizer(first_query, max_length=15, min_length=3, do_sample=False)
    #return summary[0]['summary_text'].title()  # Capitalize title

#def generate_chat_title(first_query: str) -> str:
    #"""Generate a title by extracting key words from the query."""
    #words = first_query.split()[:5]  # Take the first 5 words
    #return " ".join(words).title()
    
# === LOAD FAISS INDEXES & METADATA ===
vector_indexes = {}
vector_docs = {}
embedding_models = {}

for model_name, paths in VECTOR_STORE_PATHS.items():
    try:
        vector_indexes[model_name] = faiss.read_index(paths["index"])
        print(f"✅ FAISS index loaded for {model_name}")
    except Exception as e:
        print(f"❌ Error loading FAISS index for {model_name}: {e}")

    try:
        with open(paths["metadata"], "rb") as f:
            vector_docs[model_name] = pickle.load(f)
        print(f"✅ Metadata loaded for {model_name}")
    except Exception as e:
        print(f"❌ Error loading metadata for {model_name}: {e}")

    try:
        embedding_models[model_name] = OllamaEmbeddings(model=model_name)
    except Exception as e:
        print(f"❌ Error loading embedding model {model_name}: {e}")

# === CHAT HISTORY CONFIGURATION ===
chat_histories: Dict[str, List[Dict[str, str]]] = {}
MAX_HISTORY_LENGTH = 10
TOP_K = 1  # Number of relevant documents to fetch

# === REQUEST MODEL ===
class ChatRequest(BaseModel):
    session_id: str
    user_id: str
    query: str
    model: str  # ✅ Add model selection from user input

# === ASYNC FUNCTION TO RETRIEVE CONTEXT ===
async def retrieve_context(query: str, model_name: str, top_k: int = TOP_K):
    if model_name not in vector_indexes or model_name not in vector_docs:
        return ""

    embedder = embedding_models[model_name]
    query_embedding = np.array([embedder.embed_query(query)]).astype("float32")

    index = vector_indexes[model_name]
    documents = vector_docs[model_name]

    distances, indices = index.search(query_embedding, top_k)

    relevant_docs = [
        documents[idx].get("text", "") if isinstance(documents[idx], dict) else str(documents[idx])
        for idx in indices[0] if idx < len(documents)
    ]

    return " ".join(relevant_docs)

# === QUERY HANDLER ===
@app.post("/query")
async def get_response(chat: ChatRequest):
    user_id, query, selected_model = chat.user_id, chat.query, chat.model

    # ✅ Validate model selection
    if selected_model not in [QWEN_MODEL, DEEPSEEK_MODEL]:
        return {"error": "Invalid model selection. Choose either LLaMA or DeepSeek."}

    # ✅ Set system role based on selected model
    system_roles = {
        QWEN_MODEL: "You are a health research assistant with expertise in hypertension, diabetes, reproduction, and general health topics.",
        DEEPSEEK_MODEL: "You are an AI medical specialist focused on advanced diagnostics, clinical research, and AI-driven medical analysis."
    }

    context = system_roles[selected_model]
    relevant_info = await retrieve_context(query, selected_model, TOP_K)
    full_context = f"{context} {relevant_info}"

    # === Assign Chat Title if First Interaction ===
    if user_id not in chat_histories:
        chat_histories[user_id] = [{"role": "system", "content": full_context}]
        chat_titles[user_id] = generate_chat_title(query)  # Generate a title for this session

    chat_histories[user_id].append({"role": "user", "content": query})

    # ✅ Keep chat history within the defined limit
    if len(chat_histories[user_id]) > MAX_HISTORY_LENGTH:
        chat_histories[user_id] = chat_histories[user_id][-MAX_HISTORY_LENGTH:]

    try:
        response = ollama.chat(model=selected_model, messages=chat_histories[user_id])
        model_response = response["message"]["content"]
        chat_histories[user_id].append({"role": "assistant", "content": model_response})

        return {
            "response": model_response,
            "chat_title": chat_titles[user_id],  # ✅ Return the generated chat title
            "chat_history": chat_histories[user_id]
        }
    except Exception as e:
        return {"error": f"Failed to generate response: {str(e)}"}
        

# === RUN SERVER ===
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
