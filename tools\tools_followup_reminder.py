from langchain.tools import Tool
import json
from datetime import datetime, timedelta
import logging

def followup_reminder_tool(data_json: str) -> str:
    """Generate follow-up reminders based on past health data and abnormal values.

    Args:
        data_json: JSON string containing user_id, health_data, and optional test_type

    Returns:
        JSON string with reminders, follow-up schedule, and care continuity plan
    """
    try:
        # Parse input data
        input_data = json.loads(data_json)

        # Extract user data
        user_id = input_data.get("user_id", "unknown")
        health_data = input_data.get("health_data", {})
        test_type = input_data.get("test_type")

        # Generate reminders based on health data
        reminders = generate_reminders(health_data)

        # Filter reminders by test_type if provided
        if test_type:
            filtered_reminders = filter_reminders_by_test_type(reminders, test_type)
        else:
            filtered_reminders = reminders

        # Create follow-up schedule
        follow_up_schedule = create_follow_up_schedule(health_data)

        # Filter follow-up schedule by test_type if provided
        if test_type:
            filtered_schedule = filter_schedule_by_test_type(follow_up_schedule, test_type)
        else:
            filtered_schedule = follow_up_schedule

        # Generate care continuity plan
        care_continuity = generate_care_continuity_plan(health_data)

        # Filter care continuity plan by test_type if provided
        if test_type:
            filtered_care_continuity = filter_care_continuity_by_test_type(care_continuity, test_type)
        else:
            filtered_care_continuity = care_continuity

        # Create summary
        summary = create_reminder_summary(filtered_reminders, filtered_schedule, filtered_care_continuity, test_type)

        # Prepare result data
        result = {
            "reminders": filtered_reminders,
            "follow_up_schedule": filtered_schedule,
            "care_continuity": filtered_care_continuity,
            "summary": summary
        }

        # Add test_type to the result if provided
        if test_type:
            result["test_type"] = test_type

        # Return results
        return json.dumps(result, indent=2)
    except Exception as e:
        logging.error(f"Error in followup_reminder_tool: {str(e)}")
        return json.dumps({
            "error": f"Failed to generate follow-up reminders: {str(e)}"
        })

def generate_reminders(health_data):
    """Generate reminders based on abnormal health values for all tests and vitals"""
    reminders = []

    # === VITAL SIGNS ===
    # Check blood pressure
    systolic = health_data.get("Blood Pressure (Systolic)", 0)
    diastolic = health_data.get("Blood Pressure (Diastolic)", 0)

    if systolic > 180 or diastolic > 120:
        reminders.append({
            "type": "blood_pressure",
            "message": "Your blood pressure readings indicate a hypertensive crisis. Seek immediate medical attention.",
            "urgency": "high",
            "recommended_action": "Seek immediate medical attention",
            "timeframe": "Immediately",
            "test_name": "Blood Pressure"
        })
    elif systolic > 140 or diastolic > 90:
        reminders.append({
            "type": "blood_pressure",
            "message": "Your blood pressure readings indicate hypertension. Schedule a follow-up check within 2 weeks.",
            "urgency": "medium",
            "recommended_action": "Schedule blood pressure check",
            "timeframe": "2 weeks",
            "test_name": "Blood Pressure"
        })
    elif systolic > 120 or diastolic > 80:
        reminders.append({
            "type": "blood_pressure",
            "message": "Your blood pressure readings are elevated. Schedule a follow-up check within 1 month.",
            "urgency": "low",
            "recommended_action": "Schedule blood pressure check",
            "timeframe": "1 month",
            "test_name": "Blood Pressure"
        })

    # Check heart rate
    heart_rate = health_data.get("ECG (Heart Rate)", 0)
    if heart_rate > 100 and heart_rate < 150:
        reminders.append({
            "type": "heart_rate",
            "message": "Your heart rate is elevated (tachycardia). Schedule a cardiac evaluation.",
            "urgency": "medium",
            "recommended_action": "Schedule cardiac evaluation",
            "timeframe": "2 weeks",
            "test_name": "Heart Rate"
        })
    elif heart_rate >= 150:
        reminders.append({
            "type": "heart_rate",
            "message": "Your heart rate is significantly elevated. Seek prompt medical attention.",
            "urgency": "high",
            "recommended_action": "Seek cardiac evaluation",
            "timeframe": "1 week",
            "test_name": "Heart Rate"
        })
    elif heart_rate < 60 and heart_rate > 0:
        reminders.append({
            "type": "heart_rate",
            "message": "Your heart rate is below normal range (bradycardia). Schedule a cardiac evaluation.",
            "urgency": "medium",
            "recommended_action": "Schedule cardiac evaluation",
            "timeframe": "2 weeks",
            "test_name": "Heart Rate"
        })

    # Check glucose
    glucose = health_data.get("Glucose", 0)
    if glucose > 200:
        reminders.append({
            "type": "glucose",
            "message": "Your glucose levels are significantly elevated. Schedule an urgent diabetes evaluation.",
            "urgency": "high",
            "recommended_action": "Schedule diabetes evaluation",
            "timeframe": "1 week",
            "test_name": "Blood Glucose"
        })
    elif glucose > 125:
        reminders.append({
            "type": "glucose",
            "message": "Your glucose levels are above normal range. Schedule a follow-up check within 2 weeks.",
            "urgency": "medium",
            "recommended_action": "Schedule glucose monitoring",
            "timeframe": "2 weeks",
            "test_name": "Blood Glucose"
        })
    elif glucose > 100:
        reminders.append({
            "type": "glucose",
            "message": "Your glucose levels indicate prediabetes. Schedule a follow-up check within 1 month.",
            "urgency": "low",
            "recommended_action": "Schedule glucose monitoring",
            "timeframe": "1 month",
            "test_name": "Blood Glucose"
        })

    # Check HbA1c if available
    hba1c = health_data.get("HbA1c", 0)
    if hba1c > 9:
        reminders.append({
            "type": "hba1c",
            "message": "Your HbA1c indicates poorly controlled diabetes. Schedule an urgent diabetes evaluation.",
            "urgency": "high",
            "recommended_action": "Schedule diabetes evaluation",
            "timeframe": "1 week",
            "test_name": "HbA1c"
        })
    elif hba1c > 7:
        reminders.append({
            "type": "hba1c",
            "message": "Your HbA1c is above target range. Schedule a follow-up with your healthcare provider.",
            "urgency": "medium",
            "recommended_action": "Schedule diabetes follow-up",
            "timeframe": "2 weeks",
            "test_name": "HbA1c"
        })
    elif hba1c > 5.7 and hba1c < 6.5:
        reminders.append({
            "type": "hba1c",
            "message": "Your HbA1c indicates prediabetes. Schedule a follow-up check within 3 months.",
            "urgency": "low",
            "recommended_action": "Schedule HbA1c recheck",
            "timeframe": "3 months",
            "test_name": "HbA1c"
        })

    # Check oxygen saturation
    spo2 = health_data.get("SpO2", 0)
    if spo2 < 90 and spo2 > 0:
        reminders.append({
            "type": "oxygen",
            "message": "Your oxygen saturation is critically low. Seek immediate medical attention.",
            "urgency": "high",
            "recommended_action": "Seek immediate medical attention",
            "timeframe": "Immediately",
            "test_name": "Oxygen Saturation"
        })
    elif spo2 < 95 and spo2 >= 90:
        reminders.append({
            "type": "oxygen",
            "message": "Your oxygen saturation is below normal range. Schedule a respiratory assessment.",
            "urgency": "medium",
            "recommended_action": "Schedule respiratory assessment",
            "timeframe": "1 week",
            "test_name": "Oxygen Saturation"
        })

    # Check temperature
    temperature = health_data.get("Temperature", 0)
    if temperature > 38.5:
        reminders.append({
            "type": "temperature",
            "message": "You have a high fever. Seek medical attention if it persists for more than 2 days.",
            "urgency": "medium",
            "recommended_action": "Monitor temperature and seek medical attention if persistent",
            "timeframe": "2 days",
            "test_name": "Body Temperature"
        })

    # Check BMI
    bmi = health_data.get("Weight (BMI)", 0)
    if bmi > 30:
        reminders.append({
            "type": "bmi",
            "message": "Your BMI indicates obesity. Schedule a consultation for weight management.",
            "urgency": "low",
            "recommended_action": "Schedule weight management consultation",
            "timeframe": "1 month",
            "test_name": "BMI"
        })
    elif bmi < 18.5 and bmi > 0:
        reminders.append({
            "type": "bmi",
            "message": "Your BMI is below normal range. Schedule a nutritional assessment.",
            "urgency": "low",
            "recommended_action": "Schedule nutritional assessment",
            "timeframe": "1 month",
            "test_name": "BMI"
        })

    # === KIDNEY FUNCTION ===
    # Check eGFR
    egfr = health_data.get("eGFR", 0)
    if egfr < 15 and egfr > 0:
        reminders.append({
            "type": "kidney",
            "message": "Your kidney function (eGFR) indicates severe kidney disease. Seek immediate nephrology consultation.",
            "urgency": "high",
            "recommended_action": "Seek nephrology consultation",
            "timeframe": "1 week",
            "test_name": "eGFR"
        })
    elif egfr < 30 and egfr >= 15:
        reminders.append({
            "type": "kidney",
            "message": "Your kidney function (eGFR) indicates moderate to severe kidney disease. Schedule a follow-up with a nephrologist.",
            "urgency": "medium",
            "recommended_action": "Schedule nephrology appointment",
            "timeframe": "2 weeks",
            "test_name": "eGFR"
        })
    elif egfr < 60 and egfr >= 30:
        reminders.append({
            "type": "kidney",
            "message": "Your kidney function (eGFR) indicates mild to moderate kidney disease. Schedule a follow-up with a nephrologist.",
            "urgency": "medium",
            "recommended_action": "Schedule nephrology appointment",
            "timeframe": "1 month",
            "test_name": "eGFR"
        })

    # Check BUN
    bun = health_data.get("BUN", 0)
    if bun > 20:
        reminders.append({
            "type": "kidney",
            "message": "Your BUN level is elevated. Schedule a follow-up kidney function test.",
            "urgency": "medium",
            "recommended_action": "Schedule kidney function test",
            "timeframe": "1 month",
            "test_name": "BUN"
        })

    # Check Creatinine
    creatinine = health_data.get("Serum Creatinine", 0)
    if creatinine > 1.2:
        reminders.append({
            "type": "kidney",
            "message": "Your serum creatinine is elevated. Schedule a follow-up kidney function test.",
            "urgency": "medium",
            "recommended_action": "Schedule kidney function test",
            "timeframe": "1 month",
            "test_name": "Serum Creatinine"
        })

    # Check ACR
    acr = health_data.get("ACR", 0)
    if acr > 300:
        reminders.append({
            "type": "kidney",
            "message": "Your albumin-to-creatinine ratio (ACR) indicates severe albuminuria. Schedule a follow-up with a nephrologist.",
            "urgency": "high",
            "recommended_action": "Schedule nephrology appointment",
            "timeframe": "2 weeks",
            "test_name": "ACR"
        })
    elif acr > 30:
        reminders.append({
            "type": "kidney",
            "message": "Your albumin-to-creatinine ratio (ACR) indicates moderate albuminuria. Schedule a follow-up kidney function test.",
            "urgency": "medium",
            "recommended_action": "Schedule kidney function test",
            "timeframe": "1 month",
            "test_name": "ACR"
        })

    # === LIPID PROFILE ===
    # Check Total Cholesterol
    cholesterol = health_data.get("Total Cholesterol", 0)
    if cholesterol > 240:
        reminders.append({
            "type": "cholesterol",
            "message": "Your total cholesterol is significantly elevated. Schedule a follow-up lipid profile and cardiology consultation.",
            "urgency": "medium",
            "recommended_action": "Schedule cardiology consultation",
            "timeframe": "1 month",
            "test_name": "Total Cholesterol"
        })
    elif cholesterol > 200:
        reminders.append({
            "type": "cholesterol",
            "message": "Your total cholesterol is elevated. Schedule a follow-up lipid profile in 3 months.",
            "urgency": "low",
            "recommended_action": "Schedule lipid profile test",
            "timeframe": "3 months",
            "test_name": "Total Cholesterol"
        })

    # Check LDL
    ldl = health_data.get("LDL", 0)
    if ldl > 190:
        reminders.append({
            "type": "cholesterol",
            "message": "Your LDL cholesterol is severely elevated. Schedule a cardiology consultation.",
            "urgency": "medium",
            "recommended_action": "Schedule cardiology consultation",
            "timeframe": "1 month",
            "test_name": "LDL Cholesterol"
        })
    elif ldl > 160:
        reminders.append({
            "type": "cholesterol",
            "message": "Your LDL cholesterol is significantly elevated. Schedule a follow-up lipid profile and consider statin therapy.",
            "urgency": "medium",
            "recommended_action": "Schedule lipid profile follow-up",
            "timeframe": "1 month",
            "test_name": "LDL Cholesterol"
        })
    elif ldl > 130:
        reminders.append({
            "type": "cholesterol",
            "message": "Your LDL cholesterol is elevated. Schedule a follow-up lipid profile in 3 months.",
            "urgency": "low",
            "recommended_action": "Schedule lipid profile test",
            "timeframe": "3 months",
            "test_name": "LDL Cholesterol"
        })

    # Check HDL
    hdl = health_data.get("HDL", 0)
    if hdl < 40 and hdl > 0:
        reminders.append({
            "type": "cholesterol",
            "message": "Your HDL cholesterol is below recommended levels. Schedule a follow-up lipid profile in 3 months.",
            "urgency": "low",
            "recommended_action": "Schedule lipid profile test",
            "timeframe": "3 months",
            "test_name": "HDL Cholesterol"
        })

    # Check Triglycerides
    triglycerides = health_data.get("Triglycerides", 0)
    if triglycerides > 500:
        reminders.append({
            "type": "cholesterol",
            "message": "Your triglyceride levels are severely elevated. Schedule a follow-up lipid profile and consider medication.",
            "urgency": "medium",
            "recommended_action": "Schedule lipid profile follow-up",
            "timeframe": "1 month",
            "test_name": "Triglycerides"
        })
    elif triglycerides > 200:
        reminders.append({
            "type": "cholesterol",
            "message": "Your triglyceride levels are elevated. Schedule a follow-up lipid profile in 3 months.",
            "urgency": "low",
            "recommended_action": "Schedule lipid profile test",
            "timeframe": "3 months",
            "test_name": "Triglycerides"
        })

    # === LUNG FUNCTION ===
    # Check FEV1
    fev1 = health_data.get("FEV1", 0)
    if fev1 < 50 and fev1 > 0:
        reminders.append({
            "type": "lung",
            "message": "Your FEV1 indicates severe lung function impairment. Schedule a pulmonology consultation.",
            "urgency": "high",
            "recommended_action": "Schedule pulmonology consultation",
            "timeframe": "2 weeks",
            "test_name": "FEV1"
        })
    elif fev1 < 80 and fev1 >= 50:
        reminders.append({
            "type": "lung",
            "message": "Your FEV1 indicates moderate lung function impairment. Schedule a follow-up spirometry test.",
            "urgency": "medium",
            "recommended_action": "Schedule spirometry test",
            "timeframe": "1 month",
            "test_name": "FEV1"
        })

    # Check FVC
    fvc = health_data.get("FVC", 0)
    if fvc < 80 and fvc > 0:
        reminders.append({
            "type": "lung",
            "message": "Your FVC is below normal range. Schedule a follow-up spirometry test.",
            "urgency": "medium",
            "recommended_action": "Schedule spirometry test",
            "timeframe": "1 month",
            "test_name": "FVC"
        })

    # Check FEV1/FVC ratio
    fev1_fvc = health_data.get("FEV1/FVC", 0)
    if fev1_fvc < 70 and fev1_fvc > 0:
        reminders.append({
            "type": "lung",
            "message": "Your FEV1/FVC ratio indicates obstructive lung disease. Schedule a pulmonology consultation.",
            "urgency": "medium",
            "recommended_action": "Schedule pulmonology consultation",
            "timeframe": "1 month",
            "test_name": "FEV1/FVC Ratio"
        })

    # Check Peak Flow
    peak_flow = health_data.get("Peak Flow", 0)
    if peak_flow < 50 and peak_flow > 0:
        reminders.append({
            "type": "lung",
            "message": "Your peak flow is severely reduced. Schedule a pulmonology consultation.",
            "urgency": "high",
            "recommended_action": "Schedule pulmonology consultation",
            "timeframe": "2 weeks",
            "test_name": "Peak Flow"
        })
    elif peak_flow < 80 and peak_flow >= 50:
        reminders.append({
            "type": "lung",
            "message": "Your peak flow is moderately reduced. Schedule a follow-up spirometry test.",
            "urgency": "medium",
            "recommended_action": "Schedule spirometry test",
            "timeframe": "1 month",
            "test_name": "Peak Flow"
        })

    # === INFECTIOUS DISEASE TESTS ===
    # Check Malaria
    malaria = health_data.get("Malaria", "")
    if malaria and malaria.lower() == "positive":
        reminders.append({
            "type": "malaria",
            "message": "Your malaria test was positive. Schedule a follow-up test after completing treatment.",
            "urgency": "high",
            "recommended_action": "Schedule follow-up malaria test",
            "timeframe": "2 weeks",
            "test_name": "Malaria Test"
        })

    # Check Widal Test
    widal = health_data.get("Widal Test", "")
    if widal and widal.lower() == "positive":
        reminders.append({
            "type": "widal",
            "message": "Your Widal test was positive. Schedule a follow-up test after completing treatment.",
            "urgency": "medium",
            "recommended_action": "Schedule follow-up Widal test",
            "timeframe": "2 weeks",
            "test_name": "Widal Test"
        })

    # Check Hepatitis B
    hep_b = health_data.get("Hepatitis B", "")
    if hep_b and hep_b.lower() == "positive":
        reminders.append({
            "type": "hepatitis",
            "message": "Your Hepatitis B test was positive. Schedule a follow-up with a hepatologist.",
            "urgency": "high",
            "recommended_action": "Schedule hepatology consultation",
            "timeframe": "2 weeks",
            "test_name": "Hepatitis B Test"
        })

    # Check Typhoid
    typhoid_o = health_data.get("Typhi O", "")
    typhoid_h = health_data.get("Typhi H", "")
    if (typhoid_o and typhoid_o.lower() == "reactive") or (typhoid_h and typhoid_h.lower() == "reactive"):
        reminders.append({
            "type": "typhoid",
            "message": "Your Typhoid test was reactive. Schedule a follow-up test after completing treatment.",
            "urgency": "medium",
            "recommended_action": "Schedule follow-up Typhoid test",
            "timeframe": "2 weeks",
            "test_name": "Typhoid Test"
        })

    # === CHRONIC CONDITIONS ===
    # Check Diabetes tracking
    if "condition_type" in health_data and health_data["condition_type"] == "diabetes":
        if health_data.get("glucose", 0) > 180 or health_data.get("hba1c", 0) > 8:
            reminders.append({
                "type": "diabetes_tracking",
                "message": "Your diabetes tracking shows elevated glucose levels. Schedule a follow-up with your healthcare provider.",
                "urgency": "medium",
                "recommended_action": "Schedule diabetes follow-up",
                "timeframe": "2 weeks",
                "test_name": "Diabetes Tracking"
            })

    # Check Hypertension tracking
    if "condition_type" in health_data and health_data["condition_type"] == "hypertension":
        if health_data.get("systolic", 0) > 140 or health_data.get("diastolic", 0) > 90:
            reminders.append({
                "type": "hypertension_tracking",
                "message": "Your hypertension tracking shows elevated blood pressure. Schedule a follow-up with your healthcare provider.",
                "urgency": "medium",
                "recommended_action": "Schedule hypertension follow-up",
                "timeframe": "2 weeks",
                "test_name": "Hypertension Tracking"
            })

    # Don't add general check-up reminder if no specific issues
    # We only want to show reminders for abnormal values

    return reminders

def create_follow_up_schedule(health_data):
    """Create a comprehensive follow-up schedule based on all health data"""
    schedule = {}

    # === VITAL SIGNS ===
    # Blood pressure follow-up
    systolic = health_data.get("Blood Pressure (Systolic)", 0)
    diastolic = health_data.get("Blood Pressure (Diastolic)", 0)

    if systolic > 180 or diastolic > 120:
        schedule["Blood Pressure Check"] = "Immediate"
    elif systolic > 160 or diastolic > 100:
        schedule["Blood Pressure Check"] = "1 week"
    elif systolic > 140 or diastolic > 90:
        schedule["Blood Pressure Check"] = "2 weeks"
    elif systolic > 120 or diastolic > 80:
        schedule["Blood Pressure Check"] = "1 month"
    else:
        schedule["Blood Pressure Check"] = "6 months"

    # Heart rate follow-up
    heart_rate = health_data.get("ECG (Heart Rate)", 0)
    if heart_rate > 150 or heart_rate < 40:
        schedule["Cardiac Evaluation"] = "1 week"
    elif heart_rate > 100 or (heart_rate < 60 and heart_rate > 0):
        schedule["Cardiac Evaluation"] = "2 weeks"

    # Glucose follow-up
    glucose = health_data.get("Glucose", 0)
    if glucose > 300:
        schedule["Glucose Monitoring"] = "Immediate"
    elif glucose > 200:
        schedule["Glucose Monitoring"] = "1 week"
    elif glucose > 125:
        schedule["Glucose Monitoring"] = "2 weeks"
    elif glucose > 100:
        schedule["Glucose Monitoring"] = "3 months"
    else:
        schedule["Glucose Monitoring"] = "6 months"

    # HbA1c follow-up
    hba1c = health_data.get("HbA1c", 0)
    if hba1c > 9:
        schedule["Diabetes Management"] = "2 weeks"
    elif hba1c > 7:
        schedule["Diabetes Management"] = "1 month"
    elif hba1c > 5.7:
        schedule["HbA1c Recheck"] = "3 months"

    # Oxygen saturation follow-up
    spo2 = health_data.get("SpO2", 0)
    if spo2 < 90 and spo2 > 0:
        schedule["Respiratory Assessment"] = "Immediate"
    elif spo2 < 95 and spo2 >= 90:
        schedule["Respiratory Assessment"] = "1 week"

    # Temperature follow-up
    temperature = health_data.get("Temperature", 0)
    if temperature > 39:
        schedule["Fever Evaluation"] = "Immediate"
    elif temperature > 38:
        schedule["Fever Evaluation"] = "1 day"

    # BMI follow-up
    bmi = health_data.get("Weight (BMI)", 0)
    if bmi > 35:
        schedule["Weight Management"] = "1 month"
    elif bmi > 30:
        schedule["Weight Management"] = "3 months"
    elif bmi < 18.5 and bmi > 0:
        schedule["Nutritional Assessment"] = "1 month"

    # === KIDNEY FUNCTION ===
    # eGFR follow-up
    egfr = health_data.get("eGFR", 0)
    if egfr < 15 and egfr > 0:
        schedule["Nephrology Consultation"] = "1 week"
    elif egfr < 30 and egfr >= 15:
        schedule["Nephrology Consultation"] = "2 weeks"
    elif egfr < 60 and egfr >= 30:
        schedule["Nephrology Consultation"] = "1 month"

    # BUN and Creatinine follow-up
    bun = health_data.get("BUN", 0)
    creatinine = health_data.get("Serum Creatinine", 0)
    if bun > 30 or creatinine > 2:
        schedule["Kidney Function Test"] = "2 weeks"
    elif bun > 20 or creatinine > 1.2:
        schedule["Kidney Function Test"] = "1 month"

    # ACR follow-up
    acr = health_data.get("ACR", 0)
    if acr > 300:
        schedule["Albuminuria Evaluation"] = "2 weeks"
    elif acr > 30:
        schedule["Albuminuria Evaluation"] = "1 month"

    # === LIPID PROFILE ===
    # Total Cholesterol follow-up
    cholesterol = health_data.get("Total Cholesterol", 0)
    ldl = health_data.get("LDL", 0)

    if cholesterol > 300 or ldl > 190:
        schedule["Cardiology Consultation"] = "1 month"
    elif cholesterol > 240 or ldl > 160:
        schedule["Lipid Profile"] = "1 month"
    elif cholesterol > 200 or ldl > 130:
        schedule["Lipid Profile"] = "3 months"
    else:
        schedule["Lipid Profile"] = "12 months"

    # HDL and Triglycerides follow-up
    hdl = health_data.get("HDL", 0)
    triglycerides = health_data.get("Triglycerides", 0)

    if triglycerides > 500:
        schedule["Lipid Management"] = "1 month"
    elif (hdl < 40 and hdl > 0) or triglycerides > 200:
        schedule["Lipid Profile"] = "3 months"

    # === LUNG FUNCTION ===
    # FEV1 follow-up
    fev1 = health_data.get("FEV1", 0)
    if fev1 < 50 and fev1 > 0:
        schedule["Pulmonology Consultation"] = "2 weeks"
    elif fev1 < 80 and fev1 >= 50:
        schedule["Spirometry Test"] = "1 month"

    # FVC follow-up
    fvc = health_data.get("FVC", 0)
    if fvc < 80 and fvc > 0:
        schedule["Spirometry Test"] = "1 month"

    # FEV1/FVC ratio follow-up
    fev1_fvc = health_data.get("FEV1/FVC", 0)
    if fev1_fvc < 70 and fev1_fvc > 0:
        schedule["Pulmonology Consultation"] = "1 month"

    # Peak Flow follow-up
    peak_flow = health_data.get("Peak Flow", 0)
    if peak_flow < 50 and peak_flow > 0:
        schedule["Pulmonology Consultation"] = "2 weeks"
    elif peak_flow < 80 and peak_flow >= 50:
        schedule["Spirometry Test"] = "1 month"

    # === INFECTIOUS DISEASE TESTS ===
    # Malaria follow-up
    malaria = health_data.get("Malaria", "")
    if malaria and malaria.lower() == "positive":
        schedule["Malaria Follow-up Test"] = "2 weeks"

    # Widal Test follow-up
    widal = health_data.get("Widal Test", "")
    if widal and widal.lower() == "positive":
        schedule["Widal Follow-up Test"] = "2 weeks"

    # Hepatitis B follow-up
    hep_b = health_data.get("Hepatitis B", "")
    if hep_b and hep_b.lower() == "positive":
        schedule["Hepatology Consultation"] = "2 weeks"

    # Typhoid follow-up
    typhoid_o = health_data.get("Typhi O", "")
    typhoid_h = health_data.get("Typhi H", "")
    if (typhoid_o and typhoid_o.lower() == "reactive") or (typhoid_h and typhoid_h.lower() == "reactive"):
        schedule["Typhoid Follow-up Test"] = "2 weeks"

    # === CHRONIC CONDITIONS ===
    # Diabetes tracking follow-up
    if "condition_type" in health_data and health_data["condition_type"] == "diabetes":
        if health_data.get("glucose", 0) > 180 or health_data.get("hba1c", 0) > 8:
            schedule["Diabetes Follow-up"] = "2 weeks"
        else:
            schedule["Diabetes Follow-up"] = "3 months"

    # Hypertension tracking follow-up
    if "condition_type" in health_data and health_data["condition_type"] == "hypertension":
        if health_data.get("systolic", 0) > 160 or health_data.get("diastolic", 0) > 100:
            schedule["Hypertension Follow-up"] = "1 week"
        elif health_data.get("systolic", 0) > 140 or health_data.get("diastolic", 0) > 90:
            schedule["Hypertension Follow-up"] = "2 weeks"
        else:
            schedule["Hypertension Follow-up"] = "1 month"

    # General check-up
    schedule["Annual Physical"] = "12 months"

    return schedule

def generate_care_continuity_plan(health_data):
    """Generate a comprehensive care continuity plan based on all health data"""
    care_plan = []

    # === CARDIOVASCULAR HEALTH ===
    # Hypertension management
    systolic = health_data.get("Blood Pressure (Systolic)", 0)
    diastolic = health_data.get("Blood Pressure (Diastolic)", 0)

    if systolic > 180 or diastolic > 120:
        care_plan.append("Seek immediate medical attention for hypertensive crisis")
        care_plan.append("Take your prescribed emergency medications as directed by your doctor")
        care_plan.append("After emergency care, monitor blood pressure daily and maintain a detailed log")
    elif systolic > 140 or diastolic > 90:
        care_plan.append("Monitor blood pressure at home daily and maintain a detailed log")
        care_plan.append("Take all blood pressure medications exactly as prescribed")
        care_plan.append("Follow the DASH diet (rich in fruits, vegetables, and low-fat dairy)")
        care_plan.append("Limit sodium intake to less than 1,500 mg per day")
        care_plan.append("Limit alcohol consumption and avoid tobacco products")
    elif systolic > 120 or diastolic > 80:
        care_plan.append("Monitor blood pressure at home 2-3 times per week")
        care_plan.append("Follow the DASH diet (rich in fruits, vegetables, and low-fat dairy)")
        care_plan.append("Limit sodium intake to less than 2,300 mg per day")
        care_plan.append("Engage in regular aerobic exercise for at least 150 minutes per week")

    # Heart rate management
    heart_rate = health_data.get("ECG (Heart Rate)", 0)
    if heart_rate > 100 or (heart_rate < 60 and heart_rate > 0):
        care_plan.append("Monitor your heart rate daily and maintain a log")
        care_plan.append("Note any symptoms that occur with heart rate changes (dizziness, shortness of breath)")
        care_plan.append("Take all cardiac medications exactly as prescribed")
        care_plan.append("Avoid stimulants such as caffeine if you have tachycardia")

    # Cholesterol management
    cholesterol = health_data.get("Total Cholesterol", 0)
    ldl = health_data.get("LDL", 0)
    hdl = health_data.get("HDL", 0)
    triglycerides = health_data.get("Triglycerides", 0)

    if cholesterol > 240 or ldl > 160 or triglycerides > 500:
        care_plan.append("Follow a strict heart-healthy diet low in saturated fats and trans fats")
        care_plan.append("Take all cholesterol medications exactly as prescribed")
        care_plan.append("Engage in regular aerobic exercise for at least 150 minutes per week")
        care_plan.append("Maintain a food diary to track fat and cholesterol intake")
        care_plan.append("Consider consulting with a registered dietitian for personalized guidance")
    elif cholesterol > 200 or ldl > 130 or (hdl < 40 and hdl > 0) or triglycerides > 200:
        care_plan.append("Follow a heart-healthy diet low in saturated fats and trans fats")
        care_plan.append("Increase intake of omega-3 fatty acids (fish, flaxseeds, walnuts)")
        care_plan.append("Engage in regular aerobic exercise for at least 150 minutes per week")
        care_plan.append("Take cholesterol medications as prescribed")
        care_plan.append("Limit alcohol consumption, which can raise triglyceride levels")

    # === DIABETES MANAGEMENT ===
    # Glucose management
    glucose = health_data.get("Glucose", 0)
    hba1c = health_data.get("HbA1c", 0)

    if glucose > 200 or hba1c > 8:
        care_plan.append("Monitor blood glucose multiple times daily as directed by your healthcare provider")
        care_plan.append("Take all diabetes medications exactly as prescribed")
        care_plan.append("Follow a strict diabetic diet with consistent carbohydrate intake")
        care_plan.append("Work with a certified diabetes educator for personalized guidance")
        care_plan.append("Check feet daily for any signs of injury or infection")
        care_plan.append("Carry fast-acting glucose source at all times for hypoglycemia")
    elif glucose > 125 or hba1c > 6.5:
        care_plan.append("Monitor blood glucose regularly as advised by your healthcare provider")
        care_plan.append("Follow a balanced diet with controlled carbohydrate intake")
        care_plan.append("Engage in regular physical activity (at least 150 minutes per week)")
        care_plan.append("Take diabetes medications as prescribed and at the recommended times")
        care_plan.append("Schedule regular eye exams and foot checks")
    elif glucose > 100 or hba1c > 5.7:
        care_plan.append("Follow a balanced diet with limited refined carbohydrates and added sugars")
        care_plan.append("Engage in regular physical activity (at least 150 minutes per week)")
        care_plan.append("Maintain a healthy weight or work toward weight loss if overweight")
        care_plan.append("Monitor blood glucose periodically as advised by your healthcare provider")

    # === RESPIRATORY HEALTH ===
    # Oxygen saturation management
    spo2 = health_data.get("SpO2", 0)
    if spo2 < 90 and spo2 > 0:
        care_plan.append("Use supplemental oxygen as prescribed by your healthcare provider")
        care_plan.append("Monitor oxygen saturation levels multiple times daily")
        care_plan.append("Avoid high altitudes and air travel without medical clearance")
        care_plan.append("Position yourself upright or slightly elevated when resting")
        care_plan.append("Contact your healthcare provider immediately if levels drop further")
    elif spo2 < 95 and spo2 >= 90:
        care_plan.append("Monitor oxygen saturation levels daily")
        care_plan.append("Use any prescribed inhalers or respiratory medications as directed")
        care_plan.append("Practice breathing exercises as recommended by your healthcare provider")
        care_plan.append("Avoid exposure to smoke, pollution, and respiratory irritants")

    # Lung function management
    fev1 = health_data.get("FEV1", 0)
    fvc = health_data.get("FVC", 0)
    fev1_fvc = health_data.get("FEV1/FVC", 0)
    peak_flow = health_data.get("Peak Flow", 0)

    if (fev1 < 80 and fev1 > 0) or (fvc < 80 and fvc > 0) or (fev1_fvc < 70 and fev1_fvc > 0) or (peak_flow < 80 and peak_flow > 0):
        care_plan.append("Use all prescribed inhalers and respiratory medications as directed")
        care_plan.append("Follow your asthma or COPD action plan")
        care_plan.append("Avoid known triggers such as allergens, smoke, and air pollution")
        care_plan.append("Practice breathing exercises and airway clearance techniques as directed")
        care_plan.append("Get vaccinated against influenza and pneumonia")
        care_plan.append("Monitor peak flow readings regularly if you have a peak flow meter")

    # === KIDNEY HEALTH ===
    # Kidney function management
    egfr = health_data.get("eGFR", 100)
    bun = health_data.get("BUN", 0)
    creatinine = health_data.get("Serum Creatinine", 0)
    acr = health_data.get("ACR", 0)

    if egfr < 30 or bun > 30 or creatinine > 2 or acr > 300:
        care_plan.append("Follow a strict renal diet as prescribed by your nephrologist")
        care_plan.append("Monitor blood pressure closely and keep it under strict control")
        care_plan.append("Limit protein intake as specifically advised by your healthcare provider")
        care_plan.append("Follow fluid intake guidelines from your nephrologist")
        care_plan.append("Avoid NSAIDs and other medications that can affect kidney function")
        care_plan.append("Take all prescribed kidney medications exactly as directed")
        care_plan.append("Monitor for signs of fluid overload (swelling, shortness of breath)")
    elif egfr < 60 or bun > 20 or creatinine > 1.2 or acr > 30:
        care_plan.append("Monitor blood pressure closely and keep it under control")
        care_plan.append("Limit protein intake as advised by your healthcare provider")
        care_plan.append("Stay well-hydrated but follow fluid intake guidelines from your doctor")
        care_plan.append("Avoid NSAIDs and other medications that can affect kidney function")
        care_plan.append("Limit sodium intake to less than 2,300 mg per day")

    # === INFECTIOUS DISEASE MANAGEMENT ===
    # Malaria management
    malaria = health_data.get("Malaria", "")
    if malaria and malaria.lower() == "positive":
        care_plan.append("Complete the full course of antimalarial medication as prescribed")
        care_plan.append("Rest and stay well-hydrated during recovery")
        care_plan.append("Monitor for fever and other symptoms that may indicate treatment failure")
        care_plan.append("Use insecticide-treated bed nets to prevent reinfection")
        care_plan.append("Take preventive measures if you live in or travel to malaria-endemic areas")

    # Typhoid management
    typhoid_o = health_data.get("Typhi O", "")
    typhoid_h = health_data.get("Typhi H", "")
    widal = health_data.get("Widal Test", "")

    if (typhoid_o and typhoid_o.lower() == "reactive") or (typhoid_h and typhoid_h.lower() == "reactive") or (widal and widal.lower() == "positive"):
        care_plan.append("Complete the full course of antibiotics as prescribed")
        care_plan.append("Stay well-hydrated and maintain good nutrition during recovery")
        care_plan.append("Practice strict hand hygiene to prevent transmission to others")
        care_plan.append("Avoid preparing food for others until cleared by your healthcare provider")
        care_plan.append("Follow up with your healthcare provider to ensure complete resolution")

    # Hepatitis B management
    hep_b = health_data.get("Hepatitis B", "")
    if hep_b and hep_b.lower() == "positive":
        care_plan.append("Follow up with a hepatologist for specialized care")
        care_plan.append("Take all prescribed antiviral medications exactly as directed")
        care_plan.append("Avoid alcohol and medications that can damage the liver")
        care_plan.append("Get vaccinated against hepatitis A if not already immune")
        care_plan.append("Inform sexual partners and household members to get tested and vaccinated")
        care_plan.append("Follow a liver-healthy diet as recommended by your healthcare provider")

    # === WEIGHT MANAGEMENT ===
    # BMI management
    bmi = health_data.get("Weight (BMI)", 0)
    if bmi > 30:
        care_plan.append("Work with a healthcare provider to develop a personalized weight management plan")
        care_plan.append("Aim for gradual weight loss of 1-2 pounds per week")
        care_plan.append("Follow a balanced, calorie-controlled diet")
        care_plan.append("Engage in regular physical activity, starting with 150 minutes per week")
        care_plan.append("Consider working with a registered dietitian for personalized nutrition guidance")
        care_plan.append("Monitor weight regularly and track progress")
    elif bmi < 18.5 and bmi > 0:
        care_plan.append("Work with a healthcare provider to identify causes of underweight")
        care_plan.append("Follow a nutrient-dense diet to support healthy weight gain")
        care_plan.append("Consider working with a registered dietitian for personalized nutrition guidance")
        care_plan.append("Engage in strength training exercises to build muscle mass")
        care_plan.append("Monitor weight regularly and track progress")

    # === CHRONIC CONDITION MANAGEMENT ===
    # Diabetes tracking
    if "condition_type" in health_data and health_data["condition_type"] == "diabetes":
        care_plan.append("Continue tracking blood glucose levels as recommended")
        care_plan.append("Maintain a consistent carbohydrate intake at each meal")
        care_plan.append("Take all diabetes medications exactly as prescribed")
        care_plan.append("Inspect feet daily for any signs of injury or infection")
        care_plan.append("Wear medical identification indicating you have diabetes")

    # Hypertension tracking
    if "condition_type" in health_data and health_data["condition_type"] == "hypertension":
        care_plan.append("Continue tracking blood pressure as recommended")
        care_plan.append("Take all hypertension medications exactly as prescribed")
        care_plan.append("Maintain a low-sodium diet (DASH diet)")
        care_plan.append("Limit alcohol consumption and avoid tobacco products")
        care_plan.append("Manage stress through relaxation techniques")

    # === GENERAL WELLNESS ===
    care_plan.append("Maintain a consistent sleep schedule with 7-8 hours of sleep per night")
    care_plan.append("Practice stress management techniques such as meditation or deep breathing")
    care_plan.append("Stay up-to-date with all vaccinations and preventive screenings")
    care_plan.append("Stay well-hydrated by drinking adequate water throughout the day")
    care_plan.append("Maintain social connections and seek support when needed")

    return care_plan

def filter_reminders_by_test_type(reminders, test_type):
    """Filter reminders based on test type"""
    if not test_type:
        return reminders

    # Define which reminder types belong to each test type
    test_type_mapping = {
        "vital_signs": ["blood_pressure", "heart_rate", "oxygen", "temperature", "bmi"],
        "kidney_function": ["kidney", "bun", "creatinine", "egfr", "acr"],
        "lipid_profile": ["cholesterol", "ldl", "hdl", "triglycerides", "cardiovascular"],
        "lung_capacity": ["lung", "fev1", "fvc", "peak_flow", "respiratory"],
        "test_results": ["malaria", "widal", "hepatitis", "typhoid", "infectious"],
        "realtime_health_score": ["blood_pressure", "heart_rate", "glucose", "oxygen", "bmi", "cholesterol"]
    }

    # Get the relevant reminder types for this test type
    relevant_types = test_type_mapping.get(test_type, [])

    # Filter reminders to only include those relevant to the test type
    filtered_reminders = [r for r in reminders if r.get("type", "") in relevant_types]

    return filtered_reminders

def filter_schedule_by_test_type(schedule, test_type):
    """Filter follow-up schedule based on test type"""
    if not test_type:
        return schedule

    # Define which schedule items belong to each test type
    test_type_mapping = {
        "vital_signs": ["Blood Pressure Check", "Heart Rate Monitoring", "Oxygen Saturation Check",
                       "Temperature Check", "Weight Check", "BMI Assessment"],
        "kidney_function": ["Kidney Function Test", "BUN Test", "Creatinine Test", "eGFR Test",
                           "ACR Test", "Nephrology Consultation", "Renal Ultrasound"],
        "lipid_profile": ["Lipid Profile", "Cholesterol Check", "Cardiovascular Assessment",
                         "Cardiology Consultation", "Statin Therapy Review"],
        "lung_capacity": ["Spirometry Test", "Pulmonology Consultation", "Respiratory Assessment",
                         "Peak Flow Monitoring", "Lung Function Test"],
        "test_results": ["Malaria Test", "Widal Test", "Hepatitis Test", "Typhoid Test",
                        "Infectious Disease Follow-up"],
        "realtime_health_score": ["Comprehensive Health Check", "Health Score Assessment"]
    }

    # Get the relevant schedule items for this test type
    relevant_items = test_type_mapping.get(test_type, [])

    # Filter schedule to only include items relevant to the test type
    filtered_schedule = {}
    for item, timeframe in schedule.items():
        # Check if any relevant item is a substring of the schedule item
        if any(relevant in item for relevant in relevant_items):
            filtered_schedule[item] = timeframe

    return filtered_schedule

def filter_care_continuity_by_test_type(care_continuity, test_type):
    """Filter care continuity plan based on test type"""
    if not test_type:
        return care_continuity

    # For now, we'll keep all care continuity items regardless of test type
    # This could be enhanced in the future with more specific filtering
    return care_continuity

def create_reminder_summary(reminders, follow_up_schedule, care_continuity, test_type=None):
    """Create a streamlined summary of reminders, follow-up schedule, and care continuity plan, with optional test-specific context"""
    summary = ""

    # Add test-specific introduction if test_type is provided
    if test_type:
        test_type_name = {
            "vital_signs": "vital signs",
            "kidney_function": "kidney function",
            "lipid_profile": "cardiovascular health",
            "lung_capacity": "respiratory health",
            "test_results": "test results",
            "realtime_health_score": "overall health"
        }.get(test_type, "health")

        if reminders:
            summary += f"Based on your {test_type_name} results, here are your personalized follow-up reminders:\n\n"
        else:
            summary += f"I've reviewed your {test_type_name} results and don't see any specific follow-up reminders needed at this time. Continue with your regular health monitoring.\n\n"
    else:
        if reminders:
            summary += "Based on your health data, here are your personalized follow-up reminders:\n\n"
        else:
            summary += "I've reviewed your health data and don't see any specific follow-up reminders needed at this time. Continue with your regular health monitoring.\n\n"

    # Sort reminders by urgency
    sorted_reminders = sorted(reminders, key=lambda x: {
        "high": 0,
        "medium": 1,
        "low": 2
    }.get(x.get("urgency", "low"), 3))

    # Add urgent reminders first
    urgent_reminders = [r for r in sorted_reminders if r.get("urgency") == "high"]
    if urgent_reminders:
        for reminder in urgent_reminders:
            urgency_icon = "🔴"
            summary += f"{urgency_icon} **{reminder['recommended_action']}** ({reminder['timeframe']}): {reminder['message']}\n\n"

    # Add medium urgency reminders
    medium_reminders = [r for r in sorted_reminders if r.get("urgency") == "medium" and r not in urgent_reminders]
    if medium_reminders:
        for reminder in medium_reminders:
            urgency_icon = "🟡"
            summary += f"{urgency_icon} **{reminder['recommended_action']}** ({reminder['timeframe']}): {reminder['message']}\n\n"

    # Add low urgency reminders
    low_reminders = [r for r in sorted_reminders if r.get("urgency") == "low" and r not in urgent_reminders]
    if low_reminders:
        for reminder in low_reminders:
            urgency_icon = "🟢"
            summary += f"{urgency_icon} **{reminder['recommended_action']}** ({reminder['timeframe']}): {reminder['message']}\n\n"

    # Add follow-up schedule for immediate and short-term follow-ups only
    immediate_followups = []
    short_term_followups = []

    priority_timeframes = ["Immediate", "1 day", "2 days", "3 days", "1 week", "2 weeks"]

    for check, timeframe in follow_up_schedule.items():
        if timeframe == "Immediate":
            immediate_followups.append(f"⚠️ **{check}**: {timeframe}")
        elif timeframe in priority_timeframes:
            short_term_followups.append(f"• **{check}**: {timeframe}")

    if immediate_followups or short_term_followups:
        if summary:
            summary += "\n"

        if immediate_followups:
            for followup in immediate_followups:
                summary += f"{followup}\n"

            if short_term_followups:
                summary += "\n"

        if short_term_followups:
            for followup in short_term_followups:
                summary += f"{followup}\n"

    # Add care continuity recommendations specific to abnormal values
    specific_care_items = []

    # Match care plan items to abnormal values
    for reminder in sorted_reminders:
        test_type = reminder.get("type", "").lower()
        test_name = reminder.get("test_name", "").lower()

        # Find relevant care items
        relevant_items = []
        for item in care_continuity:
            item_lower = item.lower()

            # Check if the care item is relevant to this test
            if (test_type and test_type in item_lower) or (test_name and test_name in item_lower):
                relevant_items.append(item)

        # Add up to 2 most relevant care items per abnormal value
        for item in relevant_items[:2]:
            if item not in specific_care_items:
                specific_care_items.append(item)

    # Add the specific care items
    if specific_care_items:
        if summary:
            summary += "\n"

        for item in specific_care_items[:10]:  # Limit to top 10 most relevant items
            summary += f"• {item}\n"

    # Add closing note
    if summary:
        summary += "\nPlease consult with your healthcare provider before making any changes to your treatment plan."
    else:
        summary = "No follow-up reminders needed at this time. All your test results are within normal ranges."

    return summary
