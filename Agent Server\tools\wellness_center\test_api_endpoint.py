"""
Test script for the Lab Insights Dashboard API endpoint

This script tests the wellness center API endpoint by making HTTP requests.
"""

import requests
import json
import time

def test_lab_insights_api():
    """Test the Lab Insights Dashboard API endpoint"""
    
    base_url = "http://localhost:8002"
    endpoint = "/wellness-center/lab-insights-dashboard"
    url = f"{base_url}{endpoint}"
    
    print("🔬 Testing Lab Insights Dashboard API Endpoint")
    print("=" * 60)
    print(f"🌐 URL: {url}")
    
    # Test payload
    payload = {
        "admin_id": "admin_doctor_001",
        "date_range_days": 30,
        "test_type_filter": None,
        "user_group_filter": None
    }
    
    print(f"📋 Request Payload:")
    print(json.dumps(payload, indent=2))
    print()
    
    try:
        print("🚀 Sending request...")
        response = requests.post(url, json=payload, timeout=30)
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Request successful!")
            
            # Parse response
            result = response.json()
            
            if result.get("success"):
                dashboard_data = result["dashboard_data"]
                
                print("\n📈 Dashboard Summary:")
                print(f"• Total test requests: {dashboard_data['summary_statistics']['total_test_requests']}")
                print(f"• Unique tests: {dashboard_data['summary_statistics']['unique_tests_requested']}")
                print(f"• Active users: {dashboard_data['summary_statistics']['active_users']}")
                print(f"• Average turnaround: {dashboard_data['summary_statistics']['average_turnaround_hours']} hours")
                
                print("\n🏆 Top Requested Tests:")
                for i, test in enumerate(dashboard_data['top_requested_tests'][:3], 1):
                    print(f"{i}. {test['test_name']}: {test['request_count']} requests ({test['percentage_of_total']}%)")
                
                print("\n🌍 Geographic Distribution:")
                for geo in dashboard_data['geographic_distribution'][:3]:
                    print(f"• {geo['location']}: {geo['test_count']} tests ({geo['percentage_of_total']}%)")
                
                print("\n💡 Key Insights:")
                for insight in dashboard_data['insights_and_recommendations'][:3]:
                    print(f"• {insight}")
                
            else:
                print(f"❌ Dashboard generation failed: {result.get('error', 'Unknown error')}")
                
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Could not connect to the server")
        print("💡 Make sure the agent server is running on localhost:8002")
        print("   Run: python agent_server.py")
        
    except requests.exceptions.Timeout:
        print("❌ Timeout Error: Request took too long")
        
    except Exception as e:
        print(f"❌ Unexpected Error: {str(e)}")
    
    print("\n" + "=" * 60)

def test_filtered_request():
    """Test the API with filters"""
    
    base_url = "http://localhost:8002"
    endpoint = "/wellness-center/lab-insights-dashboard"
    url = f"{base_url}{endpoint}"
    
    print("\n🔍 Testing Filtered Dashboard Request")
    print("=" * 60)
    
    # Test with filter
    payload = {
        "admin_id": "admin_doctor_002",
        "date_range_days": 7,
        "test_type_filter": "health_score",
        "user_group_filter": None
    }
    
    print(f"📋 Filtered Request Payload:")
    print(json.dumps(payload, indent=2))
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                dashboard_data = result["dashboard_data"]
                print(f"✅ Filtered dashboard generated successfully!")
                print(f"📊 Filtered test requests: {dashboard_data['summary_statistics']['total_test_requests']}")
                print(f"🔍 Filter applied: {dashboard_data['filters_applied']['test_type']}")
            else:
                print(f"❌ Error: {result.get('error')}")
        else:
            print(f"❌ Request failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def check_server_status():
    """Check if the server is running"""
    
    try:
        response = requests.get("http://localhost:8002/docs", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running and accessible")
            return True
        else:
            print(f"⚠️ Server responded with status {response.status_code}")
            return False
    except:
        print("❌ Server is not running or not accessible")
        print("💡 Start the server with: python agent_server.py")
        return False

if __name__ == "__main__":
    print("🏥 Wellness Center API Testing")
    print("=" * 60)
    
    # Check server status first
    if check_server_status():
        # Run tests
        test_lab_insights_api()
        test_filtered_request()
    else:
        print("\n🚨 Cannot run tests - server is not accessible")
        print("\n📝 To start the server:")
        print("1. Navigate to the Agent Server directory")
        print("2. Run: python agent_server.py")
        print("3. Wait for the server to start")
        print("4. Run this test script again")
    
    print("\n🎉 API testing completed!")
